package jp.trial.DCOrderConsignment.service.impl;

import java.util.ArrayList;
import java.util.List;

import jp.trial.DCOrderConsignment.dao.impl.MenuMainDaoImpl;
import jp.trial.DCOrderConsignment.model.LoginUserInfoModel;
import jp.trial.DCOrderConsignment.model.OrderCaseInfoModel;
import jp.trial.DCOrderConsignment.model.SidebarMenuModel;
import jp.trial.DCOrderConsignment.service.MenuMainService;

import com.alibaba.fastjson.JSON;

public class MenuMainServiceImpl implements MenuMainService{

	private MenuMainDaoImpl menuMainDao=new MenuMainDaoImpl();

	/** メニューリスト取得*/
	public String getSidebarMenuSelList(String userCD,String employeeCD) throws Exception {
		String res = "";
		List<Object> objList = new ArrayList<>();
		try{
//			objList = menuMainDao.getSidebarMenuSelList(userCD,employeeCD);
			List<SidebarMenuModel> listMenu = new ArrayList<>();
			SidebarMenuModel menu1 = new SidebarMenuModel("1704", "権限設定画面", "DBMainteAuthorityPage", "icon-wrench");
			SidebarMenuModel menu2 = new SidebarMenuModel("1705", "コンサイメント推奨画面", "DCOrderConPage", "icon-briefcase");
			SidebarMenuModel menu3 = new SidebarMenuModel("1707", "展開センター設定画面", "DBMainteVenShousePage", "icon-briefcase");
			SidebarMenuModel menu4 = new SidebarMenuModel("1708", "商品登録画面", "DBMainteSupportPage", "icon-briefcase");
			SidebarMenuModel menu5 = new SidebarMenuModel("1709", "センター店舗配送画面", "DBMainteAreaPage", "icon-briefcase");
			SidebarMenuModel menu6 = new SidebarMenuModel("1710", "単日ロット制限画面", "DBMainteOrderSplitPage", "icon-briefcase");
			SidebarMenuModel menu7 = new SidebarMenuModel("1711", "納品不可設定画面", "DBMainteNotDeliveryPage", "icon-briefcase");
			SidebarMenuModel menu8 = new SidebarMenuModel("1712", "イベント設定画面", "DBMainteEventPage", "icon-briefcase");
			SidebarMenuModel menu9 = new SidebarMenuModel("1713", "商談改廃データ提供", "DBMainteBizTalksPage", "icon-briefcase");
			listMenu.add(menu1);
			listMenu.add(menu2);
			if (userCD != null && !userCD.isEmpty()) {
				SidebarMenuModel menu22 = new SidebarMenuModel("1706", "発注支援ツール", "DBOrderSupportPage", "icon-briefcase");
				listMenu.add(menu22);
			}
			listMenu.add(menu3);
			listMenu.add(menu4);
			listMenu.add(menu5);
			listMenu.add(menu6);
			listMenu.add(menu7);
			listMenu.add(menu8);
			listMenu.add(menu9);
			objList.add(listMenu);
			res = JSON.toJSONString(objList);
		}catch(Exception e){
			throw new Exception(e.getMessage());
		}
		return res;
	}

	/** 推奨案情報取得*/
	public String getOrderCaseInfoData(String userCD,String employeeCD) throws Exception {

		String res = "";
		List<Object> objList = new ArrayList<>();
		try{
//			objList = menuMainDao.getOrderCaseInfoData(userCD,employeeCD);
			List<OrderCaseInfoModel> result = new ArrayList<>();
			OrderCaseInfoModel orderCaseInfoModel1 = new OrderCaseInfoModel();
			orderCaseInfoModel1.setOrderCaseInfo("OrderCaseInfo");
			result.add(orderCaseInfoModel1);
			objList.add(result);

			res = JSON.toJSONString(objList);
		}catch(Exception e){
			throw new Exception(e.getMessage());
		}
		return res;
	}

	//ユーザー名取得
	public String getLoginInfo(String venderCD,String employeeCD) throws Exception{
		String res = "";
		List<Object> objList = new ArrayList<>();
		try{
//			objList = menuMainDao.getLoginInfo(venderCD,employeeCD);
			LoginUserInfoModel loginUserInfoModel = new LoginUserInfoModel();
			if (venderCD != null && !venderCD.isEmpty()) {
				loginUserInfoModel.setSupplierCD("1");
				loginUserInfoModel.setSupplierName("TestSupplier");
				loginUserInfoModel.setEmployeeCode(null);
				loginUserInfoModel.setEmployeeName(null);
			}
			if (employeeCD != null && !employeeCD.isEmpty()) {
				loginUserInfoModel.setSupplierCD(null);
				loginUserInfoModel.setSupplierName(null);
				loginUserInfoModel.setEmployeeCode("1");
				loginUserInfoModel.setEmployeeName("TestEmployee");
			}
			List<LoginUserInfoModel> result = new ArrayList<>();
			result.add(loginUserInfoModel);
			objList.add(result);

			res = JSON.toJSONString(objList);
		}catch(Exception e){
			throw new Exception(e.getMessage());
		}
		return res;
	};
}
