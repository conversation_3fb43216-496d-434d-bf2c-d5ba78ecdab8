var tableSelArr = [];

/** DataGrid Creat*/
var TemplateGrid = {
		tbhot:null,
		colWidths:[60,200,110,250,140,140,80],
		columns: [
            {data: 'chkFlg',    	type: 'checkbox',    checkedTemplate: '1', uncheckedTemplate: '0'},//全選 
            {data: 'dbArea',	    type: 'text',     	 readOnly:true},//DBエリア
            {data: 'jan',			type: 'text',        readOnly:false},//JAN
            {data: 'productName',	type: 'text',     	 readOnly:true},//商品名
            {data: 'specName',		type: 'text',        readOnly:true},//規格
            {data: 'brandName',		type: 'text',        readOnly:true},//ブランド
            {
            	data: 'branch',		
            	type: 'text',        
            	readOnly:true
            }//店舗
		],
		isChecked: function(){
			if(!this.tbhot){
				return false;
			}
			if("afterChg"==chk){
				var sourceData = TemplateGrid.tbhot.getSourceData();
				for (var i=0; i<sourceData.length-1; i++) {
					if(sourceData[i].chkFlg!="1"){						
						return false;
					}
				}
				return true;
			}else{
				return chk;
			}
		},
		//create grid
		CreatHotGrid: function(hotgrid,arrData){
			//まず対象内容クリア
			if(this.tbhot){
				this.tbhot.destroy();
				chk=false;
			}
			this.tbhot = new Handsontable(document.getElementById(hotgrid),{
				height:$(".page-content").height()-150,
				rowHeaders: false,
				colHeaders: function(col){
								switch(col){
								case 0:
									var txt = '<label class="checkbox-inline"><input id="chkAll" onclick="chkAll();" type="checkbox"';
										txt += TemplateGrid.isChecked() ? 'checked="checked"' : '';
										txt += '/>全選</label>';
									return txt;
								case 1: return 'エリア';	
								case 2: return 'JAN';
								case 3: return '商品名';
								case 4: return '規格';
								case 5: return 'ブランド';
								case 6: return '店舗';
								}
							},
				colWidths: this.colWidths,
				columns: this.columns,
				overflow: scroll,
				autoColumnSize:true,
				fillHandle:true,
				manualRowResize: true,  
			    manualColumnResize: true,  
			    wordWrap:false,
			    search: true,
			    minSpareRows:1,
			    columnSorting: true,
			    sortIndicator: true,
			    manualColumnResize: true,
				currentRowClassName: 'currentRow',
				cells: function (row, col, prop) {
					var cellProperties = {};
					cellProperties.renderer = styleRender;
					return cellProperties;					
		    	},
		    	beforeChange : function(changes, source){
		    		if(changes!=null){  
						for(var ii=0;ii<changes.length;ii++){
							var index = changes[ii][0];//行番号
		                    var colHead = changes[ii][1];//列番号
		                    var oldvalue = changes[ii][2];
		                    var newvalue = changes[ii][3];
		                    if(typeof(oldvalue)=="undefined"&&(newvalue==""||newvalue==null)){  	
		                    }else{
		                    	if(typeof(RegistryGlobal.showList[index].chkFlg)=="undefined"){
			                    	RegistryGlobal.showList[index].chkFlg = "0";
			                    }
		                    }
						}
	                }
		    	},
		    	afterChange: function(changes, source) {
		    		if (source === 'loadData'||source ==='external') {
		    	        return; //don't save this change
		    	    }
                    if(changes!=null){  
                    	for(var ii=0;ii<changes.length;ii++){
                    		var index = changes[ii][0];//行番号
    	                    var colHead = changes[ii][1];//列番号
    	                    var oldvalue = changes[ii][2];
    	                    var newvalue = changes[ii][3];
    	                    var sourceData = TemplateGrid.tbhot.getSourceData();
    	        			var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', index);
    	                    if(typeof(colHead)=="undefined"
    	                    	||((typeof(oldvalue)=="undefined"||oldvalue==""|| oldvalue==null)&&(typeof(newvalue)=="undefined"||newvalue==""|| newvalue==null))
    	                        || newvalue==oldvalue) {
    	                    }else{
	    	        			if("chkFlg"==colHead){
	    	        				chk = "afterChg";
	    	        				if("0"==newvalue||"1"==newvalue){
	    	        					sourceData[realRow].chkFlg=newvalue;
	    	        				}else{
	    	        					TemplateGrid.tbhot.setDataAtRowProp(index,"chkFlg",oldvalue);
	    	        				}
	    	        			}else{
	    	        				if(colHead=="jan" && newvalue!=null)
	    	        				{	
	    	        					newvalue = studioFilter(newvalue);
	    		        				
	    		        				if(newvalue=="") {
	    		        					sourceData[index] = {};
	    		        					TemplateGrid.tbhot.loadData(RegistryGlobal.showList);
	    		        					return false;
	    		        				}
	    				                sourceData[index][colHead] = newvalue;
	    				                sourceData[index].chkFlg = "0";
	    	        				}
	    	        			}
	
	                        	if(colHead == "jan"){
	    	                    	getInputData(index);
	    	                    }
    	                    }
                        }
                    	TemplateGrid.tbhot.render();
                    }
	 			},
	 			afterSelectionByProp: function(r, p, r2, p2) {
	 			}
			});	
			chk=false;
			this.tbhot.loadData(arrData);
		    this.tbhot.render();
		}		
}
var studioFilter = function(data) {
	data = numLen(data, 13);
	return data;
}
var numLen = function(val, len) {
	val = val.replace(/[^0-9]/ig, "");
	val = val.substring(0, len);

	return val;
}
//JANで情報を獲得
var getInputData = function(row){
	//JAN
	var val5 = TemplateGrid.tbhot.getDataAtCell(row, 2);
	
	if(val5!=null&&val5!=""&&val5!="null"){
		
		$("#loadMask").show();
		$.ajax({
			url:'/dbmastermainteservice/services/AreaPageControl/getGridDataList',
			type:'POST',
			data:{
				userCD:RegistryGlobal.userCD,
				venderCD:RegistryGlobal.venderCD,
				flg:'2',
				jan:val5
			},
			success:function(response){
				$("#loadMask").hide();
				if(response =="false"){
					showMsgBox(Message.msg043,['データ取得'],function(){});
		    	}else{
		    		if(response == "[[]]"){
		    			showMsgBox(Message.msg055,[],function(){
        					RegistryGlobal.showList[row] = {};
        					TemplateGrid.tbhot.loadData(RegistryGlobal.showList);
		    			});
		    		}else{
		    			var result = JSON.parse(response);	
			    		var rstJson = result[0][0];

			    		RegistryGlobal.showList[row] = rstJson;
			    		RegistryGlobal.showList[row].chkFlg = "0";
			    		TemplateGrid.tbhot.loadData(RegistryGlobal.showList);
		    		}
		    	}
			},
			error:function(){
				$("#loadMask").hide();
				showMsgBox(Message.msg043,['データ取得'],function(){});
			}
		});
	}else{
		return false;
	}
}
var setting = {
		check: {
			enable: true
		},
		data: {
			simpleData: {
				enable: true
			}
		}
	};
var BtnTreeWindow = function(DBAreaBranchList){
	$.fn.zTree.init($("#branchDialog"), setting, RegistryGlobal.branchList);
	zTree = $.fn.zTree.getZTreeObj("branchDialog");
	var type = { "Y":"ps", "N":"ps"};
	zTree.setting.check.chkboxType = type;
	if(DBAreaBranchList){
		DBAreaBranchList.forEach(function(item, index){
			zTree.checkNode(zTree.getNodeByParam("id", item), true, true);
		});
	}
	$("#branchDivDialog").modal("show");
}
var chk;
var chkAll = function() {
	if (chk == true) {
		chk = false;
	} else {
		chk = true;
	}
	if (TemplateGrid.tbhot.getSourceData().length > 0) {
		$.each(RegistryGlobal.showList, function(i, data) {
			if (data.chkFlg != null)
				data.chkFlg = chk ? "1" : "0";
		});
	}
	TemplateGrid.tbhot.render();
}

var styleRender = function(instance, td, row, col, prop, value, cellProperties) {
	Handsontable.renderers.TextRenderer.apply(this, arguments);
	var color;
	var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', row);
	if ("chkFlg" == prop) {// checkbox
		Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
		td.style.textAlign = "center";
	}
	if ("jan" == prop) {// JAN
		td.style.background = '#FFFF99';
	}
	if ("branch" == prop) {// TEN
		if (value != null)
			td.innerHTML = "<a href='#' target='' onclick=detailShow(" + row
					+ ") style='cursor:pointer;'>詳細</a>";
		td.style.textAlign = "center";
	}
	if ("branch" != prop && "chkFlg" != prop) {
		td.title = value;
	}

	return td;
}
//店舗リストを取得
var detailShow = function(row){
	//JAN
	var val5 = TemplateGrid.tbhot.getDataAtCell(row, 2);
	
	$("#loadMask").show();
	$.ajax({
		url:'/dbmastermainteservice/services/AreaPageControl/getTenListByJan',
		type:'POST',
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:RegistryGlobal.venderCD,
			jan:val5
		},
		success:function(response){
			$("#loadMask").hide();
			if(response =="false"){
				showMsgBox(Message.msg043,['データ取得'],function(){});
	    	}else{
	    		var result = JSON.parse(response);
	    		var rstArr = result[0];
	    		
	    		var rstSelList = [];
	    		for(var i=0;i<rstArr.length;i++){
	    			rstSelList.push(rstArr[i]["branchCD"]);
	    		}
	    		
	    		branchTreeSearch(row,val5,rstSelList);
	    	}
		},
		error:function(){
			$("#loadMask").hide();
			showMsgBox(Message.msg043,['データ取得'],function(){});
		}
	});

}
//店舗Tree検索
var branchTreeSearch = function(row,val5,rstSelList){
	$("#loadMask").show();
	$.ajax({
		url:SystemManage.address+'AreaPageControl/getTenListAll',
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:RegistryGlobal.venderCD,
			jan:val5
		},
		type:'POST',
		success:function(response){
			$("#loadMask").hide();
			if("false"==response){
				showMsgBox(Message.msg027,[],function(){});//店舗Tree検索失敗しました。
			}else{				
				RegistryGlobal.r = row;
				RegistryGlobal.branchList = JSON.parse(response);
				BtnTreeWindow(rstSelList);
			}
		},
		error:function(response, textStatus){
			$("#loadMask").hide();
			showMsgBox(Message.msg027,[],function(){});//店舗Tree検索失敗しました。
		}
	});
}