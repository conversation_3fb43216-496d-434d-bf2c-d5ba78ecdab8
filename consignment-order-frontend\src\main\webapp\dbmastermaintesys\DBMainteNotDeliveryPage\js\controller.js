/**
 * @description: DBマスタメンテナンス-納品不可設定画面
 * @author: 10047496 zhangyunfeng
 * @date: 2018/9/20
 */
var RegistryGlobal = {
	userCD: sessionStorage.getItem("userCD"),
	divMixList:[],//混載
	divCenterList:[],//センター
	divSupplierList:[],
	CreateDate: new Date().toISOString(),//将日期格式转化为字符型
	tableData: [],
	tableSourceData: [],
	tableDataAll: [],
	tableSourceDataAll: [],
	showAllList:[],
	showCenterList:[],
	init: true
}

$(document).ready(function () {
	$("#loadMask").show();
	$(document).ajaxStart(function(){
		$("#loadMask").show();
    }).ajaxStop(function(){
    	$("#loadMask").hide();
    });


	//INIT
	selMultiLoadInit();
	//ベンダー用画面Grid表示する
	getInitDataList();
	
	//Grid　初期データロード
	TemplateGridAll.CreatHotGrid('gridAllInfo',RegistryGlobal.showAllList);
	
	//検索
	$("#btn_search").on("click",function(){
		tableData_search();
	});
	//削除
	$("#btn_Delete").on("click",function(){
		tableData_delete();
	});
	//保存
	$("#btn_save").on("click",function(){
		tableData_Save();
	});
	
	$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
		if(e.target.id=="centerTb"){
			//Grid　初期データロード
			if(RegistryGlobal.init){
			    TemplateGridCenter.CreatHotGrid('gridCenterInfo',RegistryGlobal.showCenterList);
			}
			RegistryGlobal.init =false;
			$('#divMixMulti').multiselect("disable");
			$('#divSupplierMulti').multiselect("disable");
		}else{
			$('#divMixMulti').multiselect("enable");
			if(RegistryGlobal.userCD){
				$('#divSupplierMulti').multiselect("disable");
			}else{
			    $('#divSupplierMulti').multiselect("enable");
			}
		}
    });
});

var selMultiLoadInit=function(){
	//混載
	$('#divMixMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全混載',
		nonSelectedText: '全て',
		nSelectedText: ' 混載',
		disableIfEmpty: true,
	    disabledText: '全て',
		numberDisplayed:1
	});	
	
	$('#divCenterMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全センター',
		nonSelectedText: '全て',
		nSelectedText: ' センター',
		numberDisplayed:1
	});
	
	$('#divSupplierMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全ベンダー',
		nonSelectedText: '全て',
		nSelectedText: ' ベンダー',
		disableIfEmpty: true,
	    disabledText: '全て',
		numberDisplayed:1
	});
}

var getInitDataList=function(){
	$.ajax({
		url:`${SystemManage.shiniseAddress}NotDeliveryController/getInitList`,//getCenterList',
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		type:'POST',
		data:{userCD:sessionStorage.getItem("employeeCD"),//社員CD
	        venderCD:sessionStorage.getItem("userCD")//ベンダーCD
	    },
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg001,[],function(){});//初期化失敗しました。
			}else{				
				//var result = eval('('+response+')');
				var result =JSON.parse(response);
					RegistryGlobal.divMixList = result[0].MixList;
				RegistryGlobal.divCenterList = result[0].CenterList;
				RegistryGlobal.divSupplierList = result[0].SupplierList;
				//混載
				bindData2(RegistryGlobal.divMixList,"mixCD","mixCD","divMixMulti","");
				//センター
				bindData(RegistryGlobal.divCenterList,"centerCD","centerName","divCenterMulti","");
				//ベンダー
				bindData(RegistryGlobal.divSupplierList,"supplierCD","supplierName","divSupplierMulti","");
				
				//ベンダー選択不可用
				if(RegistryGlobal.userCD){
					$('#divSupplierMulti').multiselect("disable");
				}
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg001,[],function(){});//初期化失敗しました。
		}
	});
}

//検索
var tableData_search = function(){
	var mixStr;//DIV
	var centerStr;//センター
	var supplierStr;//ベンダー
	mixStr = $("#divMixMulti").val();
	centerStr = $("#divCenterMulti").val();
	if (!$("#divSupplierMulti").val()) {
		supplierStr = sessionStorage.getItem("userCD");//ベンダーCD
	} else {
		supplierStr = $("#divSupplierMulti").val();
		if(supplierStr){
			supplierStr=supplierStr.join(",");
		}
	}
	if(mixStr){
		mixStr=mixStr.join(",");
	}
	if(centerStr){
		centerStr=centerStr.join(",");
	}
	var tabId=$('.tab-pane.fade.active.in').attr("id");
	$.ajax({
		url:`${SystemManage.shiniseAddress}NotDeliveryController/getSearchInfo`,
		type:'POST',
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		data:{mixStr : mixStr, centerStr: centerStr, supplierStr : supplierStr,flg:tabId=="tbAllGrid"?0:1},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
			}else{				
				var result = JSON.parse(response);
				if(tabId=="tbAllGrid"){
					RegistryGlobal.showAllList = result;
					TemplateGridAll.CreatHotGrid('gridAllInfo',RegistryGlobal.showAllList);			
				}else{
					RegistryGlobal.showCenterList = result;
					TemplateGridCenter.CreatHotGrid('gridCenterInfo',RegistryGlobal.showCenterList);
				}
				RegistryGlobal.CreateDate = new Date().toISOString();
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
		}
	});
}

//削除
var tableData_delete = function(){
	var sendData = "";
	var cnt = 0;
	chk = "";
	var tabId=$('.tab-pane.fade.active.in').attr("id");
	if(tabId=="tbAllGrid"){
		var tableData = TemplateGridAll.tbhot1.getData();
		if(tableData.length==0){
			showMsgBox(Message.msg003,[],function(){});//対象データがありません。
			return;
		}
		for(var i=0;i<tableData.length;i++){
			if("1"==tableData[i][1]){
				cnt++;
				sendData += (tableData[i][9]?tableData[i][9]:"")+",";
				sendData += tableData[i][2].split("-")[0]+",";
				sendData += tableData[i][3].split("-")[0]+",";
				sendData += tableData[i][4].split("-")[0]+",";
				sendData += ",";
				sendData += ",";
				sendData += ",";
				sendData += (tableData[i][9]?tableData[i][9]:"")+";";
			}
		}
		if(0==cnt){
			showMsgBox(Message.msg004,[],function(){});//チェックされたデータがありません。
			return;
		}
	}else{
		var tableData = TemplateGridCenter.tbhot2.getData();
		if(tableData.length==0){
			showMsgBox(Message.msg003,[],function(){});//対象データがありません。
			return;
		}
		for(var i=0;i<tableData.length;i++){
			if("1"==tableData[i][1]){
				cnt++;
				sendData += (tableData[i][7]?tableData[i][7]:"")+",";
				sendData += tableData[i][2].split("-")[0]+",";
				sendData += ",";
				sendData += ",";
				sendData += ";";
				
			}
		}
		if(0==cnt){
			showMsgBox(Message.msg004,[],function(){});//チェックされたデータがありません。
			return;
		}
	}
	
	$.ajax({
		url:`${SystemManage.shiniseAddress}NotDeliveryController/deleteInfo`,
		type:'POST',
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		data:{
			sendData: sendData,
			userCD:sessionStorage.getItem("employeeCD"),//社員CD
	        venderCD:sessionStorage.getItem("userCD"),//ベンダーCD
	        flg:tabId=="tbAllGrid"?0:1
	    },
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg037,[],function(){});//削除失敗しました。
			}else{
				tableData_search();
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg037,[],function(){});//削除失敗しました。
		}
	});
}
//保存
var tableData_Save = function(){
	var sendData = "";
	var cnt = 0;
	chk = "";
	var tabId=$('.tab-pane.fade.active.in').attr("id");
	if(tabId=="tbAllGrid"){
		var tableData = TemplateGridAll.tbhot1.getData();
		if(tableData.length==0){
			showMsgBox(Message.msg003,[],function(){});//対象データがありません。
			return;
		}
		for(var i=0;i<tableData.length;i++){
			if("1"==tableData[i][1]){
				cnt++;
				if((!tableData[i][6]||tableData[i][6]=="") && (!tableData[i][7]||tableData[i][7]=="") && (!tableData[i][8]||tableData[i][8]=="")){
					showMsgBox(Message.msg024,[],function(){TemplateGridAll.tbhot1.selectCellByProp(i,"notDeliveryWeekDay");});
					return;
				}
				if(tableData[i][7] && !isDate(tableData[i][7])){
					showMsgBox(Message.msg018,["納品不可開始日"],function(){TemplateGridAll.tbhot1.selectCellByProp(i,"notDeliveryBeginDate");});//$1の日付入力が不正です。
					return;
				}
				if(tableData[i][8] && !isDate(tableData[i][8])){
					showMsgBox(Message.msg018,["納品不可終了日"],function(){TemplateGridAll.tbhot1.selectCellByProp(i,"notDeliveryEndDate");});//$1の日付入力が不正です。
					return;
				}
				if(tableData[i][8] && (!tableData[i][7]||tableData[i][7]=="")){
					showMsgBox(Message.msg026,["納品不可開始日"],function(){TemplateGridAll.tbhot1.selectCellByProp(i,"notDeliveryBeginDate");});//$1を入力してください
					return;
				}
				if(tableData[i][7] && (!tableData[i][8]||tableData[i][8]=="")){
					showMsgBox(Message.msg026,["納品不可終了日"],function(){TemplateGridAll.tbhot1.selectCellByProp(i,"notDeliveryEndDate");});//$1を入力してください
					return;
				}
				if(tableData[i][7] &&tableData[i][8]&& new Date(new Date(tableData[i][7]))>new Date(new Date(tableData[i][8]))){
					showMsgBox(Message.msg025,[],function(){TemplateGridAll.tbhot1.selectCellByProp(i,"notDeliveryBeginDate");});//当日より過去の納品日は入力できません。
					return;
				}
				
				sendData += (tableData[i][9]?tableData[i][9]:"")+",";
				sendData += tableData[i][2].split("-")[0]+",";
				sendData += tableData[i][3].split("-")[0]+",";
				sendData += tableData[i][4].split("-")[0]+",";
				sendData += (tableData[i][6]?tableData[i][6].replace(/,/g, "-"):"")+",";
				sendData += (tableData[i][7]?tableData[i][7]:"")+",";
				sendData += (tableData[i][8]?tableData[i][8]:"")+",";
				sendData += (tableData[i][10]?tableData[i][10]:"")+";";
			}
		}
		if(0==cnt){
			showMsgBox(Message.msg004,[],function(){});//チェックされたデータがありません。
			return;
		}
	}else{
		var tableData = TemplateGridCenter.tbhot2.getData();
		if(tableData.length==0){
			showMsgBox(Message.msg003,[],function(){});//対象データがありません。
			return;
		}
		for(var i=0;i<tableData.length;i++){
			if("1"==tableData[i][1]){
				cnt++;
				if((!tableData[i][4]||tableData[i][4]=="") && (!tableData[i][5]||tableData[i][5]=="") && (!tableData[i][6]||tableData[i][6]=="")){
					showMsgBox(Message.msg024,[],function(){TemplateGridCenter.tbhot2.selectCellByProp(i,"notDeliveryWeekDay");});
					return;
				}
				if(tableData[i][5] && !isDate(tableData[i][5])){
					showMsgBox(Message.msg018,["納品不可開始日"],function(){TemplateGridCenter.tbhot2.selectCellByProp(i,"notDeliveryBeginDate");});//$1の日付入力が不正です。
					return;
				}
				if(tableData[i][6] && !isDate(tableData[i][6])){
					showMsgBox(Message.msg018,["納品不可終了日"],function(){TemplateGridCenter.tbhot2.selectCellByProp(i,"notDeliveryEndDate");});//$1の日付入力が不正です。
					return;
				}
				if(tableData[i][6] && (!tableData[i][5]||tableData[i][5]=="")){
					showMsgBox(Message.msg026,["納品不可開始日"],function(){TemplateGridCenter.tbhot2.selectCellByProp(i,"notDeliveryBeginDate");});//$1を入力してください
					return;
				}
				if(tableData[i][5] && (!tableData[i][6]||tableData[i][6]=="")){
					showMsgBox(Message.msg026,["納品不可終了日"],function(){TemplateGridCenter.tbhot2.selectCellByProp(i,"notDeliveryEndDate");});//$1を入力してください
					return;
				}
				if(tableData[i][5] &&tableData[i][6]&& new Date(new Date(tableData[i][5]))>new Date(new Date(tableData[i][6]))){
					showMsgBox(Message.msg025,[],function(){TemplateGridCenter.tbhot2.selectCellByProp(i,"notDeliveryBeginDate");});//当日より過去の納品日は入力できません。
					return;
				}
				
				sendData += (tableData[i][7]?tableData[i][7]:"")+",";
				sendData += tableData[i][2].split("-")[0]+",";
				sendData += (tableData[i][4]?tableData[i][4].replace(/,/g, "-"):"")+",";
				sendData += (tableData[i][5]?tableData[i][5]:"")+",";
				sendData += (tableData[i][6]?tableData[i][6]:"")+";";
			}
		}
		if(0==cnt){
			showMsgBox(Message.msg004,[],function(){});//チェックされたデータがありません。
			return;
		}
	}
	$.ajax({
		url:`${SystemManage.shiniseAddress}NotDeliveryController/saveInfo`,
		type:'POST',
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		data:{
			sendData: sendData,
			userCD:sessionStorage.getItem("employeeCD"),//社員CD
	        venderCD:sessionStorage.getItem("userCD"),//ベンダーCD
	        flg:tabId=="tbAllGrid"?0:1
	    },
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg028,[],function(){});//登録失敗しました。
			}else{
				tableData_search();
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg028,[],function(){});//登録失敗しました。
		}
	});
}
/////////////////////////////////////////////////////////
//bindDataとonchange事件
//data     Json
//valuecol 項目CDString
//labelcol 項目名 String
//id       String
//multiple  "all":全選(複数選択のみ有効) 
//        "single":初期に選択項目
//        Array:選択したデータ  []:すべて選択しない
////////////////////////////////////////////////////////
function bindData(filtdata, valuecol, labelcol, id, multiple) {
	var data = filtdata;
	var options = [];
	for (var i = 0; i < data.length; i++) {
		options.push({
			label : data[i][valuecol] + "-" + data[i][labelcol],
			title : data[i][labelcol],
			value : data[i][valuecol]
		});
	}
	$('#' + id).multiselect('dataprovider', options);
	if (multiple == "all") {
		//全選  及び　onChange trigger
		$('#' + id).multiselect('selectAll', false, true);
		$('#' + id).multiselect('updateButtonText');
	} else if (multiple == "single") {
		//第一の項目選択　及び　onChange trigger
		$('#' + id).multiselect('select', options[0].value, true);
	} else {
		//選択設定   及び　onChange trigger
		$('#' + id).multiselect('select', multiple, true);
	}
}
function bindData2(filtdata, valuecol, labelcol, id, multiple) {
	var data = filtdata;
	var options = [];
	for (var i = 0; i < data.length; i++) {
		options.push({
			label : data[i][valuecol],
			title : data[i][labelcol],
			value : data[i][valuecol]
		});
	}
	$('#' + id).multiselect('dataprovider', options);
	//the follow process have problem if have time need to fix
	if (multiple == "all") {
		//全選  及び　onChange trigger
		$('#' + id).multiselect('selectAll', false, true);
		$('#' + id).multiselect('updateButtonText');
	} else if (multiple == "single") {
		//第一の項目選択　及び　onChange trigger
		$('#' + id).multiselect('select', options[0].value, true);
	} else {
		//選択設定   及び　onChange trigger
		$('#' + id).multiselect('select', multiple, true);
	}
}