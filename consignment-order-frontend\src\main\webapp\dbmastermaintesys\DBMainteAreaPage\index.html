<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>センター店舗配送画面</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width, initial-scale=1" name="viewport"/>
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Cache-Control" content="no-cache">
	<meta http-equiv="Expires" content="0">
	<link rel="icon" type="image/png/ico" href="../Common/favicon.ico">
	<!-- basic styles -->
	<link href="../Common/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <link href="../Common/common.css" rel="stylesheet">
    <link href="../Common/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/bootstrap/css/bootstrap.min.css" rel="stylesheet">
	<link href="../Common/css/layout.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/css/themes/light2.css" rel="stylesheet" type="text/css" id="style_color"/>
	<link href="../Common/bootstrap-multiselect/css/bootstrap-multiselect.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/handsontable/handsontable.full.min.css" rel="stylesheet"/>
	<link href="../Common/jan/jan.css" rel="stylesheet"/>
	<link href="../Common/zTree/css/zTreeStyle.css" rel="stylesheet"/>
	<link href="../Common/prettify/prettify.css" rel="stylesheet"/>
	<link href="css/style.css" rel="stylesheet" type="text/css" />
	<script src="../Common/json2csv.js" type="text/javascript" ></script>
    <script src="../Common/jquery/jquery.min.js" type="text/javascript"></script>
	<script src="../Common/bootstrap-multiselect/js/bootstrap-multiselect.js" type="text/javascript" ></script>
	<script src="../Common/handsontable/handsontable.full.min.js" type="text/javascript" ></script>
	<script src="../Common/handsontable/handsontable.maxlength.full.js" type="text/javascript" ></script>
    <script src="../Common/prettify/prettify.js" type="text/javascript" ></script>
    <script src="../Common/zTree/js/jquery.ztree.core-3.5.min.js" type="text/javascript" ></script>
    <script src="../Common/zTree/js/jquery.ztree.excheck-3.5.min.js" type="text/javascript" ></script>
	<script>document.write("<s"+"cript src='../Common/version.js?ver="+Math.random()+"'></scr"+"ipt>"); </script>
	<script>document.write("<s"+"cript src='../Common/common.js?ver="+sysversion+"'></scr"+"ipt>");</script>
	<link rel="shortcut icon" href="../Common/favicon.ico"/>
</head>
<body class="page-container-bg-solid">
<script>
	var href=window.document.location.href;
	var rankNum=getRank(href);
</script>
<!-- BEGIN CONTAINER -->
<div class="page-container">
	<!-- BEGIN CONTENT -->
	<div class="page-content-wrapper">
		<div class="page-content">
			<!-- <div class="form-inline hidden-box" > -->	
			<div class="form-inline box" >	  		
			<div class="form-horizontal" id="VenderFLG">		
					<div class="btn-group"  role="group">
						<label class="lblWidth" style="margin-left:25px;">JAN</label>
					</div>
					<div class="btn-group"  role="group">
						<span class="dropdown btn-group" style="margin-left:0px;">
							<button type="button" id="parse_button" class="btn btn-default btn-sm dropdown-toggle"
									style="width:180px;" data-toggle="dropdown" aria-haspopup="true"
									aria-expanded="false" title="JANは完全入力でのみ検索可能です" >JAN入力
								<span class="caret"></span>
							</button>
							<ul class="dropdown-menu scrollable-menu" id="parse_menu">
								<li>
									<a href="javascript:void(0)" style="padding:0 5px;">
										<textarea id="janArea" class="form-control" style="width:180px;" rows="5" placeholder="JANは完全入力でのみ&#13;&#10;検索可能です" onfocus="this.select();"></textarea>
									</a>
								</li>
								<li role="separator" class="divider"></li>
								<li style="padding:0px 5px;"><button type="button" class="btn btn-success btn-sm" style="display: block; width: 100%;"
											 id="jan_reload">確定</button></li>
							</ul>
						</span>
					</div>
				<div class="form-group  pull-right" style="margin-top:15px;margin-right:20px;">
                       <div class="btn-group btnWidth"  role="group" >
						<input id="btn_search" type="button" class="btn btn-primary btn-block" value="検索">
					</div>
					<div class="btn-group btnWidth"  role="group" >
						<!-- <input id="btn_ExcelPort" type="button" class="btn btn-primary btn-block" value="Excel出力"> -->
						<a id="btn_ExcelPort" class="btn btn-success btn-block">
							<i class="glyphicon glyphicon-download-alt"></i>
							CSV出力
						</a>
					</div>	
					<div class="btn-group btnWidth"  role="group" >
						<input id="btn_Delte" type="button" class="btn btn-primary btn-block" value="削除">
					</div>					
				</div>	
				</div>																																											
				</div>			
			  <div>
			</div>
			<!--/検索条件 form-->
			<div class="col-md-12 col-sm-12 col-xs-12" id="grid" style="padding:40px 10px 10px 10px;">
				<div id="MainGrid" style="width:100%"></div>
			</div>			
						
		</div>
	</div>
	<!-- END CONTENT -->
</div>
<!-- END CONTAINER -->

<!-- BEGIN FOOTER -->
<div class="page-footer" style="display: none; height: 4px; padding: 0px; margin: 0px;">
</div>
<!-- END FOOTER -->

<!--店舗-->
<div class="modal fade" id="branchDivDialog" tabindex="-2" role="dialog" aria-labelledby="" style="z-index: 2000;">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-header">
	        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			</button>
	        <h4 class="modal-title" id="branchDialogTitle">店舗選択</h4>
	      </div>
	      <div class="modal-body" style="height:400px;overflow-y: scroll;" >
	      	  <div class="zTreeDemoBackground left">
	      	  	<ul id="branchDialog" class="ztree"></ul>
	      	  </div>
	      </div>
	      <div class="modal-footer">
	          <button type="button" id="branchDialogYes" class="btn btn-default" data-dismiss="">OK</button>
	          <button type="button" id="branchDialogNo" class="btn btn-default" data-dismiss="modal">キャンセル</button>
	      </div>
	    </div>
	  </div>
</div>

<!--メッセージ表示-->
<div class="modal fade" id="myMessage" tabindex="-2" role="dialog" aria-labelledby=""
	 style="z-index: 2000;margin-top:100px;">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-header">
	        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			</button>
	        <h4 class="modal-title" id="myMessageTitle">DBマスタメンテ</h4>
	      </div>
	      <div class="modal-body" >
	      	  <p id="myMessageBody"></p>
	      </div>
	      <div class="modal-footer">
	          <button type="button" id="msgSysBtnYes" class="btn btn-default" data-dismiss="">OK</button>
	          <button type="button" id="msgSysBtnNo" class="btn btn-default" data-dismiss="modal">キャンセル</button>
	      </div>
	    </div>
	  </div>
</div>

<script src="../Common/jquery/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
<script src="../Common/bootstrap/js/bootstrap.min.js"></script>
<script src="../Common/message/message.js"></script>
<script src="../Common/js/metronic.js" type="text/javascript"></script>
<script src="../Common/js/layout.js" type="text/javascript"></script>
<script>
	jQuery(document).ready(function() {    
	   Metronic.init(); // init metronic core componets
	   Layout.init(); // init layout
	});
	makeProcessing(rankNum);
</script>
<script>document.write("<s"+"cript src='js/gridconfig.js?ver="+sysversion+"'></scr"+"ipt>");</script>
<script>document.write("<s"+"cript src='js/controller.js?ver="+sysversion+"'></scr"+"ipt>");</script>
</body>
<!-- END BODY -->
</html>