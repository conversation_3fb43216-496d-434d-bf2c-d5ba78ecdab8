/*Common Message*/
var Message = {
	 "sysTitle": "コンサイメント発注専用システム。"
	,"msg001": "初期化失敗しました。"
	,"msg002": "検索失敗しました。"
	,"msg003": "対象データがありません。"
	,"msg004": "チェックされたデータがありません。"
	,"msg005": "推奨数は0、或は推奨数/納品予定日は空白です。ご確認してください。"
	,"msg006": "同じ混載区分で、納品予定日は同じではない。ご確認してください。"
	,"msg007": "ケース入数は$1です、推奨数はケース入数の倍数を入力してください。"
	,"msg008": "単品発注ロットは$1です、推奨数は単品発注ロットの倍数ではない"//そのまま処理して、よろしいですか？
	,"msg009": "当日より過去の納品予定日は入力できません。"	
	,"msg010": "1年以内の納品予定日を入力してください。"	
	,"msg011": "確定失敗しました。"	
	,"msg012": "$1は不正です。桁数は$2以内に数字入力してください。"
	,"msg013": "$1が必須入力情報です。入力してください。"
	,"msg014": "伝票分けNOは既に存在します。新しく追加してください。"
	,"msg015": "同じ発注案IDの同じ回数の同じ伝票分けNOの推奨数は伝票分け単位数を超えています。ご確認ください。"
	,"msg016": "トラック/コンテナの体積を超えています。ご確認お願いします。"
	,"msg017": "トラック/コンテナの最大重量を超えています。ご確認お願いします。"
	,"msg018": "$1の日付入力が不正です。"
	,"msg019": "案推奨データがないので、確定できません。"
	,"msg020": "JANは5000件まで入力してください。"
	,"msg021": "正しいJANを入力してください。"
	,"msg022": "初期化失敗しました。<br/>サポートが必要な場合、トライアル担当者に問い合わせてください。"
	,"msg023": "同じ発注案IDの同じ回数の伝票分けNO「$1」の納品予定日は全部同じに設定します。よろしいですか？"
	,"msg024": "既存に同じ発注案IDの同じ回数の伝票分けNO「$1」の納品予定日と違います。ご確認ください。"
	,"msg025": "同じ発注案IDの同じ納品予定日の発注予定数は1日最大数を超えています。ご確認ください。"
	,"msg026": "推奨数或は納品予定日は空白です。ご確認してください。"
	,"msg027": "推奨数は100,000を超えました。ご確認ください。"//そのまま処理して、よろしいですか？
	,"msg028": "$1は失敗しました。"
	,"msg029": "発注データの予約は成功しました。自動承認に設定された混載が存在して、承認した時エラーを発生しました。これから、担当承認になります。ご了承してください。"
	,"msg030": "発注データの予約は成功しました。自動承認に設定された混載が存在して、発注承認できない時間帯！これから、担当承認になります。ご了承してください。"
	,"msg031": "今日は同じ商品を発注しました。本当に再発注しますか？"
	,"msg032": "センター「$1」JAN「$2」の納品予定日は重複です。違う納品予定日を入力してください。"
		
}

var showMsgBox = function(msg,inParam,okCallback) {
	for(var counter=0;counter<inParam.length;counter++){
		msg=msg.replace("$"+(counter+1),inParam[counter]);
	}
	$("#myMessageBody").html(msg);
	$("#msgSysBtnNo").hide();
	if($('.modal-backdrop').length>0){
		$("#myMessage").modal({backdrop: false, keyboard: false});
	}else{
		$("#myMessage").modal({backdrop: 'static', keyboard: false});
	}
	
	//OK:メッセージ閉じる
	$("#msgSysBtnYes").bind("click",function(){
    	$("#myMessage").modal('hide');
    	$(this).unbind('click');
    	okCallback.call();
    });
	//メッセージ表示
	$("#myMessage").on("shown.bs.modal",function(){
		$("#msgSysBtnYes").focus();
		$('.modal-backdrop').each(function() {
			$(this).attr("style","z-Index:1100; !important");
		});
    });
	//メッセージ閉じる
	$("#myMessage").on("hidden.bs.modal",function(){
		$('.modal-backdrop').each(function() {
    		$(this).attr("style","z-Index:1040; !important");
    	});
    });
}

var showMsgConfirm = function(msg,inParam,okCallback,cancelCallback) {
	for(var counter=0;counter<inParam.length;counter++){
		msg=msg.replace("$"+(counter+1),inParam[counter]);
	}
	$("#myMessageBody").html(msg);
	$("#msgSysBtnNo").show();
	if($('.modal-backdrop').length>0){
		$("#myMessage").modal({backdrop: false, keyboard: false});
	}else{
		$("#myMessage").modal({backdrop: 'static', keyboard: false});
	}
	
	$("#msgSysBtnYes").unbind('click').bind("click",function(){
    	$("#myMessage").modal('hide');
    	//$(this).unbind('click');
    	okCallback.call();
    });
	$("#msgSysBtnNo").unbind('click').bind("click",function(){
    	$("#myMessage").modal('hide');
    	//$(this).unbind('click');
    	cancelCallback.call();
    });
	//メッセージ表示
	$("#myMessage").on("shown.bs.modal",function(){
		$("#msgSysBtnNo").focus();
		$('.modal-backdrop').each(function() {
			$(this).attr("style","z-Index:1100; !important");
		});
    });
	//メッセージ閉じる
	$("#myMessage").on("hidden.bs.modal",function(){
		$('.modal-backdrop').each(function() {
    		$(this).attr("style","z-Index:1040; !important");
    	});
    });
	
}