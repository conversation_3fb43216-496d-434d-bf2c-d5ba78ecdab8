/**
 * @description: DBマスタメンテナンス-権限設定画面
 * @author: 10104911 z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2018/9/28
 */
$(document).ready(function () {
	$("#loadMask").show();
	$(document).ajaxStart(function(){
		$("#loadMask").show();
    }).ajaxStop(function(){
    	$("#loadMask").hide();
    });
	
	//データINIT
	TemplateGrid.CreatHotGrid('MainGrid', []);
	
	//ベンダーリスト、画面の区分リスト
	getInitList();
		
	//検索
	$("#btn_search").on("click",function(){
		tableDataSearch();
	});
	
	//登録
	$("#btn_save").on("click",function(){
		tableDataSave("0");//0:保存 1:削除
	});
	
	//削除
	$("#btn_delete").on("click",function(){
		tableDataSave("1");//0:保存 1:削除
	});
});

//ベンダーリスト、APPNOリスト、権限リスト
var getInitList = function(){
	//INIT
	divMultiLoadInit();
	
	$.ajax({
		url:SystemManage.address+'DBMainteAuthority/getInitList',
		type:'POST',
		data:{userCD:sessionStorage.getItem("employeeCD"),//社員CD
			venderCD:sessionStorage.getItem("userCD")//ベンダーCD
		},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg039,[],function(){});//イベント名リスト、店舗Tree、権限 検索失敗しました。
			}else{				
				var result = JSON.parse(response);
				//ベンダーリスト
				RegistryGlobal.supplierList = result[0].supplierList;
				//画面区分リスト
				RegistryGlobal.appnoList = result[1].appnoList;
				//権限リスト
				RegistryGlobal.authorityList = result[2].authorityList;
				
				bindData(RegistryGlobal.supplierList, "id", "label", "divSupplierMulti", "");
				bindData(RegistryGlobal.appnoList, "id", "label", "divAppnoMulti", "");
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg039,[],function(){});//イベント名リスト、店舗Tree、権限 検索失敗しました。
		}
	});
}

//検索
var tableDataSearch = function(){
	var param = {};
	if($("#divSupplierMulti").val()){
		param.SupplierCD = $("#divSupplierMulti").val().join(",");
	}else{
		param.SupplierCD = $("#divSupplierMulti").val();
	}
	if($("#divAppnoMulti").val()){
		param.APPNO = $("#divAppnoMulti").val().join(",");
	}else{
		param.APPNO = $("#divAppnoMulti").val();
	}
	
	//クリアデータ
	TemplateGrid.CreatHotGrid('MainGrid', []);
	
	$.ajax({
		url:SystemManage.address+'DBMainteAuthority/tableDataSearch',
		type:'POST',
		data:param,
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
			}else{				
				var result = JSON.parse(response);
				
				TemplateGrid.CreatHotGrid('MainGrid', result);
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
		}
	});
}

//登録、削除
var tableDataSave = function(para_Del){
	var param = {};
	param.userCD = sessionStorage.getItem("employeeCD");
	param.para_Del = para_Del;//0:保存 1:削除
	
	var tableDatas = TemplateGrid.tbhot.getSourceData();
	
	var saveDatas = [];
	for(var i=0; i<tableDatas.length; i++){
		var tableData = tableDatas[i];
		if(tableData.ChkFlg=="1"){
			if(!tableData.SupplierCD){
				showMsgBox(Message.msg040,[],function(){});//ベンダーを選択してください。
				return;
			}
			if(!tableData.APPNO){
				showMsgBox(Message.msg041,[],function(){});//画面区分を選択してください。
				return;
			}
			if(para_Del=="0" && !tableData.SupAppAuthority){
				showMsgBox(Message.msg042,[],function(){});//権限を選択してください。
				return;
			}
			
			for(var j = 0; j < saveDatas.length; j++){
	            if (tableData.SupplierCD == saveDatas[j].split(",")[0]&& tableData.APPNO==saveDatas[j].split(",")[1]) {
	        	    showMsgBox(Message.msg023,[],function(){});//重複なデータチェック
					return;
	            }
		    }
			var data = [];
			data.push(tableData.SupplierCD);
			data.push(tableData.APPNO);
			data.push(tableData.SupAppAuthority);
			
			saveDatas.push(data.join(","));
		}
	}
	
	if (saveDatas.length == 0){
		showMsgBox(Message.msg004,[],function(){});//チェックされたデータがありません。
	} else {
		param.saveDatas = saveDatas.join(";");
		
		$.ajax({
			url:SystemManage.address+'DBMainteAuthority/tableDataSave',
			type:'POST',
			data:param,
			success:function(response){
				if("success"==response){
					//検索
					tableDataSearch();
				}else{
					if(para_Del=="0"){						
						showMsgBox(Message.msg028,[],function(){});//登録失敗しました。
					}else{
						showMsgBox(Message.msg037,[],function(){});//削除失敗しました。
					}
				}
			},
			error:function(response, textStatus){
				if(para_Del=="0"){						
					showMsgBox(Message.msg028,[],function(){});//登録失敗しました。
				}else{
					showMsgBox(Message.msg037,[],function(){});//削除失敗しました。
				}
			}
		});
	}
}

//INIT
var divMultiLoadInit=function(){
	$('#divSupplierMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全ベンダー',
		nonSelectedText: '全て',
		nSelectedText: ' ベンダー',
		numberDisplayed:1
	});
	$('#divAppnoMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全画面',
		nonSelectedText: '全て',
		nSelectedText: ' 画面',
		numberDisplayed:1
	});
}

/////////////////////////////////////////////////////////
//bindDataとonchange事件
//data     Json
//valuecol 項目CDString
//labelcol 項目名 String
//id       String
//multiple  "all":全選(複数選択のみ有効) 
//      "single":初期に選択項目
//      Array:選択したデータ  []:すべて選択しない
////////////////////////////////////////////////////////
function bindData(filtdata ,valuecol, labelcol, id, multiple){
	var data = filtdata;
	var options=[];
	for(var i=0;i<data.length;i++){
		   options.push({
			   label:data[i][labelcol],
			   title:data[i][labelcol],
			   value:data[i][valuecol]
		   });
	}
	$('#'+id).multiselect('dataprovider', options);
	if(multiple=="all"){
		 	//全選  及び　onChange trigger
		 	$('#'+id).multiselect('selectAll',false,true);
		 	$('#'+id).multiselect('updateButtonText');				   
	} else if(multiple=="single"){
		 	//第一の項目選択　及び　onChange trigger
		 	$('#'+id).multiselect('select',options[0].value,true);			     
	} else {
		   	//選択設定   及び　onChange trigger
		 $('#'+id).multiselect('select',multiple,true);
	}
}