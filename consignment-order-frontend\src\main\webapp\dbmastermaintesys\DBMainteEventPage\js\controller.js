/**
 * @description: DBマスタメンテナンス-イベント設定画面
 * @author: 10104911 z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2018/9/24
 */
$(document).ready(function () {
	$("#loadMask").show();
	$(document).ajaxStart(function(){
		$("#loadMask").show();
    }).ajaxStop(function(){
    	$("#loadMask").hide();
    });
	
	////INIT
	divMultiLoadInit();
	
	//イベント開始日、イベント終了日 設定
	$('#StartDate, #EndDate').datepicker({	
		language:  'ja',
		format:"yyyy/mm/dd",
		weekStart:1,
		todayBtn: "linked",
		autoclose: true,
		todayHighlight: true,
		orientation:"top auto",
		calendarWeeks:true
	});
	$("#StartDate").datepicker("setDate", new Date());
	$("#EndDate").datepicker("setDate", new Date());
	
	//ベンダー用画面Grid表示する
	tableDataSearch();
	
	//イベント名リスト、店舗Tree　検索
	eventBranchSearch();
	
	//店舗Tree Dialog，"OK"
	$("#branchDialogYes").click(function(){
		branchDialogYes();
	});
	
	//検索
	$("#btn_search").on("click",function(){
		tableDataSearch();
	});
	
	//登録
	$("#btn_Save").on("click",function(){
		tableDataSave("0");//0:保存 1:削除
	});
	
	//削除
	$("#btn_Delete").on("click",function(){
		tableDataSave("1");//0:保存 1:削除
	});
});

//イベント名リスト、店舗Tree　検索
var eventBranchSearch = function(){
	var param = {};
	param.userCD = sessionStorage.getItem("employeeCD");
	param.venderCD = sessionStorage.getItem("userCD");

	$.ajax({
		url:`${SystemManage.shiniseAddress}DBMainteEvent/eventBranchSearch`,
		type: 'POST',
		data: param,
		headers: {
			"user-code": sessionStorage.getItem("userCode") || ""
		},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg027,[],function(){});//イベント名リスト、店舗Tree 検索失敗しました。
			}else{				
				var result = JSON.parse(response);
				RegistryGlobal.branchList = result[1].branchTreeList;
				bindData(result[0].eventNameList, "EventId", "EventName", "divEventNameMulti", "");
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg027,[],function(){});//イベント名リスト、店舗Tree 検索失敗しました。
		}
	});
}

//検索
var tableDataSearch = function(){
	var param = {};
	param.EventIds = $("#divEventNameMulti").val()?$("#divEventNameMulti").val().join(","):$("#divEventNameMulti").val();
	param.StartDate = $("#StartDate").val();
	param.EndDate = $("#EndDate").val();
	
	//クリアデータ
	TemplateGrid.CreatHotGrid('MainGrid', []);
	
	$.ajax({
		url:`${SystemManage.shiniseAddress}DBMainteEvent/tableDataSearch`,
		type:'POST',
		data:param,
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
			}else{				
				var result = JSON.parse(response);
				RegistryGlobal.tableDataList = result[0].tableDataList;//ページデータ
				RegistryGlobal.divList = result[1].divList;//DIVリスト
				
				TemplateGrid.CreatHotGrid('MainGrid', RegistryGlobal.tableDataList);
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
		}
	});
}

//店舗Tree Dialog，"OK"
var branchDialogYes = function(){
	RegistryGlobal.chkNodes = zTree.getCheckedNodes();
	var dbList = [], branchList = [], DBAreaBranchList = [], BranchSel = [];
	RegistryGlobal.chkNodes.forEach(function(item, index){
		//DBエリア
		if(item.level=="1"){
			dbList.push(item.name);
		}
		//店舗
		else if(item.level=="3"){
			branchList.push(item.name);
			DBAreaBranchList.push(item.id);
			BranchSel.push(item.id.split("^")[2]);
		}
	});
	TemplateGrid.tbhot.setDataAtRowProp(RegistryGlobal.r, "DBArea", dbList.join(","));
	TemplateGrid.tbhot.setDataAtRowProp(RegistryGlobal.r, "Branch", branchList.join(","));
	TemplateGrid.tbhot.setDataAtRowProp(RegistryGlobal.r, "DBAreaBranch", DBAreaBranchList.join(","));
	TemplateGrid.tbhot.setDataAtRowProp(RegistryGlobal.r, "BranchSel", BranchSel.join(","));
	$("#branchDivDialog").modal("hide");
}

//登録、削除
var tableDataSave = function(para_Del){
	var param = {};
	param.userCD = sessionStorage.getItem("employeeCD");
	param.venderCD = sessionStorage.getItem("userCD");
	param.para_Del = para_Del;//0:保存 1:削除
	
	var tableDatas = TemplateGrid.tbhot.getSourceData();
	
	var saveDatas = [], phyDelDatas = [];
	for(var i=0; i<tableDatas.length; i++){
		var tableData = tableDatas[i];
		if(tableData.ChkFlg=="1"){
			if(para_Del == "0"){//0:保存
				if(!tableData.EventName){
					showMsgBox(Message.msg030,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"EventName");});//イベント名を入力してください。
					return;
				}
				if(!tableData.BranchSel){
					showMsgBox(Message.msg031,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"BranchSel");});//店舗を選択してください。
					return;
				}
				if(!tableData.Division){
					showMsgBox(Message.msg032,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"Division");});//DIVを選択してください。
					return;
				}
				if(!tableData.EventStartDate){
					showMsgBox(Message.msg033,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"EventStartDate");});//イベント開始日を入力してください。
					return;
				}
				if(!tableData.EventEndDate){
					showMsgBox(Message.msg034,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"EventEndDate");});//イベント終了日を入力してください。
					return;
				}
				if($(TemplateGrid.tbhot.getCell(i, 7)).hasClass("htInvalid")){
					showMsgBox(Message.msg035,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"EventStartDate");});//イベント開始日を正確に入力してください。
					return;
				}
				if($(TemplateGrid.tbhot.getCell(i, 8)).hasClass("htInvalid")){
					showMsgBox(Message.msg036,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"EventEndDate");});//イベント終了日を正確に入力してください。
					return;
				}
				if(new Date(tableData.EventStartDate)>new Date(tableData.EventEndDate)){
					showMsgBox(Message.msg057,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"EventEndDate");});//イベント開始日より終了日は小さいです。
					return;
				}
			}
			
			if(para_Del=="1" && !tableData.ID){
				phyDelDatas.push(i);
			}else{		
				if(para_Del=="0"){
					var data = [];
					data.push(tableData.EventName);
					data.push(bindDBAreaBranchValue(tableData.DBAreaBranch, tableData.DBArea, tableData.Branch));
					data.push(tableData.Division.replaceAll(",", "-"));
					data.push(tableData.EventStartDate);
					data.push(tableData.EventEndDate);
					data.push(tableData.ID || 'null');
					saveDatas.push(data.join(","));
				} else {
					saveDatas.push(tableData.ID);
				}
			}
		}
	}
	
	if (phyDelDatas.length==0 && saveDatas.length==0){
		showMsgBox(Message.msg004,[],function(){});//チェックされたデータがありません。
	}else if (phyDelDatas.length>0 && saveDatas.length==0){
		showMsgBox(Message.msg038, [], function(){
			//ページデータの削除
			for(var i=phyDelDatas.length-1; i>=0; i--){
				var index = phyDelDatas[i];
				tableDatas.splice(index, 1);
			}
		});//削除成功しました。
	}else if (saveDatas.length > 0) {
		param.saveDatas = saveDatas.join(";");
		
		$.ajax({
			url:`${SystemManage.shiniseAddress}DBMainteEvent/tableDataSave`,
			type:'POST',
			data:param,
			headers: {
	        	"user-code": sessionStorage.getItem("userCode") || ""
	    	},
			success:function(response){
				if("true"==response){
					//イベント名リスト、店舗Tree　検索
					eventBranchSearch();
					//検索
					tableDataSearch();
				}else{
					if(para_Del=="0"){						
						showMsgBox(Message.msg028,[],function(){});//登録失敗しました。
					}else{
						showMsgBox(Message.msg037,[],function(){});//削除失敗しました。
					}
				}
			},
			error:function(response, textStatus){
				if(para_Del=="0"){						
					showMsgBox(Message.msg028,[],function(){});//登録失敗しました。
				}else{
					showMsgBox(Message.msg037,[],function(){});//削除失敗しました。
				}
			}
		});
	}
}

//INIT
var divMultiLoadInit=function(){
	$('#divEventNameMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全イベント',
		nonSelectedText: '全て',
		nSelectedText: ' イベント',
		numberDisplayed:1
	});
}

/////////////////////////////////////////////////////////
//bindDataとonchange事件
//data     Json
//valuecol 項目CDString
//labelcol 項目名 String
//id       String
//multiple  "all":全選(複数選択のみ有効) 
//    "single":初期に選択項目
//    Array:選択したデータ  []:すべて選択しない
////////////////////////////////////////////////////////
function bindData(filtdata ,valuecol, labelcol, id, multiple){
	var data = filtdata;
	var options=[];
	for(var i=0;i<data.length;i++){
		   options.push({
			   label:data[i][labelcol],
			   title:data[i][labelcol],
			   value:data[i][valuecol]
		   });
	}
	$('#'+id).multiselect('dataprovider', options);
	if(multiple=="all"){
		 	//全選  及び　onChange trigger
		 	$('#'+id).multiselect('selectAll',false,true);
		 	$('#'+id).multiselect('updateButtonText');				   
	} else if(multiple=="single"){
		 	//第一の項目選択　及び　onChange trigger
		 	$('#'+id).multiselect('select',options[0].value,true);			     
	} else {
		   	//選択設定   及び　onChange trigger
		 $('#'+id).multiselect('select',multiple,true);
	}
}

//日付のフォーマット
var dateFtt = function(fmt, date) {
	var o = {
		"M+" : date.getMonth() + 1, //月   
		"d+" : date.getDate(), //日   
		"h+" : date.getHours(), //時   
		"m+" : date.getMinutes(), //分   
		"s+" : date.getSeconds(), //秒   
		"q+" : Math.floor((date.getMonth() + 3) / 3), //四半期   
		"S" : date.getMilliseconds() //ms   
	};
	if (/(y+)/.test(fmt))
		fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "")
				.substr(4 - RegExp.$1.length));
	for ( var k in o)
		if (new RegExp("(" + k + ")").test(fmt))
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k])
					: (("00" + o[k]).substr(("" + o[k]).length)));
	return fmt;
}

const getName = function(dBAreas, dBAreaCD){
	const dBArea = dBAreas.find((item)=>item.key === dBAreaCD);
	return dBArea?.value;
}

const bindDBAreaBranchValue = function(dBAreaBranch, dBArea, branch){
	const dBAreas = dBArea.split(",").map((item) => {
		const areas = item.split("-");
		const key = areas[0];
		const value = `${areas[0]}&${areas[1]}`;
		return {key, value};
	});
	const branchs = branch.split(",").map((item) => {
		const branch = item.split("-");
		const key = branch[0];
		const value = `${branch[0]}&${branch[1]}`;
		return {key, value};
	});

	if(dBAreaBranch) {
		const dBAreaBranchList = dBAreaBranch.split(",");
		const dBAreaListValue = dBAreaBranchList.map((item)=>{
			const dBArea = item.split("^");
			
			return `${getName(dBAreas, dBArea[0])}&${dBArea[1]}&${getName(branchs, dBArea[2])}`;
		}).join('-')
		return dBAreaListValue;
	}
	return "";
}
