package jp.trial.DCOrderConsignment.common;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Properties;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 * Date: 2015/07/07
 */
public class BaseDao {
	private static final Logger logger=Logger.getLogger(Thread.currentThread().getStackTrace()[1].getClassName());
	/**Description: データベースに接続*/
	public Connection getConnection() {
		Connection conn = null;
		Properties properties = new Properties();
		try {
			
			properties.load(this.getClass().getResourceAsStream("/db.properties"));
			String DRIVER_CLASS = properties.getProperty("DRIVER_CLASS");
			String DATABASE_URL = properties.getProperty("DATABASE_URL");
			String DATABASE_USER = properties.getProperty("DATABASE_USER");
			String DATABASE_PASSWORD = properties.getProperty("DATABASE_PASSWORD");
			
			Class.forName(DRIVER_CLASS);
			conn = DriverManager.getConnection(DATABASE_URL, DATABASE_USER,
					DATABASE_PASSWORD);
		} catch (ClassNotFoundException e) {
			logger.error(e.getMessage());
			e.printStackTrace();
		} catch (IOException e) {
			logger.error(e.getMessage());
			e.printStackTrace();
		} catch (SQLException e) {
			logger.error(e.getMessage());
			e.printStackTrace();
		}
		return conn;
	}

	/**Description: データベースの接続を閉じる*/
	public void closeAll(Connection conn, Statement stmt, ResultSet rs) {
		try {
			if (rs != null) {
				rs.close();
			}
			if (stmt != null) {
				stmt.close();
			}
			if (conn != null) {
				conn.close();
			}
		} catch (SQLException e) {
			logger.error(e.getMessage());
			e.printStackTrace();
		}
	}
}
