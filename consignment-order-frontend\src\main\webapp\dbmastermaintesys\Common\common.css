@CHARSET "UTF-8";
/* *********まち画面******************** */
#loadMask{
	display:none;
	top: 0%; 
	left: 0%;
	width:100%;
	height:100%;
	position:fixed;
    z-index: 3000;
}
.bgStyle{
	opacity: .5;
	position: absolute; 
	top: 0%; 
	left: 0%;
	width:100%;
	height:100%;
	background-color:lightgray;
	z-index:999
}
.maskStyle{
	position: absolute; 
	top: 0%; 
	left: 0%;
	text-align:center;
	vertical-align:middle;
	width:100%;
	height:100%;
	z-index:1002
}
.pcursor{
	cursor:pointer;
}
 .hr-dotted {
    border-top-style: dotted;
}
.hr {
    display: block;
    height: 0;
    overflow: hidden;
    font-size: 0;
    border-top: 1px solid #e3e3e3;
    margin: 1px 0;
}

.hrr-dotted {
    border-top: 1px dotted #e3e3e3;
}
.hrr {
    display: block;
    height: 0;
    overflow: hidden;
    font-size: 0;
    margin: 5px 0;
    cursor: pointer;
}
.leftblue{
	background-image:url(../Common/images/left01.png);
	background-repeat:no-repeat;
}
.leftred{
	background-image:url(../Common/images/left02.png);
	background-repeat:no-repeat;
}
.rightblue{
	background-image:url(../Common/images/right01.png);
	background-repeat:no-repeat;
}
.rightred{
	background-image:url(../Common/images/right02.png);
	background-repeat:no-repeat;
}
.marktxt{
	height:40px;
	padding:5px 30px;
	font-size: 18px;
	color: #FFFFFF;
}

.btn {
    display: inline-block;
    padding: 3px 8px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.428571429;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
}
.headlogo{
  	color: #fff;
    font-size:16px;
    text-shadow: 2px 4px 3px rgba(100,100,115,.45);
    margin-left:-20px;
    height:40px;
    padding-top:22px;
}
.hr10{
	margin: 10px 0px;
}
.form-group{
	 margin-bottom: 10px;
}
.margintop5{
	margin-top:5px;
}
.margintop10{
	margin-top:10px;
}
.margintop15{
	margin-top:15px;
}
.margintop20{
	margin-top:20px;
}
.tree-node-selected {
  background: #777;
  color: #fff;
}
.radio-inline,.checkbox-inline {
    display: inline-block;
    padding-left: 20px;
    margin-bottom: 0;
    font-weight: normal;
    vertical-align: middle;
    cursor: pointer;
    text-shadow: 2px 2px 3px rgba(100,100,115,.45);
}
.nav-list>li.active:after {
    display: inline-block;
    content: "";
    position: absolute;
    right: -2px;
    top: -1px;
    bottom: 0;
    z-index: 1;
    border: 2px solid #555;
    border-width: 0 2px 0 0;
}
.nav-list li.active>a:after {
    display: block;
    content: "";
    position: absolute!important;
    right: 0;
    top: 4px;
    border: 8px solid transparent;
    border-width: 14px 10px;
    border-right-color: #555;
}
.nav-list>li.active>a,.nav-list>li.active>a:hover,.nav-list>li.active>a:focus,.nav-list>li.active>a:active {
    background-color: #fff;
    color: #555;
    font-weight: bold;
    font-size: 13px;
}
.nav-list>li .submenu>li.active>a {
    color: #333;
    font-size: 11px;
}
.nav-list>li .submenu>li>a {
    color: #333;
    font-size: 11px;
}

.nav-list>li.open>a {
    background-color: #fafafa;
    color: #333;
}
.ace-nav>li.light-blue>a {
    background-color: #455863;
}
.breadcrumbs {
    position: relative;
    border-bottom: 1px solid #e5e5e5;
    background-color: #fff;
    min-height: 41px;
    line-height: 40px;
    padding: 0 12px 0 0;
    display: block;
}

.ace-nav>li {
    line-height: 45px;
    height: 45px;
    position: relative;
    background-color: #333;
    float: left!important;
    border-left: 1px solid #3f3f3f;
    border-right: 1px solid #1f1f1f;
}
.ace-nav>li.grey>a {
    background-color: #333;
    border-left: 1px solid #3f3f3f;
    border-right: 1px solid #1f1f1f;
}
.ace-nav>li.purple>a {
    background-color: #333;
    border-left: 1px solid #3f3f3f;
    border-right: 1px solid #1f1f1f;
}
.ace-nav>li.light-blue>a {
   background-color: #333;
    border-left: 1px solid #3f3f3f;
    border-right: 1px solid #1f1f1f;
}

.ace-nav>li.light-blue>a {
    background-color: #333
}

.ace-nav>li.light-blue>a:hover,.ace-nav>li.light-blue>a:focus {
    background-color: #000
}

.ace-nav>li.open.light-blue>a {
    background-color: #333!important
}

.ace-nav>li.light-blue2>a {
    background-color: #444
}

.ace-nav>li.light-blue2>a:hover,.ace-nav>li.light-blue2>a:focus {
    background-color: #000
}

.ace-nav>li.open.light-blue2>a {
    background-color: #444!important
}

.ace-nav>li.grey>a:hover,.ace-nav>li.grey>a:focus {
    background-color: #000
}

.ace-nav>li.purple>a:hover,.ace-nav>li.purple>a:focus {
     background-color: #000
}
.navbar .navbar-brand {
    color: #fff;
    font-size: 24px;
    text-shadow: none;
    padding-top: 0px;
    padding-bottom: 0px;
    padding-left:0px;
    margin-left:-10px;
}
.margintop3{
    margin-top: 3px;
}

.dropdown-navbar>li.dropdown-header {
    color: #8090a0;
    border-bottom-color: #bcd4e5;
}
.modal-body{
    position: relative;
    padding: 5px 25px;
}
.datagrid-row {
  height: 33px;
}
.uxcldclr{
    color:#BF3B3B;
}
.lbllinklabel{
    color:blue;
    cursor:pointer;
    font-size: 13px;
}
.linkmarklabel{
    color:blue;
    cursor:pointer;
    font-size: 18px;
}
.marginBottom0{
    margin-bottom:0px;
}

.mulSelectCss{
    background-color: #fff;
    padding: 3px 4px;
    height: 30px;
    border-radius: 0;
    -webkit-box-shadow: none!important;
    box-shadow: none!important;
    color: #393939;
    background-color: #fff;
    border: 1px solid #d5d5d5;
    width: 130px;
    overflow: hidden;
    text-align: left;
}
.multiselect span{
  margin-left: 3px;
}

.tree-node {
  height: 24px;
  white-space: nowrap;
  cursor: pointer;
}
/***********handsontable************/
.handsontable th,
.handsontable td {
    line-height:26px;
}
.handsontable th {
    background-color: #FFFFFF;
}
.x-hotgrid
{
    margin:5px 3px;   
}
.x-currentRow {
  background-color: rgba(249, 224, 143, 0.4) !important;
}

.handsontable input,.handsontable textarea {
    min-height: initial;
    border: 1px solid rgb(187, 187, 187);
}
.uxtextshadow{
    text-shadow: 2px 2px 3px rgba(100,100,115,.45);
}