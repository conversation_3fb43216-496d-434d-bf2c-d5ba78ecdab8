# Stage 1: Build
FROM maven:3.9.9-eclipse-temurin-11-alpine AS builder

WORKDIR /app
COPY . .

RUN mvn clean package -DskipTests

# Stage 2: Run with Tomcat
FROM tomcat:9.0-jdk11

# Clean default apps
RUN rm -rf /usr/local/tomcat/webapps/*

# Copy your WAR with original name
COPY --from=builder /app/target/DCOrderConsignment.war /usr/local/tomcat/webapps/DCOrderConsignment.war

EXPOSE 8080
CMD ["catalina.sh", "run"]
