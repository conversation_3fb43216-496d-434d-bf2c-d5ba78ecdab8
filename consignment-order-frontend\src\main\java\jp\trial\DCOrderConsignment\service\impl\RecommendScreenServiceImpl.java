package jp.trial.DCOrderConsignment.service.impl;

import java.util.List;

import jp.trial.DCOrderConsignment.dao.impl.RecommendScreenDaoImpl;
import jp.trial.DCOrderConsignment.model.InOutPlanModel;
import jp.trial.DCOrderConsignment.service.RecommendScreenService;

import com.alibaba.fastjson.JSON;

public class RecommendScreenServiceImpl implements RecommendScreenService {
	private RecommendScreenDaoImpl recommendScreenDao=new RecommendScreenDaoImpl();
	
	/** DIVとセンターとFOUR WEEKSを取得する*/
	public String getInitDataList(String userCD,String employeeCD) throws Exception {
		String res = "";
		List<Object> objList;
		try{
			objList = recommendScreenDao.getInitDataList(userCD,employeeCD);
			res = JSON.toJSONString(objList);
		}catch(Exception e){
			throw new Exception(e.getMessage());
		}
		return res;
	}
    
	/** 検索*/
	public String tableData_search(String divStr, String storeHouseStr, String janStr, String userCD, String dataFlg,String employeeCD,String venderFlg,String chkTimesFlg) throws Exception {
		
		String res = "";
		List<Object> objList;
		try{
			objList = recommendScreenDao.tableData_search(divStr, storeHouseStr, janStr, userCD, dataFlg,employeeCD,venderFlg,chkTimesFlg);
			res = JSON.toJSONString(objList);
		}catch(Exception e){
			throw new Exception(e.getMessage());
		}
		return res;
	}
	
	/** 確定*/
	public String tableData_confirm(String sendData, String userCD,String employeeCD) throws Exception {
		String res = "false";
		try{
			res=recommendScreenDao.tableData_confirm(sendData, userCD,employeeCD);
		}catch(Exception e){
			throw new Exception(e.getMessage());
		}
		return res;
	}

	/*センター入出荷予定結果一覧*/
	public String GetInOutPlanInfo(String jan,String storeHouseCD) throws Exception{

		String res = "";
		List<InOutPlanModel> objList;
		try{
			objList = recommendScreenDao.GetInOutPlanInfo(jan,storeHouseCD);
			res = JSON.toJSONString(objList);
		}catch(Exception e){
			throw new Exception(e.getMessage());
		}
		return res;
	}
}
