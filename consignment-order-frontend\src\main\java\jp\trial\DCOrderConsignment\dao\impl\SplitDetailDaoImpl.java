package jp.trial.DCOrderConsignment.dao.impl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import jp.trial.DCOrderConsignment.common.BaseDao;
import jp.trial.DCOrderConsignment.dao.SplitDetailDao;
import jp.trial.DCOrderConsignment.model.SplitDetailModel;
import jp.trial.DCOrderConsignment.model.SplitNotDeliveryModel;

import org.apache.log4j.Logger;

public class SplitDetailDaoImpl extends BaseDao implements SplitDetailDao{
	//データベース接続
	private Connection conn = null;
	// 実行Statement
	private CallableStatement cstmt = null;

	// LOG
	private static final Logger logger = Logger.getLogger(SplitDetailDaoImpl.class);
	/*
	 * 伝票明細データ初期化
	 * */
	@Override
	public List<Object> GetSplitDetailInfo(String orderCaseID) throws Exception {
		//logger.info(Thread.currentThread().getStackTrace()[1].getMethodName());
		// データベースの接続 OPEN
		conn = super.getConnection();
		List<Object> objList = new ArrayList<Object>();
		List<SplitDetailModel> showList = new ArrayList<SplitDetailModel>();
		List<SplitNotDeliveryModel> confirmList = new ArrayList<SplitNotDeliveryModel>();
		String sql = "{Call PROC_DCOrderAutoSys_GetOrderSplitDetail(?,?)}";
		try {
			// 実行SQL
			cstmt = conn.prepareCall(sql);
			cstmt.setQueryTimeout(300);
			int pram = 1;
			cstmt.setString(pram++, orderCaseID);
			cstmt.setInt(pram++, 1);

			// ストアドプロシージャ実行
			cstmt.execute();
			ResultSet rs = cstmt.getResultSet();
			SplitDetailModel showModel;
			while(rs.next()){
				showModel = new SplitDetailModel();
				showModel.setChkFlg("1");
				showModel.setTimes(rs.getString("Times"));
				showModel.setOrderCaseDetailID(rs.getString("OrderCaseDetailID"));
				showModel.setSplitNO(rs.getString("SplitNO"));
				showModel.setMixCD(rs.getString("MixCD"));
				showModel.setJAN(rs.getString("JAN"));
				showModel.setProductName(rs.getString("ProductName"));
				showModel.setSpecName(rs.getString("SpecName"));
				showModel.setOrderUnitQty(rs.getString("OrderUnitQty"));
				showModel.setOrderUnit(rs.getString("OrderUnit"));
				showModel.setLotUnit(rs.getString("LotUnit"));
				showModel.setTerms(rs.getString("Terms"));
				showModel.setOrderPlanQtyUpdate(rs.getString("OrderPlanQtyUpdate"));
				showModel.setDeliveryPlanDateUpdate(rs.getString("DeliveryPlanDateUpdate"));
				showModel.setOrderPlanQtyUpdate_Old(rs.getString("OrderPlanQtyUpdate_Old"));
				showModel.setDeliveryPlanDateUpdate_Old(rs.getString("DeliveryPlanDateUpdate_Old"));
				showModel.setSplitNO_Old(rs.getString("SplitNO_Old"));
				showModel.setTermsCD(rs.getString("TermsCD"));
				showModel.setInnerCaseQty(rs.getString("InnerCaseQty"));
				showModel.setCSlot(rs.getString("CSlot"));
				showModel.setMixLotUnit(rs.getString("MixLotUnit"));
				showModel.setCSLotUnit(rs.getString("CSLotUnit"));
				showModel.setSlipSplitQty(rs.getString("SlipSplitQty"));
				showModel.setDayMaxQty(rs.getString("DayMaxQty"));
				showModel.setProLotQtyCheck(rs.getString("ProLotQtyCheck"));
				showModel.setCaseMeasurement(rs.getString("CaseMeasurement"));
				showModel.setCaseWeight(rs.getString("CaseWeight"));
				showModel.setSplitMesure(rs.getString("SplitMesure"));
				showModel.setSplitWeight(rs.getString("SplitWeight"));
				showModel.setMeasurement(rs.getString("Measurement"));
				showModel.setMaxWeight(rs.getString("MaxWeight"));
				showModel.setAutoSplitFlg(rs.getString("AutoSplitFlg"));
				showModel.setNotDeliveryCheck(rs.getString("NotDeliveryCheck"));
				showModel.setCaseQty(rs.getString("CaseQty"));
				showList.add(showModel);
			}
			if(cstmt.getMoreResults()){
				rs = cstmt.getResultSet();
				SplitNotDeliveryModel NotDelModel;
				while(rs.next()){
					NotDelModel = new SplitNotDeliveryModel();
					NotDelModel.setNotDeliveryWeekDay(rs.getString("NotDeliveryWeekDay"));
					NotDelModel.setNotDeliveryBeginDate(rs.getString("NotDeliveryBeginDate"));
					NotDelModel.setNotDeliveryEndDate(rs.getString("NotDeliveryEndDate"));
					confirmList.add(NotDelModel);
				}
			}
			objList.add(showList);
			objList.add(confirmList);
		}catch (Exception e) {
			logger.error(e.getMessage());
			e.printStackTrace();
			throw new Exception("false");
		} finally {
			// データベースの接続　CLOSE
			super.closeAll(conn, cstmt, null);
		}
		return objList;
	}
	
	/*
	 * 確定
	 * */
	public String SaveSplitDetailInfo(String sendData, String userCD) throws Exception {
		// DB接続確立
		conn=super.getConnection();
		String minDeliveryPlanDate="";
		try {
			cstmt = conn.prepareCall("{Call PROC_DCOrderAutoSys_OrderCaseSplit_Update_Web (?,?)}");
			int pram = 1;
			cstmt.setString(pram++, sendData);
			cstmt.setString(pram++, userCD);
			// ストアドプロシージャ実行
			cstmt.execute();
			ResultSet rs = cstmt.getResultSet();
			int intReturn=0;
			while(rs.next()){
				intReturn=rs.getInt("RETURN");
			}
			if(cstmt.getMoreResults()&&intReturn==1){
				rs = cstmt.getResultSet();
				while(rs.next()){
					minDeliveryPlanDate=rs.getString("DeliveryPlanDate");
				}
			}
		}catch (Exception e) {
			logger.error(e.getMessage());
			e.printStackTrace();
			throw new Exception("false");
		} finally {
			// データベースの接続　CLOSE
			super.closeAll(conn, cstmt, null);
		}
		return minDeliveryPlanDate;
	}
}
