package jp.trial.DCOrderConsignment.action;

import java.io.IOException;

import jp.trial.DCOrderConsignment.common.BaseAction;
import jp.trial.DCOrderConsignment.service.RecommendScreenService;
import jp.trial.DCOrderConsignment.service.impl.RecommendScreenServiceImpl;

public class RecommendScreenAction extends BaseAction{
	private static final long serialVersionUID = -2207004973292092843L;
	/** Service */
	private RecommendScreenService recommendService;
	
	// ストラクター
	public RecommendScreenAction() throws IOException {
		super();
		recommendService = new RecommendScreenServiceImpl();
	}
	public String userCD;//ユーザーCD
	public String divStr;//選択したDIV
	public String storeHouseStr;//選択したセンター
	public String janStr;//入力したDIV
	public String dataFlg;//入力したDIV
	public String sendData;//推奨数ある商品のみ表示
	public String employeeCD;//社員CD  2018/08/15　zhangyunfeng追加
	public String week1;
	public String week2;
	public String week3;
	public String week4;
	public String fileName;
	public String jan;
	public String storeHouseCD;
	public String venderFlg;//コンサイメント発注商品除外 zhangyunfeng add 2019/06/05
	public String chkTimesFlg;//二回発注 zhangyunfeng add 2019/06/06
	
	/*
	 * DIVとセンターを取得する
	 * */
	public void getInitDataList() {
		String res = "";
		try {
			//社員CD  2019/05/04　zhangyunfeng追加
			//res = recommendService.getInitDataList(userCD);
			res = recommendService.getInitDataList(userCD,employeeCD);
			super.print.print(res);
		}catch(Exception e){
			// データをプリンター
			super.print.print("false");
		}finally {
			super.print.flush();
			super.print.close();
		}
	}
	
	/* 検索*/
	public void tableData_search() {
		String res = "";
		try {
			//社員CD  2019/05/04　zhangyunfeng追加
			//res = recommendService.tableData_search(divStr, storeHouseStr, janStr, userCD, dataFlg);
			res = recommendService.tableData_search(divStr, storeHouseStr, janStr, userCD, dataFlg,employeeCD,venderFlg,chkTimesFlg);
			super.print.print(res);
		}catch(Exception e){
			// データをプリンター
			super.print.print("false");
		}finally {
			super.print.flush();
			super.print.close();
		}
	}
	
	/*確定*/
	public void tableData_confirm() {
		String res = "";
		try {
			res=recommendService.tableData_confirm(sendData, userCD,employeeCD);
			super.print.print(res);
		}catch(Exception e){
			// データをプリンター
			super.print.print("false");
		}finally {
			super.print.flush();
			super.print.close();
		}
	}

	/**
	 * Description: 初期化：センター入出荷予定結果一覧
	 * <AUTHOR>
	 * Date: 2019/06/05
	 */
	public void GetInOutPlanInfo() {
		String res = "";
		try {
			res = recommendService.GetInOutPlanInfo(jan,storeHouseCD);
			super.print.print(res);
		}catch(Exception e){
			// データをプリンター
			super.print.print("false");
		}finally {
			super.print.flush();
			super.print.close();
		}
	}
}
