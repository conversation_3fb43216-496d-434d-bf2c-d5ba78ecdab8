/*Common Message*/
var Message = {
	 "sysTitle": "コンサイメント発注専用システム。"
	,"msg001": "初期化失敗しました。"
	,"msg002": "検索失敗しました。"
	,"msg003": "対象データがありません。"
	,"msg004": "チェックされたデータがありません。"
	,"msg005": "同じ混載区分「$1」の商品の混載ロット条件は同じに設定してください。画面未選択或は未表示分をご確認ください。"
	,"msg006": "同じ混載区分「$1」の商品の混載ロット条件は同じに設定してください。"
	,"msg007": "同じ混載区分「$1」の商品の重量OR体積が全部記入してないので、登録できません。"
	,"msg008": "同じ混載区分「$1」の重量情報が記入してないので、登録できません。画面未選択或は未表示分をご確認ください。"
	,"msg009": "同じ混載区分「$1」の重量情報が記入してないので、登録できません。"
	,"msg010": "同じ混載区分「$1」の体積情報が記入してないので、登録できません。画面未選択或は未表示分をご確認ください。"
	,"msg011": "同じ混載区分「$1」の体積情報が記入してないので、登録できません"
	,"msg012": "センター「$1」JAN「$2」の区分と参照JANが同時に入力情報です。入力してください。"
	,"msg013": "センター「$1」JAN「$2」の参照JANは商品マスタに存在しないです。ご確認ください。"
	,"msg014": "センター「$1」JAN「$2」の参照JANはJANと同じに設定しています。ご確認ください。"
	,"msg015": "同じ混載区分「$1」の商品の推奨方法は同じに設定してください。画面未選択或は未表示分をご確認ください。"
	,"msg016": "同じ混載区分「$1」の商品の推奨方法は同じに設定してください。"
	,"msg017": "JANは5000件まで入力してください。"
	,"msg018": "$1の日付入力が不正です。"
	,"msg019": "発注区分は「99：未設定」以外に設定してください。"
	,"msg020": "ベンダーを入力してください。"
	,"msg021": "センターを入力してください。"
	,"msg022": "DIVを入力してください。"
	,"msg023": "重複データが存在しています。ご確認してから再度登録してください。"
	,"msg024": "選択されたデータは設定しないので、登録できません。ご確認ください。"
	,"msg025": "納品不可開始日より、終了日は小さいです。ご確認お願いします。"
	,"msg026": "$1を入力してください。"
	,"msg027": "店舗Tree 検索失敗しました。"
	,"msg028": "登録失敗しました。"
	,"msg029": "登録成功しました。"
	,"msg030": "イベント名を入力してください。"
	,"msg031": "店舗を選択してください。"
	,"msg032": "DIVを選択してください。"
	,"msg033": "イベント開始日を入力してください。"
	,"msg034": "イベント終了日を入力してください。"
	,"msg035": "イベント開始日を正確に入力してください。"
	,"msg036": "イベント終了日を正確に入力してください。"
	,"msg037": "削除失敗しました。"
	,"msg038": "削除成功しました。"
	,"msg039": "イベント名リスト、店舗Tree、権限 検索失敗しました。"
	,"msg040": "ベンダーを選択してください。"
	,"msg041": "画面区分を選択してください。"
	,"msg042": "権限を選択してください。"
    ,"msg043": "$1失敗しました。"
	,"msg044": "$1成功しました。"
	,"msg045": "$1が必須入力情報です。入力してください。"
	,"msg046" : "運用開始日・センター・JANは重複しているデータが存在しています。ご確認してください。"
	,"msg047" : "適用開始日は過去のデータが存在しています。ご確認ください。"
	,"msg048" : "扱い対象ではない商品が存在しています。この商品を除外して登録するのはよろしいですか？"
	,"msg049" : "適用開始日は日付で入力してください。"
	,"msg050" : "$1は数字で入力してください。"
	,"msg051" : "違う混載区分のバンドル商品「$1」が存在しています。同じ混載区分に設定してください。"
	,"msg052" : "バンドル商品「$1」が存在しています。両方とも登録する場合、2重発注を発生するかもしれません。本当に登録しますか？"
	,"msg053" : "チェックされたデータがないので、登録できません。ご確認お願いします。"
	,"msg054": "店舗CD「$1」は2回を選択しています、ご確認ください。"
	,"msg055": "対象商品は商品センターマスタに存在しない。商品登録画面に登録してから店舗設定を行ってください。"
	,"msg056": "対象商品は調達情報に存在しないので、登録できません。ご確認ください。"
	,"msg057": "イベント開始日より、終了日は小さいです。ご確認お願いします。"
	,"msg058": "指定されたデータは発注推奨数計算している為、計算完了まで再計算できません。ご確認ください。"
	,"msg059": "取込ファイルのフォーマットが不正です。ご確認ください。"
	,"msg060": "既に発注しました。本当に発注案再作成しますか？"
	,"msg061": "$1は取込ファイルに存在しないので、取込できません。ご確認ください。"
	,"msg062": "本当に削除しますか？"
}


var showMsgBox = function(msg,inParam,okCallback) {
	for(var counter=0;counter<inParam.length;counter++){
		msg=msg.replace("$"+(counter+1),inParam[counter]);
	}
	$("#myMessageBody").html(msg);
	$("#msgSysBtnNo").hide();
	$("#myMessage").modal({backdrop: 'static', keyboard: false});
	
	//OK:メッセージ閉じる
	$("#msgSysBtnYes").unbind('click').bind("click",function(){
    	$("#myMessage").modal('hide');
    	//$(this).unbind('click');
    	okCallback.call();
    });
	//メッセージ表示
	$("#myMessage").on("shown.bs.modal",function(){
		$("#msgSysBtnYes").focus();
		$('.modal-backdrop').each(function() {
			$(this).attr("style","z-Index:1100; !important");
		});
    });
	//メッセージ閉じる
	$("#myMessage").on("hidden.bs.modal",function(){
		$('.modal-backdrop').each(function() {
    		$(this).attr("style","z-Index:1040; !important");
    	});
    });
}

var showConfMsg = function(msg,inParam,okCallback,cancelCallback) {
	for(var counter=0;counter<inParam.length;counter++){
		msg=msg.replace("$"+(counter+1),inParam[counter]);
	}
	$("#confMessageBody").html(msg);
	$("#msgConfSysBtnNo").show();
	$("#confMessage").modal({backdrop: 'static', keyboard: false});
	
	$("#msgConfSysBtnYes").unbind('click').bind("click",function(){
    	$("#confMessage").modal('hide');
    	//$(this).unbind('click');
    	okCallback.call();
    });
	$("#msgConfSysBtnNo").unbind('click').bind("click",function(){
    	$("#confMessage").modal('hide');
    	//$(this).unbind('click');
    	cancelCallback.call();
    });
	//メッセージ表示
	$("#confMessage").on("shown.bs.modal",function(){
		$("#msgConfSysBtnNo").focus();
		$('.modal-backdrop').each(function() {
			$(this).attr("style","z-Index:1100; !important");
		});
    });
	//メッセージ閉じる
	$("#confMessage").on("hidden.bs.modal",function(){
		$('.modal-backdrop').each(function() {
    		$(this).attr("style","z-Index:1040; !important");
    	});
    });
}
//削除用メッセージ
var showDelAskMsg = function(msg,inParam,okCallback,cancelCallback) {
	for(var counter=0;counter<inParam.length;counter++){
		msg=msg.replace("$"+(counter+1),inParam[counter]);
	}
	$("#delMessageBody").html(msg);
	$("#msgDelSysBtnNo").show();
	$("#delMessage").modal({backdrop: 'static', keyboard: false});

	$("#msgDelSysBtnYes").unbind('click').bind("click",function(){
		$("#delMessage").modal('hide');
		//$(this).unbind('click');
		okCallback.call();
	});
	$("#msgDelSysBtnNo").unbind('click').bind("click",function(){
		$("#delMessage").modal('hide');
		//$(this).unbind('click');
		cancelCallback.call();
	});
	//メッセージ表示
	$("#delMessage").on("shown.bs.modal",function(){
		$("#msgDelSysBtnNo").focus();
		$('.modal-backdrop').each(function() {
			$(this).attr("style","z-Index:1100; !important");
		});
	});
	//メッセージ閉じる
	$("#delMessage").on("hidden.bs.modal",function(){
		$('.modal-backdrop').each(function() {
			$(this).attr("style","z-Index:1040; !important");
		});
	});
}
//バンドル一致性メッセージ
var showBundleAskMsg = function(msg,inParam,okCallback,cancelCallback) {
	for(var counter=0;counter<inParam.length;counter++){
		msg=msg.replace("$"+(counter+1),inParam[counter]);
	}
	$("#bundleMessageBody").html(msg);
	$("#msgBundleSysBtnNo").show();
	$("#bundleMessage").modal({backdrop: 'static', keyboard: false});

	$("#msgBundleSysBtnYes").unbind('click').bind("click",function(){
		$("#bundleMessage").modal('hide');
		//$(this).unbind('click');
		okCallback.call();
	});
	$("#msgBundleSysBtnNo").unbind('click').bind("click",function(){
		$("#bundleMessage").modal('hide');
		//$(this).unbind('click');
		cancelCallback.call();
	});
	//メッセージ表示
	$("#bundleMessage").on("shown.bs.modal",function(){
		$("#msgBundleSysBtnNo").focus();
		$('.modal-backdrop').each(function() {
			$(this).attr("style","z-Index:1100; !important");
		});
	});
	//メッセージ閉じる
	$("#bundleMessage").on("hidden.bs.modal",function(){
		$('.modal-backdrop').each(function() {
			$(this).attr("style","z-Index:1040; !important");
		});
	});
}