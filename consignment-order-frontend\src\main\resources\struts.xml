<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.5//EN"
	"http://struts.apache.org/dtds/struts-2.5.dtd">
<struts>
	<constant name="struts.devMode" value="true" />
	<constant name="struts.action.extension" value="action,do"></constant>
	<package name="default" namespace="/" extends="struts-default" strict-method-invocation="false">

		<default-action-ref name="index" />
		<global-results>
			<result name="error">/WEB-INF/jsp/error.jsp</result>
		</global-results>
		<global-exception-mappings>
			<exception-mapping exception="java.lang.Exception"
				result="error" />
		</global-exception-mappings>
	</package>
	<!-- Add packages here -->
	<!-- login Action -->
	<package name="supportAction" namespace="/Page" extends="struts-default" strict-method-invocation="false">
		<action name="split_*" class="jp.trial.DCOrderConsignment.action.SplitDetailAction" 
			method="{1}">
		</action>
		<action name="menu_*" class="jp.trial.DCOrderConsignment.action.MenuMainAction" 
			method="{1}">
		</action>
		<action name="recommend_*" class="jp.trial.DCOrderConsignment.action.RecommendScreenAction" 
			method="{1}">
		</action>
<!--		<action name="excel_*" class="jp.trial.DCOrderConsignment.action.ExelAction"-->
<!--			method="{1}">-->
<!--		</action>-->
	</package>
</struts>