!function(){
    "use strict";
    try{
        if( $.fn.dataTable !== undefined ){
            $.fn.dataTable.ext.errMode = 'none'; //移除datatables警告
        }

        if( Dropzone !== undefined ){
            Dropzone.autoDiscover = false; //移除dropzonejs上传警告
        }

        if( $.validationEngine !== undefined ){
            $.validationEngine.defaults.promptPosition = 'topLeft'; //表单验证默认位置
        }

    }catch(e){

    }
    //菜单title
    $(function(){
        $('#sidebar .nav>li').each(function(i,d){
            if( $(d).find('.caret').length === 0 ){
                $(d).attr('title',$(d).find('a:first>span:first').text());
            }
        })
    })

    //左侧菜单折叠
    $(document).on('click','.sidebar .nav > .has-sub > a',function () {
        var a = $(this).next('.sub-menu'),
            b = '.sidebar .nav > li.has-sub > .sub-menu';
        0 === $('.page-sidebar-minified').length && ($(b).not(a).slideUp(250, function () {
            $(this).closest('li').removeClass('expand');
        }), $(a).slideToggle(250, function () {
            var a = $(this).closest('li');
            $(a).hasClass('expand') ? $(a).removeClass('expand') : $(a).addClass('expand');
        }));
    })

    //左侧二级菜单折叠
    $(document).on('click','.top-menu .nav > .has-sub .sub-menu li.has-sub > a,.sidebar .nav > .has-sub .sub-menu li.has-sub > a',function () {
        if (0 === $('.page-sidebar-minified').length) {
            var a = $(this).next('.sub-menu');
            $(a).slideToggle(250);
        }
    });

    //侧边栏折叠
    $(document).on('click','[data-click=sidebar-minify]',function (a) {
        a.preventDefault();
        var b = "page-sidebar-minified",
            c = "#page-container";
        $(c).hasClass(b) ? ($(c).removeClass(b), $('.scroll-container').css('overflow', 'auto')) : ($(c).addClass(b), $('.scroll-container').css('overflow', 'inherit'));
    });

    //loading显隐
    var loadingControl = function(s){
        if( s === 'hide' ){
            $('#page-loader').removeClass('in');
        }else{
            $('#page-loader').addClass('in');
        }
    };

    //菜单折叠
    var sidebarControl = function(s){
        $('.sidebar-minify-btn').click();
    };
    window.ui = {
        //显示
        loading:function(s){
            loadingControl(s)
        },
        sidebarFold:function(){
            sidebarControl()
        }
    }
}()