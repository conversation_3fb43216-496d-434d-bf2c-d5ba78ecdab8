/** DataGrid Creat*/
var TemplateGridAll = {
	tbhot1:null,
	colWidths:[60,60,80,170,180,130,140,110,110,0.001,0.001],
	columns: [
        {data: 'addBtn',    		renderer: "html",    readOnly:true},//全
        {data: 'chkFlg',    		type: 'checkbox',    checkedTemplate: '1', uncheckedTemplate: '0'},//全
        {data: 'mixCD',	    		type: 'text',     	 readOnly:true},//混載区分
        {data: 'center',	    	type: 'text',     	 readOnly:true},//センター
        {data: 'supplier',			type: 'text',     	 readOnly:true},//ベンダー
        {data: 'division',			type: 'text',     	 readOnly:true},//DIV
        {data: 'notDeliveryWeekDay',
            editor: "chosen",
            chosenOptions: {
            	multiple: true,
                data: [
                       {id:"日",label:"日"},
                       {id:"月",label:"月"},
                       {id:"火",label:"火"},
                       {id:"水",label:"水"},
                       {id:"木",label:"木"},
                       {id:"金",label:"金"},
                       {id:"土",label:"土"}
                       ]
            }
        },//納品不可曜日
        {data: 'notDeliveryBeginDate',
	    	type: 'date',
            dateFormat: 'YYYY/MM/DD',
	        allowEmpty: true,
	        datePickerConfig: {
	            firstDay: 1,
	            showWeekNumber: true,
	            numberOfMonths: 1,
	            disableDayFn: function(date) {
	        	    return new Date(date)<
		       		    new Date(new Date(RegistryGlobal.CreateDate.substring(0,10)));
	          },
	          i18n: {
			    previousMonth : 'Previous Month',
			    nextMonth     : 'Next Month',
			    months        : ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
			    weekdays      : ['日曜日','月曜日','火曜日','水曜日','木曜日','金曜日','土曜日'],
			    weekdaysShort : ['日','月','火','水','木','金','土']
			  }
	        }
        },//納品不可開始日
        {data: 'notDeliveryEndDate',
	    	type: 'date',
            dateFormat: 'YYYY/MM/DD',
	        allowEmpty: true,
	        datePickerConfig: {
	            firstDay: 1,
	            showWeekNumber: true,
	            numberOfMonths: 1,
	            disableDayFn: function(date) {
	            return new Date(date)<
		       		   new Date(new Date(RegistryGlobal.CreateDate.substring(0,10)));
	          },
	          i18n: {
			    previousMonth : 'Previous Month',
			    nextMonth     : 'Next Month',
			    months        : ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
			    weekdays      : ['日曜日','月曜日','火曜日','水曜日','木曜日','金曜日','土曜日'],
			    weekdaysShort : ['日','月','火','水','木','金','土']
			  }
	        }
        },//納品不可終了日
        {data: 'id',	type: 'text',     readOnly:true},//ID
        {data: 'centerSetID',	type: 'text',     readOnly:true}
	],
	isChecked: function(){
		if(!this.tbhot1){
			return false;
		}
		if("afterChg1"==chk1){
			RegistryGlobal.tableSourceDataAll = TemplateGridAll.tbhot1.getSourceData();
			for (var i=0; i<RegistryGlobal.tableSourceDataAll.length; i++) {
				if(RegistryGlobal.tableSourceDataAll[i].chkFlg!="1"){						
					return false;
				}
			}
			return true;
		}else{
			return chk1;
		}
	},
	//create grid
	CreatHotGrid: function(hot1grid,arrData){
		//まず対象内容クリア
		if(this.tbhot1){
			this.tbhot1.destroy();
			chk1=false;
		}
		this.tbhot1 = new Handsontable(document.getElementById(hot1grid),{
			height:$(".page-content").height()-200,
			rowHeaders: false,
			colHeaders: function(col){
							switch(col){
							case 0: return '追加';
							case 1:
								var txt = '<label class="checkbox-inline"><input id="chkAll1" onclick="chkAll1();" type="checkbox"';
								txt += TemplateGridAll.isChecked() ? 'checked="checked"' : '';
								txt += '/>全</label>';
								return txt;
							case 2: return '混載区分';	
							case 3: return 'センター';
							case 4: return 'ベンダー';
							case 5: return 'DIV';
							case 6: return '納品不可曜日';
							case 7: return '納品不可開始日';
							case 8: return '納品不可終了日';
							case 9: return 'ID';
							case 10: return 'centerSetID';
							}
						},
			colWidths: this.colWidths,
			columns: this.columns,
			overflow: scroll,
		    fillHandle:false,
		    wordWrap:false,
		    search: true,
		    minSpareRows:0,
		    columnSorting: true,
		    sortIndicator: true,
		    manualColumnResize: true,
			currentRowClassName: 'currentRow',
			cells: function (row, col, prop) {
				var cellProperties = {};
				cellProperties.renderer = styleRender1;
				return cellProperties;					
	    	},
	    	beforeChange : function(changes, source){
	    		if (source === 'loadData'||source ==='external') {
	    	        return; //don't save this change
	    	    }
	    		if (changes && changes[0].length) {
		    		for(var i=0; i<changes.length; i++){
		    			rowidx = changes[i][0];
		    			colName = changes[i][1];
		    			preValue = changes[i][2];
		    			newValue = changes[i][3];
			    		if(colName=="notDeliveryBeginDate"||colName=="notDeliveryEndDate"){
			    			if(newValue!=preValue){
			    			    TemplateGridAll.tbhot1.setDataAtRowProp(changes[i][0], changes[i][1], dateDeal(newValue));
			    			}
			    		}
		    		}
	    		}
	    	},
	    	afterChange: function(changes, source) {
	    		afterChg1(changes, source);
 			},
 			afterSelectionByProp: function(r, p, r2, p2) {
 				afterSel(r, p, r2, p2);
 			}
		});	
		chk1=false;
		this.tbhot1.loadData(arrData);
	    this.tbhot1.render();
	}		
}

var chk1;
var afterChg1 = function(changes, source){
	if (source === 'loadData'||source ==='external') {
        return; //don't save this change
    }
	if (changes && changes[0].length) {
		for(var i=0; i<changes.length; i++){
			rowidx = changes[i][0];
			colName = changes[i][1];
			preValue = changes[i][2];
			newValue = changes[i][3];

			RegistryGlobal.tableDataAll = TemplateGridAll.tbhot1.getData();
			RegistryGlobal.tableSourceDataAll = TemplateGridAll.tbhot1.getSourceData();
			var realRow = Handsontable.hooks.run(TemplateGridAll.tbhot1, 'modifyRow', rowidx);
			if("chkFlg"==colName){
				chk1 = "afterChg1";
				if("0"==newValue||"1"==newValue){
					RegistryGlobal.tableSourceDataAll[realRow].chkFlg=newValue;
				}else{
					RegistryGlobal.tableSourceDataAll[realRow].chkFlg=preValue;
				}
			}else if(preValue!=newValue && ("notDeliveryWeekDay"==colName || "notDeliveryBeginDate"==colName|| "notDeliveryEndDate"==colName)){
			    RegistryGlobal.tableSourceDataAll[realRow].chkFlg="1";
			}
			TemplateGridAll.tbhot1.render();
		}
	}
}
var chkAll1 = function(){
	if(chk1==true){
		chk1 = false;
	}else{
		chk1 = true;
	}
	if(TemplateGridAll.tbhot1.getSourceData().length>0){		
		$.each(RegistryGlobal.showAllList, function(i, data){ 
				data.chkFlg = chk1?"1":"0";
		});
	}
	TemplateGridAll.tbhot1.render();
}

var styleRender1=function(instance, td, row, col, prop, value, cellProperties){
	var realRow = Handsontable.hooks.run(TemplateGridAll.tbhot1, 'modifyRow', row);
	var color;
	if("addBtn"==prop){//html
		td.innerHTML = value;
		td.style.textAlign = "center";
	}else if("chkFlg"==prop){//checkbox
		Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
		td.style.textAlign = "center";
	}else if("notDeliveryWeekDay"==prop){//checkbox
		if(value == null){
			Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
			td.style.textAlign = "left";
			td.style.backgroundColor = "#FFFF99";
		}else{
			var selectedId;
		    var optionsList = cellProperties.chosenOptions.data;
		    if(typeof optionsList === "undefined" || typeof optionsList.length === "undefined" || !optionsList.length) {
		        Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
		        return td;
		    }
		    var values = (value + "").split(",");
		    var value = [];
		    for (var index = 0; index < optionsList.length; index++) {
		        if (values.indexOf(optionsList[index].id + "") > -1) {
		            selectedId = optionsList[index].id;
		            value.push(optionsList[index].label);
		        }
		    }
		    value = value.join(", ");
		    Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
		    td.style.textAlign = "left";
			td.style.backgroundColor = "#FFFF99";
		    if(TemplateGridAll.tbhot1!=null && value==""){
		    	TemplateGridAll.tbhot1.setDataAtCell(row, col, null);
			}
		}
	}else if("notDeliveryBeginDate"==prop||"notDeliveryEndDate"==prop){//checkbox
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		if(!value||isDate(convertToCorrectDate(value))){				
			$(arguments[1]).removeClass("htInvalid");
		}
		if(value && !isDate(value)){
			value = "";
		}else if( value && 
			(new Date(new Date(value))<new Date(new Date(RegistryGlobal.CreateDate.substring(0,10))))
		){
			color = "#EEEEEE";
		}
		if(!color){
			color = '#FFFF99';
		}
		td.style.backgroundColor = color;
	}else if("division"==prop){
		if(RegistryGlobal.showAllList[realRow]&&RegistryGlobal.showAllList[realRow].division){
			td.title = RegistryGlobal.showAllList[realRow].division;
		}
		Handsontable.renderers.TextRenderer.apply(this, arguments);
	}
	else{
		Handsontable.renderers.TextRenderer.apply(this, arguments);
	}
	return td;
}

var afterSel = function(r, p, r2, p2){
	if("addBtn"==p && r==r2 && p==p2){//追加
		var realRow = Handsontable.hooks.run(TemplateGridAll.tbhot1, 'modifyRow', r);
		var rowData = TemplateGridAll.tbhot1.getSourceDataAtRow(realRow);
		TemplateGridAll.tbhot1.alter('insert_row', r+1,1);
		var tableSourceData= TemplateGridAll.tbhot1.getSourceData();
		tableSourceData[r+1].addBtn=rowData.addBtn;
		tableSourceData[r+1].chkFlg=rowData.chkFlg;
		tableSourceData[r+1].mixCD=rowData.mixCD;
		tableSourceData[r+1].center=rowData.center;
		tableSourceData[r+1].supplier=rowData.supplier;
		tableSourceData[r+1].division=rowData.division;
		TemplateGridAll.tbhot1.render();
	}
	
}


