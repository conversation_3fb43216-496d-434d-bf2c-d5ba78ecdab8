/** DataGrid Creat*/
var TemplateGrid = {
		tbhot:null,
		colWidths:[70,200,130,140,140,160,160,120,120],
		columns: [
            {data: 'bizTalksID',	type: 'text',	readOnly:true},//商談ID
            {data: 'bizTalksTitle',	type: 'text',   readOnly:true},//タイトル
            {data: 'author',		type: 'text',   readOnly:true},//提出者
            {data: 'registered',	type: 'text',   readOnly:true},//提出日
            {data: 'approved',		type: 'text',   readOnly:true},//承認日
            {data: 'cutProduct',	type: 'text',   readOnly:true},//CUT商品
            {data: 'newProduct',	type: 'text',   readOnly:true},//新商品
            {data: 'cutStorehouse',	type: 'text',   readOnly:true},//CUTセンター
            {data: 'newStorehouse',	type: 'text',   readOnly:true}//新センター
		],
		//create grid
		CreatHotGrid: function(hotgrid,arrData){
			//まず対象内容クリア
			if(this.tbhot){
				this.tbhot.destroy();
			}
			this.tbhot = new Handsontable(document.getElementById(hotgrid),{
				height:$(".page-content").height()-150,
				rowHeaders: false,
				colHeaders: function(col){
					switch(col){
						case 0: return '商談ID';	
						case 1: return 'タイトル';	
						case 2: return '提出者';
						case 3: return '提出日';
						case 4: return '承認日';
						case 5: return 'CUT商品';
						case 6: return '新商品';
						case 7: return 'CUTセンター';
						case 8: return '新センター';
					}
				},
				colWidths: this.colWidths,
				columns: this.columns,
				overflow: scroll,
			    fillHandle:false,
			    wordWrap:false,
			    search: true,
			    startRows: 27,
			    columnSorting: true,
			    sortIndicator: true,
			    manualColumnResize: true,
				currentRowClassName: 'currentRow',
			    cells : function(row, col, prop) {
					var cellProperties = {};
					cellProperties.renderer = styleRender;
					return cellProperties;
				}
			});				
			this.tbhot.loadData(arrData);
		    this.tbhot.render();
		}		
}
var styleRender = function(instance, td, row, col, prop, value, cellProperties) {
	Handsontable.renderers.TextRenderer.apply(this, arguments);
	td.title = value;
	return td;
}
