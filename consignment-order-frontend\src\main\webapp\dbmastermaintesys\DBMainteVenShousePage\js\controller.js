/**
 * @description: DBマスタメンテナンス-展開センター設定画面
 * @author: 10047496 zhangyunfeng
 * @date: 2018/10/14
 */
var RegistryGlobal = {
	userCD: sessionStorage.getItem("userCD"),
	divMixList:[],//混載
	divCenterList:[],//センター
	divSupplierList:[],
	CreateDate: new Date().toISOString(),//日付転�?
	tableData: [],
	tableSourceData: [],
	showList:[],
	cArr:[],
	vArr:[],
	dArr:[],
	init: null,
	repeat:0
}

$(document).ready(function () {
	$("#loadMask").show();
	$(document).ajaxStart(function(){
		$("#loadMask").show();
    }).ajaxStop(function(){
    	$("#loadMask").hide();
    });
	//INIT
	DivMultiLoadInit();
	
	//ベンダー用画面Grid表示する
	getInitDataList();
	
	//検索
	$("#btn_search").on("click",function(){
		tableData_search();
	});
	//削除
	$("#btn_Delete").on("click",function(){
		tableData_delete();
	});
	//保�?
	$("#btn_save").on("click",function(){
		tableData_Save();
	});

});

var DivMultiLoadInit=function(){
	$('#divCenterMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名�?',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全センター',
		nonSelectedText: '全て',
		nSelectedText: 'センター',
		numberDisplayed:1
	});
	
	$('#divSupplierMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名�?',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全ベンダー',
		nonSelectedText: '全て',
		nSelectedText: ' ベンダー',
		numberDisplayed:1
	});
}
//初期�?
var getInitDataList=function(){
	
	$.ajax({
		url:`${SystemManage.shiniseAddress}VenShouseController/getInitList`, //getCenterList',
		type: 'GET',
		data:{userCD:sessionStorage.getItem("employeeCD"),//社員CD
	        venderCD:sessionStorage.getItem("userCD")//ベンダーCD
	    },
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg001,[],function(){});//初期化失敗しました�?
			}else{				
				//var result = eval('('+response+')');
				var result = JSON.parse(response);
				RegistryGlobal.divCenterList = result[0].CenterList;
				RegistryGlobal.divSupplierList = result[0].SupplierList;
				RegistryGlobal.cArr = result[0].CenterList;
				RegistryGlobal.vArr = result[0].SupplierList;
				RegistryGlobal.dArr = result[0].DivisionList;
				//センター
				bindData(RegistryGlobal.divCenterList,"centerCD","centerName","divCenterMulti","");
				//センター
				bindData(RegistryGlobal.divSupplierList,"supplierCD","supplierName","divSupplierMulti","");
				getTemplateMainGrid(RegistryGlobal.cArr, RegistryGlobal.vArr, RegistryGlobal.dArr);
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg001,[],function(){});//初期化失敗しました�?
		}
	});
}

//検索
var tableData_search = function(){
	RegistryGlobal.init = "search";
	chk = "";
	var supplierStr;//ベンダー
	var centerStr;
	if (!$("#divSupplierMulti").val()) {
		supplierStr = sessionStorage.getItem("userCD")//ベンダーCD
	} else {
		supplierStr = $("#divSupplierMulti").val().join(",");
	}
	if (!$("#divCenterMulti").val()) {//センターCD
		centerStr="";
	} else {
		centerStr = $("#divCenterMulti").val().join(",");
	}
	$.ajax({
		url:`${SystemManage.shiniseAddress}VenShouseController/getSearchInfo`,
		type:'POST',
		data:{supplierStr: supplierStr, centerStr:centerStr, userCD:sessionStorage.getItem("employeeCD")},
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg002,[],function(){});//検索失敗しました�?
			}else{				
				var result = JSON.parse(response);
				RegistryGlobal.showList = result;
				TemplateGrid.CreatHotGrid('MainGrid',RegistryGlobal.showList);
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg002,[],function(){});//検索失敗しました�?
		}
	});
}

//削除
var tableData_delete = function(){
	var tableData = TemplateGrid.tbhot.getData();
	var sendData = "";
	chk = "";
	var flag = 1;
	var count = 0;
	var saveDatas = [];
	for(var i=0;i<tableData.length;i++){
		if("1"==tableData[i][0]){
			if (tableData[i][1]==null || tableData[i][1]=="") {
				showMsgBox(Message.msg020,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"supplier");});
				return;
			}
			if (tableData[i][2]==null || tableData[i][2]=="") {
				showMsgBox(Message.msg021,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"center");});
				return;
			}
			var data = [];
			data.push(tableData[i][1]);
			data.push(tableData[i][2]);
			data.push("");
			data.push("");
			
			saveDatas.push(data.join(","));
		}
	}
	if (saveDatas.length == 0){
		showMsgBox(Message.msg004,[],function(){});//チェ�?クされたデータがありません�?
	} else {
		sendData = saveDatas.join(";");
		$.ajax({
			url:`${SystemManage.shiniseAddress}VenShouseController/getSave`,
			headers: {
	        	"user-code": sessionStorage.getItem("userCode") || ""
	    	},
			type:'POST',
			data:{
				sendData: sendData,
				userCD:sessionStorage.getItem("employeeCD"),//社員CD
		        venderCD:sessionStorage.getItem("userCD"),//ベンダーCD
		        flag:flag
		    },
			success:function(response){
				if("false"==response){
					showMsgBox(Message.msg037,[],function(){});//削除失敗しました�?
				}else{
					tableData_search();
				}
			},
			error:function(response, textStatus){
				showMsgBox(Message.msg037,[],function(){});//削除失敗しました�?
			}
		});
	}
}
//保�?
var tableData_Save = function(){
	var tableData = TemplateGrid.tbhot.getData();
	var sendData = "";
	chk = "";
	var flag = 0;
	var count = 0;
	var wrongful = false;
	var saveDatas = [];
	for(var i=0;i<tableData.length;i++){
		if("1"==tableData[i][0]){			
			if (tableData[i][1]==null || tableData[i][1]=="") {
				showMsgBox(Message.msg020,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"supplier");});
				return;
			}
			if (tableData[i][2]==null || tableData[i][2]=="") {
				showMsgBox(Message.msg021,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"center");});
				return;
			}
			if (tableData[i][3]==null || tableData[i][3]=="") {
				showMsgBox(Message.msg022,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"division");});
				return;
			}
			
			for(var j = 0; j < saveDatas.length; j++){
	            if (tableData[i][1] == saveDatas[j].split(",")[0]&& tableData[i][2]==saveDatas[j].split(",")[1]) {
	        	    showMsgBox(Message.msg023,[],function(){});//重�?な�?ータチェ�?ク
					return;
	            }
		    }
			var data = [];
			data.push(tableData[i][1]);
			data.push(tableData[i][2]);
			data.push(tableData[i][3].replaceAll(",", "-"));
			data.push(tableData[i][4]);
			
			saveDatas.push(data.join(","));
		}
	}
	if (saveDatas.length == 0){
		showMsgBox(Message.msg004,[],function(){});//チェ�?クされたデータがありません�?
	} else {
		sendData = saveDatas.join(";");
		$.ajax({
			url:`${SystemManage.shiniseAddress}VenShouseController/getSave`,
			type:'POST',
			headers: {
	        	"user-code": sessionStorage.getItem("userCode") || ""
	    	},
			data:{
				sendData: sendData,
				userCD:sessionStorage.getItem("employeeCD"),//社員CD
		        venderCD:sessionStorage.getItem("userCD"),//ベンダーCD
		        flag:flag
		    },
			success:function(response){
	            if("false"==response){
					showMsgBox(Message.msg028,[],function(){});//登録失敗しました�?
				}else{
					tableData_search();
				}
			},
			error:function(response, textStatus){
				showMsgBox(Message.msg028,[],function(){});//検索失敗しました�?
			}
		});	
	}
}

/////////////////////////////////////////////////////////
//bindDataとonchange事件
//data     Json
//valuecol �?目CDString
//labelcol �?目�? String
//id       String
//multiple  "all":全選(�?数選択�?�み有効) 
//        "single":初期に選択�??目
//        Array:選択したデータ  []:すべて選択しな�?
////////////////////////////////////////////////////////
function bindData(filtdata ,valuecol, labelcol, id, multiple){ 
 var data = filtdata;
 var options=[];
 for(var i=0;i<data.length;i++){
	   options.push({
		   label:data[i][valuecol]+"-"+data[i][labelcol],
		   title:data[i][labelcol],
		   value:data[i][valuecol]
	   });
 }
 $('#'+id).multiselect('dataprovider', options);
 if(multiple=="all"){
	 	//全選  及び　onChange trigger
	 	$('#'+id).multiselect('selectAll',false,true);
	 	$('#'+id).multiselect('updateButtonText');				   
 } else if(multiple=="single"){
	 	//第一の項目選択　及び　onChange trigger
	 	$('#'+id).multiselect('select',options[0].value,true);			     
 } else {
	 	//選択設定   及び　onChange trigger
	 $('#'+id).multiselect('select',multiple,true);
 }
}
