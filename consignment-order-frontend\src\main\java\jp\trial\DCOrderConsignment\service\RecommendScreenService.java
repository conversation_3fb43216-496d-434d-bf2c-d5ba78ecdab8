package jp.trial.DCOrderConsignment.service;

public interface RecommendScreenService {
	/** DIVとセンターを取得する*/
	public String getInitDataList(String userCD,String employeeCD) throws Exception;

	/** 検索*/
	public String tableData_search(String divStr, String storeHouseStr, String janStr, String userCD, String dataFlg,String employeeCD,String venderFlg,String chkTimesFlg) throws Exception;

	/** 確定 */
	public String tableData_confirm(String sendData, String userCD,String employeeCD) throws Exception;

	/**センター入出荷予定結果一覧*/
	public String GetInOutPlanInfo(String jan,String storeHouseCD) throws Exception;
}