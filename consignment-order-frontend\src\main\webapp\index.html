<!DOCTYPE html>
<html lang="ja">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta http-equiv ="proma" content="no-cache"/>
	<meta http-equiv="cache-control" content="no-cache" />
	<meta http-equiv="expires" content="0" />
	<title>ログイン</title>
	<link rel="icon" type="image/png/ico" href="Common/favicon.ico">
	<script src="Common/jquery/jquery.min.js" type="text/javascript"></script>
	<script>document.write("<s"+"cript src='Common/version.js?ver="+Math.random()+"'></scr"+"ipt>"); </script>
	<script>document.write("<s"+"cript src='Common/common.js?ver="+sysversion+"'></scr"+"ipt>");</script>
</head>
<body>
    <script type="text/javascript">
	    var paramsString = window.location.search.substr(1),
	 	paramsArray = paramsString.split("&"),
	 	params = {};
		for (var i = 0; i < paramsArray.length; i++) {
		    var tmpArray = paramsArray[i].split("=");
		    params[tmpArray[0]] = tmpArray[1];
		}
		if(params["usercd"] != null||params["employeecd"] != null){
			sessionStorage.setItem("userCD",params["usercd"]== null?"":params["usercd"]);//ベンダーCD
			sessionStorage.setItem("employeeCD",params["employeecd"]==null?"":params["employeecd"]);//社員CD
			//ユーザー名取得
			$.ajax({
				url:'Page/menu_getLoginInfo.action',
				type:'POST',
				data:{
					login_id:sessionStorage.getItem("userCD")//ベンダーCD
			        ,login_employee:sessionStorage.getItem("employeeCD")//社員CD
			    },
			    success:function(response){
			    	if(response=="false"&&params["usercd"] != null){//ベンダー別はTrialLink画面へ変換する
			    		window.location.href="https://tralink.trial-net.co.jp/TrialLink/login.aspx";
			    	}else if(response=="false"&&params["employeecd"] != null){
			    		location.href="ErrorPage/error.html";//権限がありません
			    	}else{
				    	var result = JSON.parse(response);
				    	if(result && result[0]&&(result[0].supplierCD!= null||result[0].employeeCode!= null)){
				    		var decname = decodeURI(result[0].supplierName== null?"":result[0].supplierName);
							var decEmployeeName = decodeURI(result[0].employeeName== null?"":result[0].employeeName);
							sessionStorage.setItem("userName",decname);
							sessionStorage.setItem("employeeName", decEmployeeName);
							getSidebarMenuCheck();
				    	}else{
				    		if(params["usercd"] != null&&params["usercd"]!=""){
					    		window.location.href="https://tralink.trial-net.co.jp/TrialLink/login.aspx";
					    	}else {
					    		location.href="ErrorPage/error.html";//権限がありません
					    	}
				    	}
			    	}
			    },
			    error:function(){
			    	location.href="MenuPage/index.html";
			    }
			});
		}else{
			window.location.href="https://tralink.trial-net.co.jp/TrialLink/login.aspx";
		}
		//メニュー存在するか判断
		var getSidebarMenuCheck= function(){
			$.ajax({
				url:'Page/menu_getSidebarMenuSelList.action',
				type:'POST',
				data:{
					login_id:sessionStorage.getItem("userCD")//ベンダーCD
			        ,login_employee:sessionStorage.getItem("employeeCD")//社員CD
			    },
			    success:function(response){
			    	if(response=="false"){
			    		window.location.href="https://tralink.trial-net.co.jp/TrialLink/login.aspx";
			    	}else{
				    	var result = eval("("+response+")");
				    	if(!result || !result[0] || result[0].result || result[0].result=="false"){
				    		location.href="MenuPage/index.html";
				    	}else{
			    			if(result[0].length==1&&sessionStorage.getItem("userCD") != null && sessionStorage.getItem("userCD")!="")
			    			{
			    				location.href="/DBOrderSupport/index.html?usercd="+sessionStorage.getItem("userCD")+"&username="+encodeURIComponent(sessionStorage.getItem("userName"));
			    			}
			    			else
			    			{
			    				location.href="MenuPage/index.html";
			    			}
				    	}
			    	}
			    },
			    error:function(){
			    	location.href="MenuPage/index.html";
			    }
			});
		}
    </script>
</body>
</html>
