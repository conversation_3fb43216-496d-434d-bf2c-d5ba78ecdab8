package jp.trial.DCOrderConsignment.model;

public class SplitDetailModel {
	public String ChkFlg;
	public String Times;
	public String OrderCaseDetailID;
	public String SplitNO;
	public String MixCD;
	public String JAN;
	public String ProductName;
	public String SpecName;
	public String OrderUnitQty;
	public String OrderUnit;
	public String LotUnit;
	public String Terms;
	public String OrderPlanQtyUpdate;
	public String DeliveryPlanDateUpdate;
	public String OrderPlanQtyUpdate_Old;
	public String DeliveryPlanDateUpdate_Old;
	public String SplitNO_Old;
	public String TermsCD;
	public String InnerCaseQty;
	public String CSlot;
	public String MixLotUnit;
	public String CSLotUnit;
	public String SlipSplitQty;
	public String DayMaxQty;
	public String ProLotQtyCheck;
	public String CaseMeasurement;
	public String CaseWeight;
	public String SplitMesure;
	public String SplitWeight;
	public String Measurement;
	public String MaxWeight;
	public String AutoSplitFlg;
	public String NotDeliveryCheck;
	public String CaseQty;

	public String getChkFlg() {
		return ChkFlg;
	}
	public void setChkFlg(String chkFlg) {
		ChkFlg = chkFlg;
	}
	public String getTimes() {
		return Times;
	}
	public void setTimes(String times) {
		Times = times;
	}
	public String getOrderCaseDetailID() {
		return OrderCaseDetailID;
	}
	public void setOrderCaseDetailID(String orderCaseDetailID) {
		OrderCaseDetailID = orderCaseDetailID;
	}
	public String getSplitNO() {
		return SplitNO;
	}
	public void setSplitNO(String splitNO) {
		SplitNO = splitNO;
	}
	public String getMixCD() {
		return MixCD;
	}
	public void setMixCD(String mixCD) {
		MixCD = mixCD;
	}
	public String getJAN() {
		return JAN;
	}
	public void setJAN(String jAN) {
		JAN = jAN;
	}
	public String getProductName() {
		return ProductName;
	}
	public void setProductName(String productName) {
		ProductName = productName;
	}
	public String getSpecName() {
		return SpecName;
	}
	public void setSpecName(String specName) {
		SpecName = specName;
	}
	public String getOrderUnitQty() {
		return OrderUnitQty;
	}
	public void setOrderUnitQty(String orderUnitQty) {
		OrderUnitQty = orderUnitQty;
	}
	public String getOrderUnit() {
		return OrderUnit;
	}
	public void setOrderUnit(String orderUnit) {
		OrderUnit = orderUnit;
	}
	public String getLotUnit() {
		return LotUnit;
	}
	public void setLotUnit(String lotUnit) {
		LotUnit = lotUnit;
	}
	public String getTerms() {
		return Terms;
	}
	public void setTerms(String terms) {
		Terms = terms;
	}
	public String getOrderPlanQtyUpdate() {
		return OrderPlanQtyUpdate;
	}
	public void setOrderPlanQtyUpdate(String orderPlanQtyUpdate) {
		OrderPlanQtyUpdate = orderPlanQtyUpdate;
	}
	public String getDeliveryPlanDateUpdate() {
		return DeliveryPlanDateUpdate;
	}
	public void setDeliveryPlanDateUpdate(String deliveryPlanDateUpdate) {
		DeliveryPlanDateUpdate = deliveryPlanDateUpdate;
	}
	
	public String getOrderPlanQtyUpdate_Old() {
		return OrderPlanQtyUpdate_Old;
	}
	public void setOrderPlanQtyUpdate_Old(String orderPlanQtyUpdate_Old) {
		OrderPlanQtyUpdate_Old = orderPlanQtyUpdate_Old;
	}
	public String getDeliveryPlanDateUpdate_Old() {
		return DeliveryPlanDateUpdate_Old;
	}
	public void setDeliveryPlanDateUpdate_Old(String deliveryPlanDateUpdate_Old) {
		DeliveryPlanDateUpdate_Old = deliveryPlanDateUpdate_Old;
	}
	public String getSplitNO_Old() {
		return SplitNO_Old;
	}
	public void setSplitNO_Old( String splitNO_Old ){
		SplitNO_Old= splitNO_Old;
    }
	public String getTermsCD() { 
		return TermsCD;
	}
	public void setTermsCD( String termsCD){ 
		TermsCD= termsCD;
	}
	public String getInnerCaseQty() { 
		return InnerCaseQty;
	}
	public void setInnerCaseQty( String innerCaseQty ){ 
		InnerCaseQty= innerCaseQty;
	}
	public String getCSlot() { 
		return CSlot;
	}
	public void setCSlot( String cSlot){
		CSlot= cSlot;
	}
	public String getMixLotUnit() { 
		return MixLotUnit;
	}
	public void setMixLotUnit( String mixLotUnit){ 
		MixLotUnit= mixLotUnit;
	}
	public String getCSLotUnit() { 
		return CSLotUnit;
	}
	public void setCSLotUnit( String cSLotUnit){ 
		CSLotUnit= cSLotUnit;
	}
	public String getSlipSplitQty() { 
		return SlipSplitQty;
	}
	public void setSlipSplitQty( String slipSplitQty ){ 
		SlipSplitQty= slipSplitQty;
	}
	public String getDayMaxQty() { 
		return DayMaxQty;
	}
	public void setDayMaxQty( String dayMaxQty){ 
		DayMaxQty= dayMaxQty;
	}
	public String getProLotQtyCheck() { 
		return ProLotQtyCheck;
	}
	public void setProLotQtyCheck( String proLotQtyCheck ){
		ProLotQtyCheck= proLotQtyCheck;
	}
	public String getCaseMeasurement() { 
		return CaseMeasurement;
	}
	public void setCaseMeasurement( String caseMeasurement ){ 
		CaseMeasurement= caseMeasurement;
	}
	public String getCaseWeight() { 
		return CaseWeight;
	}
	public void setCaseWeight( String caseWeight){ 
		CaseWeight= caseWeight;
	}
	public String getSplitMesure() { 
		return SplitMesure;
	}
	public void setSplitMesure( String splitMesure ){ 
		SplitMesure= splitMesure;
	}
	public String getSplitWeight() { 
		return SplitWeight;
	}
	public void setSplitWeight( String splitWeight ){ 
		SplitWeight= splitWeight;
	}
	public String getMeasurement() { 
		return Measurement;
	}
	public void setMeasurement( String measurement ){ 
		Measurement= measurement;
	}
	public String getMaxWeight() { 
		return MaxWeight;
	}
	public void setMaxWeight( String maxWeight ){ 
		MaxWeight= maxWeight;
	}
	public String getAutoSplitFlg() { 
		return AutoSplitFlg;
	}
	public void setAutoSplitFlg( String autoSplitFlg ){ 
		AutoSplitFlg= autoSplitFlg;
	}
	public String getNotDeliveryCheck() {
		return NotDeliveryCheck;
	}
	public void setNotDeliveryCheck(String notDeliveryCheck ){
		NotDeliveryCheck= notDeliveryCheck;
	}
	public String getCaseQty() {
		return CaseQty;
	}
	public void setCaseQty(String caseQty) {
		CaseQty = caseQty;
	}
}
