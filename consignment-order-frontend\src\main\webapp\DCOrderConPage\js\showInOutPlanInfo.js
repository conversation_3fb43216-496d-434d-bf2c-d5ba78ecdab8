/**
 * センター入出荷予定結果一覧
 * zhangyunfeng
 * 2019/06/03
*/
var inOutPlanInfo = (function ($) {
    var id=Math.floor(Math.random() * 1000);
    var $dialog = $(
    '<div id="inOutPlanInfo-dialog-module'+id+'" class="modal fade" style="top:10px;" tabindex="-1">' +
        '<div id="inOutPlanInfo-modal'+id+'" class="modal-dialog" style="width: 500px;" >' +
            '<div class="modal-content" id="inOutPlanInfo-modal-content'+id+'">' +
                '<div class="modal-header" style="padding:10px 15px;">' +
                    '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '<h4 class="modal-title" style="display:inline-block;">センター入出荷予定結果一覧</h4>' +
                '</div>' +
                '<div class="modal-body" style="padding-top:5px;padding-bottom:5px;">' +
                    '<div class="panel panel-default" style="margin-bottom:10px;">' +
                        '<div class="panel-body" style="padding: 0px; height: 200px; overflow: auto;">' +
                            '<div id="inOutPlanInfoGrid'+id+'" style="height:200px;overflow:hidden;z-index:10;"></div>'+
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>' +
    '</div>'
    );
    
    var $fun = {
        dialog: function(){
            return $dialog;
        },
        inOutPlanTbhot:null,
        styleRender:function(instance, td, row, col, prop, value, cellProperties){
            if("slipNo"==prop||"deliveryPlanDate"==prop ||"deliveryPlanQty"==prop || "saleDays"==prop) {
                td.style.textAlign = "right";
            }else{
                td.style.textAlign = "left";
            }
            if("deliveryPlanQty"==prop){
                Handsontable.renderers.NumericRenderer.apply(this, arguments);
            }else{
                Handsontable.renderers.TextRenderer.apply(this, arguments);
            }
            td.title = value;
            return td;
        },
        createTbhot: function(data){
            var datatableCate=document.getElementById('inOutPlanInfoGrid'+id);
            $fun.inOutPlanTbhot = new Handsontable(datatableCate,{
                //data:data,
                colHeaders: function(col){
                    switch(col){
                        case 0: return 'センター';
                        case 1: return '区分';
                        case 2: return '伝票NO';
                        case 3: return '入出荷日';
                        case 4: return '数量';
                        case 5: return '消化日数';
                        case 6: return 'avgDigestQty';
                        case 7: return 'storeHouseCD';
                        case 8: return 'centerCD';
                        case 9: return 'jAN';
                    }
                },
                colWidths: [100,50, 70, 80, 80,70,0.01,0.01,0.01,0.01],
                fillHandle:false,
                wordWrap:false,
                minCols: 9,
                search: true ,
                contextMenu: false,
                viewportColumnRenderingOffset: 9,
                currentRowClassName: 'currentRow',
                columns : [
                    {data: 'storeHouseName',type: 'text',readOnly:true},
                    {data: 'planName',type: 'text',readOnly:true},
                    {data: 'slipNo',type: 'text',readOnly:true},
                    {data: 'deliveryPlanDate',type: 'text',readOnly:true},
                    {data: 'deliveryPlanQty',type: 'numeric', format: '0,0',readOnly:true},
                    {data: 'saleDays',type: 'text',readOnly:true},
                    {data: 'avgDigestQty',type: 'text',readOnly:true},
                    {data: 'storeHouseCD',type: 'text',readOnly:true},
                    {data: 'centerCD',type: 'text',readOnly:true},
                    {data: 'jAN',type: 'text',readOnly:true}
                ],
                cells: function (row, col, prop) {
                    var cellProperties = {};
                    cellProperties.renderer = $fun.styleRender;
                    return cellProperties;
                }
            });
            $fun.inOutPlanTbhot.loadData(data);
            $fun.inOutPlanTbhot.render();
        },
        init: function(obj){/*モジュール初期入口*/
            $dialog.appendTo('body');
            $fun.param = obj;
            $fun.dialog().on("hidden.bs.modal",function(){
                $fun.destroy();
            });
            $fun.dialog().on("shown.bs.modal",function(){
            });
            $fun.dialog().on("show.bs.modal",function(){
                $fun.adjust();
            });
            $fun.dialog().on("hide.bs.modal",function(){
            });
           
            $("#inOutPlanInfo-dialog-module"+id).scroll(function(){
                $fun.adjust();
            });
            $("#inOutPlanInfo-dialog-module"+id).resize(function(){
                $fun.adjust();
            });
        },
        show: function(){
            $dialog.modal({backdrop: true, keyboard: true});//{backdrop: 'static', keyboard: false}
            $fun.GetInOutPlanInfo();
        },
        GetInOutPlanInfo: function(obj){//初期化
            $.ajax({
                url:`${SystemManage.shiniseAddress}Recommend/getInOutPlanInfo`,
                type:'POST',
                data:{jan:$fun.param.JAN,storeHouseCD:$fun.param.StoreHouseCD},
                headers: {
                    "user-code": sessionStorage.getItem("userCode") || ""
                },
                success:function(response){
                    if("false"==response){
                        showMsgBox(Message.msg001,[],function(){});
                    }else{
                        var result = JSON.parse(response);
                        $fun.createTbhot(result);
                    }
                },
                error:function(response, textStatus){
                    showMsgBox(Message.msg001,[],function(){});
                }
            });
        },
        hide: function(){
            $dialog.modal('hide');
        },
        destroy: function(){
            $("#inOutPlanInfo-dialog-module" + id).remove();
        },
        adjust:function() {
            if( $('#inOutPlanInfo-modal'+id).size()){
                if(($fun.param.PosY+300)>$(window).height()){
            		$('#inOutPlanInfo-modal'+id).css("top",$fun.param.PosY-330);
            	}else{
            		$('#inOutPlanInfo-modal'+id).css("top",$fun.param.PosY-20);
            	}
                if(($fun.param.PosX+220)>$(window).width()){
                    $('#inOutPlanInfo-modal'+id).css("left",($(window).width()/2-$fun.param.PosX+200)*(-1));
                }else{
                    $('#inOutPlanInfo-modal'+id).css("left",($(window).width()/2-$fun.param.PosX)*(-1));
                }
            }
        }
    };
    return $fun;
});