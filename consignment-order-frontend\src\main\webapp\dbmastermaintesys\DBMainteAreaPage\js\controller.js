/**
 * @description: DBマスタメンテナンス-センター店舗配送画面
 * @author: 10066179 tangnaikun
 * @date: 2018/9/28
 */
var RegistryGlobal = {
	userCD: sessionStorage.getItem("employeeCD"),
	venderCD: sessionStorage.getItem("userCD"),
	branchList:[],//店舗
	chkNodes:[],
	fourWeekModel: {},
	BranchSelStr:"",
	showList: [],
	confirmList: [],
	showDataLength: 0,
	tableData: [],
	tableSourceData: [],
	repeatTen:"",
	init: null,
	activeDialog:null,
	activeRow:null,
	r: 0
}

$(document).ready(function () {
	//ベンダー用画面Grid表示する
	getInitDataList();
	
	//店舗Tree Dialog，"OK"
	$("#branchDialogYes").click(function(){
		branchDialogYes();
	});
	
	//検索
	$("#btn_search").on("click",function(){
		tableData_search();
	});
	
	//Excel出力
	$("#btn_ExcelPort").on("click",function(){
		tableData_csv();
	});
	
	//削除
	$("#btn_Delte").on("click",function(){
		tableData_del();
	});
	
	//JAN条件入力
	$("#janArea").keypress(function(event){
		$("#janArea").val(this.value.replace(/[^\d-.\n]/g,''));
		return ((event.charCode >= 48 && event.charCode <= 57)||(event.charCode == 13));
	});
	$('ul.dropdown-menu').on('click', function(event){
        event.stopPropagation();
    });
	$("#jan_reload").bind("click", function(){
		 var janlistVal = "";
         var janListTxt=$("#janArea").val();
         if (janlistCheck(janListTxt)){
             var janlistVal = janListTxt.replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
			 var l = janlistVal.split(",").length;
			 if(l>5000){
				 showMsgBox(Message.msg017,[],function(){});//JANは5000件まで入力してください。
			 }else{
				 $("#parse_button").dropdown('toggle');}
	         }else{
	        	 if(janListTxt!=""){
	        		 showMsgBox(Message.msg026,["正しいJAN"],function(){});//正しいJANを入力してください。
		         }else{
		        	 $("#parse_button").dropdown('toggle');
		         }
		    }
	 });
});

//JANチェック
var janlistCheck=function(janlist){
	var regex = /^\d+([\s,]*\d+)*[\s,]*$/;
    var r = regex.test(janlist.split("...\r\n")[0].replace(/[ ]/g,""));
	return r;
}

//店舗Tree Dialog，"OK"
var branchDialogYes = function(){
	RegistryGlobal.chkNodes = zTree.getCheckedNodes();
	var dbList = [], BranchSel = [], BranchSelChk = [];
	RegistryGlobal.chkNodes.forEach(function(item, index){
		//DBエリア
		if(item.level=="1"){
			dbList.push(item.name);
		}
		//店舗
		if(item.level=="4"){
			BranchSelChk.push(item.id.split("^")[3]);
			BranchSel.push(item.id.split("^")[1]+","+item.id.split("^")[3]);
		}
	});
	if(isRepeat(BranchSelChk)){
		showMsgBox(Message.msg054,[RegistryGlobal.repeatTen],function(){});
	}else{
		if(BranchSel.length == 0){
			showMsgBox(Message.msg004,[],function(){});
		}else{
			RegistryGlobal.BranchSelStr = BranchSel.join(";");
			TemplateGrid.tbhot.setDataAtRowProp(RegistryGlobal.r, "dbArea", dbList.join(","));
			selTenListSave();
		}
	}
	$("#branchDivDialog").modal("hide");
}
//リピートチェック
var isRepeat = function(arr) {
	var hash = {};
	for ( var i in arr) {
		if (hash[arr[i]]){
			RegistryGlobal.repeatTen = arr[i];
			return true;
		}
		hash[arr[i]] = true;
	}
	return false;
} 
//保存
var selTenListSave = function(){
	$("#loadMask").show();
	$.ajax({
		url:'/dbmastermainteservice/services/AreaPageControl/selTenListSave',
		type:'POST',
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:RegistryGlobal.venderCD,
			jan:RegistryGlobal.showList[RegistryGlobal.r]["jan"],
			branch:RegistryGlobal.BranchSelStr
		},
		success:function(response){
			$("#loadMask").hide();
			if(response =="false"){
				showMsgBox(Message.msg028,[],function(){});
	    	}else{
	    		showMsgBox(Message.msg029,[],function(){});
	    	}
		},
		error:function(){
			$("#loadMask").hide();
			showMsgBox(Message.msg028,[],function(){});
		}
	});
}
//初期化
var getInitDataList=function(){
	//Grid　初期データロード
	var arrData = [[]];
	//var result = eval(arrData);
	RegistryGlobal.showList = arrData[0];
	TemplateGrid.CreatHotGrid('MainGrid',RegistryGlobal.showList);
}

//検索
var tableData_search = function(){
	RegistryGlobal.activeDialog = null;
	tableSelArr=[];
	$("#loadMask").show();
	var jan = $("#janArea").val().replaceAll("\n",",");
	
	$.ajax({
		url:'/dbmastermainteservice/services/AreaPageControl/getGridDataList',
		type:'POST',
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:RegistryGlobal.venderCD,
			jan:jan,
			flg:0
		},
		success:function(response){
			$("#loadMask").hide();
			if(response =="false"){
				showMsgBox(Message.msg002,[],function(){});
	    	}else{
	    		var result = JSON.parse(response)[0];
	    		
	    		if(result.length == 0){
	    			showMsgBox(Message.msg003,[],function(){});
//	    			return false;
	    		}
	    		
	    		RegistryGlobal.showList = result;
	    		TemplateGrid.tbhot.loadData(RegistryGlobal.showList);
	    	}
		},
		error:function(){
			$("#loadMask").hide();
			showMsgBox(Message.msg002,[],function(){});
		}
	});
}
//Excel出力
var tableData_csv = function(){
	RegistryGlobal.activeDialog = null;
	$("#loadMask").show();
	var jan = $("#janArea").val().replaceAll("\n",",");
	
	$.ajax({
		url:'/dbmastermainteservice/services/AreaPageControl/downloadCsv',
		type:'POST',
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:RegistryGlobal.venderCD,
			jan:jan,
			flg:1
		},
		success:function(response){
			$("#loadMask").hide();
			if(response =="false"){
				showMsgBox(Message.msg043,['出力'],function(){});//出力失敗しました。
	    	}else{
	    		var result = JSON.parse(response)[0];
	    		
	    		if(result.length == 0){
	    			showMsgBox(Message.msg003,[],function(){});
	    			return false;
	    		}
	    		
				var fields = ['dBArea', 'jAN', 'productName', 'specName', 'brandName', 'storeHouse', 'branch'];
			    var headNames = ['エリア', 'JAN', '商品名', '規格', 'ブランド', 'センター', '店舗'];
				//json data transform csv format
			    var csv = json2csv({data:result, fields:fields, fieldNames:headNames});
			    var filename = "Area" + "-" + getNowTime() + ".csv";
			    funDownload(csv.replaceAll("\"", ""), filename);
	    	}
		},
		error:function(){
			$("#loadMask").hide();
			showMsgBox(Message.msg043,['出力'],function(){});//出力失敗しました。
		}
	});
}
//download csv
var funDownload = function (content, filename) {
	if (window.navigator.msSaveOrOpenBlob) {
		// if browser is IE
	  	var blob = new Blob(['\uFEFF',decodeURIComponent(encodeURI(content))], {
	    	type: "text/csv;charset=utf-8;"
	  	});
	  	navigator.msSaveBlob(blob, filename);
	}else{
    	var eleLink = document.createElement('a');
        eleLink.download = filename;
        eleLink.style.display = 'none';
        var sjis_array = UTF8_ShiftJIS(content);
        var uint8_array = new Uint8Array(sjis_array);
        var blob = new Blob([uint8_array],{type: "text/csv"}); 
        eleLink.href = URL.createObjectURL(blob);   
        document.body.appendChild(eleLink);
        eleLink.click();
        document.body.removeChild(eleLink);
    }
};
//削除
var tableData_del = function(){
	
	var jan = getSelJan();
	if(jan == ""){
		showMsgBox(Message.msg004,[],function(){});
		return false;
	}
	$("#loadMask").show();
	$.ajax({
		url:'/dbmastermainteservice/services/AreaPageControl/delDataByJan',
		type:'POST',
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:RegistryGlobal.venderCD,
			jan:jan
		},
		success:function(response){
			$("#loadMask").hide();
			if(response =="false"){
				showMsgBox(Message.msg037,[],function(){});
	    	}else{
	    		delSelRow();
	    		showMsgBox(Message.msg038,[],function(){});
	    	}
		},
		error:function(){
			$("#loadMask").hide();
			showMsgBox(Message.msg037,[],function(){});
		}
	});
}
//選択された行を削除
var delSelRow = function(){
	var rowArr = [];
	
	for(var i=0;i<RegistryGlobal.showList.length;i++){
		if(RegistryGlobal.showList[i].chkFlg == "1"){
			rowArr.push(i);
		}
	}
	
	for(var i=rowArr.length-1;i>=0;i--){
		TemplateGrid.tbhot.alter("remove_row",rowArr[i]);
	}
}
//選択されたJANを取得
var getSelJan = function(){
	var janStr = "";
	
	for(var i=0;i<RegistryGlobal.showList.length;i++){
		if(RegistryGlobal.showList[i].chkFlg == "1"){
			janStr = janStr + RegistryGlobal.showList[i].jan + ",";
		}
	}
	
	if(janStr.length > 0){
		janStr = janStr.substring(0,janStr.length-1);
	}
	
	return janStr;
}

/////////////////////////////////////////////////////////
//bindDataとonchange事件
//data     Json
//valuecol 項目CDString
//labelcol 項目名 String
//id       String
//multiple  "all":全選(複数選択のみ有効) 
//        "single":初期に選択項目
//        Array:選択したデータ  []:すべて選択しない
////////////////////////////////////////////////////////msg055
function bindData(filtdata ,valuecol, labelcol, id, multiple){ 
 var data = filtdata;
 var options=[];
 for(var i=0;i<data.length;i++){
	   options.push({
		   label:data[i][valuecol]+"-"+data[i][labelcol],
		   title:data[i][labelcol],
		   value:data[i][valuecol]
	   });
 }
 $('#'+id).multiselect('dataprovider', options);
 if(multiple=="all"){
	 	//全選  及び　onChange trigger
	 	$('#'+id).multiselect('selectAll',false,true);
	 	$('#'+id).multiselect('updateButtonText');				   
 } else if(multiple=="single"){
	 	//第一の項目選択　及び　onChange trigger
	 	$('#'+id).multiselect('select',options[0].value,true);			     
 } else {
	   	//選択設定   及び　onChange trigger
		$('#'+id).multiselect('select',multiple,true);
 }
}