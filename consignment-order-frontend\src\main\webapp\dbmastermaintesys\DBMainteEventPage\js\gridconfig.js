var RegistryGlobal = {
	tableDataList: []
	,branchList:[]
	,divList: []
	,chkNodes: []
	,r: 0
}

/** DataGrid Creat*/
var TemplateGrid = {
	tbhot:null,
	colWidths:[60, 100, 140, 0.1, 0.1, 140, 140, 100, 100, 120, 140, 0.1],
	columns: [
        {data: 'ChkFlg',    		type: 'checkbox',    checkedTemplate: '1', uncheckedTemplate: '0'},//全選 
        {data: 'EventName',	    	type: 'text'},//イベント名
        {data: 'DBArea', 			type: 'text',        readOnly:true},//DBエリア
        {data: 'DBAreaBranch', 		type: 'text'},//被选中店舗： 1^1^5,1^1^8
        {data: 'BranchSel', 		type: 'text'},//被选中店舗店舗CD： 5,8
        {data: 'Branch', 			type: 'text',        readOnly:true},//店舗： 5-北九州空港ﾊﾞｲﾊﾟｽ店,8-上津役店
        {data: 'Division',
            editor: "chosen",
            chosenOptions: {
            	multiple: true
            }
        },//DIV
        {data: 'EventStartDate',	type: 'date',
        	dateFormat: 'YYYY/MM/DD',
            correctFormat: true,
            allowEmpty: true,
            datePickerConfig: {
            	showWeekNumber: true,
            	i18n: {
    			    months        : ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
    			    weekdays      : ['日曜日','月曜日','火曜日','水曜日','木曜日','金曜日','土曜日'],
    			    weekdaysShort : ['日','月','火','水','木','金','土']
            	}
            }
        },//イベント開始日
        {data: 'EventEndDate',	    type: 'date',
        	dateFormat: 'YYYY/MM/DD',
            correctFormat: true,
            allowEmpty: true,
            datePickerConfig: {
            	showWeekNumber: true,
            	i18n: {
    			    months        : ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
    			    weekdays      : ['日曜日','月曜日','火曜日','水曜日','木曜日','金曜日','土曜日'],
    			    weekdaysShort : ['日','月','火','水','木','金','土']
            	}
            }
        },//イベント終了日
        {data: 'EmployeeName',		type: 'text',        readOnly:true},//最終更新者
        {data: 'Modified',			type: 'text',        readOnly:true},//最終更新日
        {data: 'ID',	    		type: 'text',     	 readOnly:true}//ID
	],
	isChecked: function(){
		if(!this.tbhot){
			return false;
		}
		if("afterChg"==chk){
			var sourceData = TemplateGrid.tbhot.getSourceData();
			for (var i=0; i<sourceData.length-1; i++) {
				if(sourceData[i].ChkFlg!="1"){						
					return false;
				}
			}
			return true;
		}else{
			return chk;
		}
	},
	//create grid
	CreatHotGrid: function(hotgrid, arrData){
		//まず対象内容クリア
		if(this.tbhot){
			this.tbhot.destroy();
			chk=false;
		}
		this.tbhot = new Handsontable(document.getElementById(hotgrid),{
			height:$(".page-content").height()-150,
			rowHeaders: false,
			colHeaders: function(col){
							switch(col){
								case 0:
									var txt = '<label class="checkbox-inline"><input id="chkAll" onclick="chkAll();" type="checkbox"';
										txt += TemplateGrid.isChecked() ? 'checked="checked"' : '';
										txt += '/>全選</label>';
									return txt;
								case 1: return 'イベント名';
								case 2: return 'DBエリア';
								case 3: return '店舗';
								case 4: return 'DBエリア店舗';
								case 5: return '選択店舗';
								case 6: return 'DIV';
								case 7: return 'イベント開始日';
								case 8: return 'イベント終了日';
								case 9: return '最終更新者';
								case 10: return '最終更新日';
								case 11: return 'ID';
							}
						},
			colWidths: this.colWidths,
			columns: this.columns,
			overflow: scroll,
		    fillHandle:false,
		    wordWrap:false,
		    search: true,
		    minSpareRows:1,
		    columnSorting: true,
		    sortIndicator: true,
		    manualColumnResize: true,
			currentRowClassName: 'currentRow',
			cells: function (row, col, prop) {
				var cellProperties = {};
				cellProperties.renderer = styleRender;
				return cellProperties;					
	    	},
	    	beforeChange : function(changes, source){
	    	},
	    	afterChange: function(changes, source) {
	    		afterChange(changes, source);
 			},
 			afterSelectionByProp: function(r, p, r2, p2) {
 				if(r==r2 && p==p2 && p=="Branch"){
 					RegistryGlobal.r = r;
 					BtnTreeWindow();
 				}
 			}
		});
		this.tbhot.loadData(arrData);
	    this.tbhot.render();
	}
}

var chk;
var chkAll = function(){
	if(chk==true){
		chk = false;
	}else{
		chk = true;
	}
	if(TemplateGrid.tbhot.getSourceData().length>0){		
		$.each(RegistryGlobal.tableDataList, function(i, data){
			if(!data.ChkFlg)
			{
				return false;
			}
			data.ChkFlg = chk?"1":"0";
		});
	}
	TemplateGrid.tbhot.render();
}

var styleRender=function(instance, td, row, col, prop, value, cellProperties){
	Handsontable.renderers.TextRenderer.apply(this, arguments);
	var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', row);
	
	if("ChkFlg"==prop){//checkbox
		td.style.textAlign = "center";
		Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
	}
	else if("Branch"==prop){//店舗
		td.style.backgroundColor = "#FFFF99";
		td.style.color = "#157FCC";
		td.style.textDecoration = "underline";
		td.style.cursor="pointer";
		if(value){			
			td.title = value;
		}
	}
	else if("Division" == prop){
		if(value == null){
			Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
			td.style.backgroundColor = "#FFFF99";
		}else{
			var selectedId;
		    cellProperties.chosenOptions.data = RegistryGlobal.divList;
	        var optionsList = cellProperties.chosenOptions.data;
		    if(typeof optionsList === "undefined" || typeof optionsList.length === "undefined" || !optionsList.length) {
		        Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
		        return td;
		    }
		    var values = (value + "").split(",");
		    var value = [];
		    for (var index = 0; index < optionsList.length; index++) {
		        if (values.indexOf(optionsList[index].id + "") > -1) {
		            selectedId = optionsList[index].id;
		            value.push(optionsList[index].label);
		        }
		    }
		    value = value.join(", ");
		    Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
			td.style.backgroundColor = "#FFFF99";
		    if(TemplateGrid.tbhot!=null && value==""){
		    	TemplateGrid.tbhot.setDataAtCell(row, col, null);
			}
		}
	    if(value){			
			td.title = value;
		}
	    return td;
	}
	else if("EventName" == prop){
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		td.style.backgroundColor = "#FFFF99";
	}
	else if("EventStartDate" == prop || "EventEndDate" == prop){
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		if(!value||isDate(convertToCorrectDate(value))){				
			$(arguments[1]).removeClass("htInvalid");
		}
		if(value && !isDate(value)){
			value = "";
		}
		td.style.backgroundColor = '#FFFF99';
	}
	return td;
}

var setting = {
	check: {
		enable: true
	},
	data: {
		simpleData: {
			enable: true
		}
	}
};

var afterChange = function(changes, source){
	if (source === 'loadData'||source ==='external') {
        return; //don't save this change
    }
    if(changes){
    	changes.forEach(function(change, index){
    		var rowIndex = change[0];//行番号
            var colHead = change[1];//列
            var oldvalue = change[2];
            var newvalue = change[3];
            
            var sourceData = TemplateGrid.tbhot.getSourceData();
            var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', rowIndex);
            if(typeof(colHead)=="undefined"
            	||((typeof(oldvalue)=="undefined"||oldvalue==""|| oldvalue==null)&&  (typeof(newvalue)=="undefined"||newvalue==""|| newvalue==null))
                || newvalue==oldvalue) {
            }else{
	            if("ChkFlg"==colHead){
					chk = "afterChg";
					if("0"==newvalue||"1"==newvalue){
						sourceData[realRow].ChkFlg = newvalue;
					}else{
						sourceData[realRow].ChkFlg=oldvalue;
					}
				}else if(!(!newvalue && !oldvalue) && newvalue!=oldvalue){//自動選択
					sourceData[realRow].ChkFlg = "1";
	            }
	            
	            //イベント名
	            if(colHead=="EventName" && newvalue){
	            	newvalue = newvalue.replace(/[,;'()"，；‘（）“]/g, "");
	            	sourceData[realRow].EventName=newvalue;
	    		}
	            
	            //店舗
	            if(colHead=="BranchSel" && newvalue){
	            	RegistryGlobal.r = rowIndex;
	            	$.fn.zTree.init($("#branchDialog"), setting, RegistryGlobal.branchList);
	            	zTree = $.fn.zTree.getZTreeObj("branchDialog");
	            	var type = { "Y":"ps", "N":"ps"};
	            	zTree.setting.check.chkboxType = type;
	            	var DBAreaBranch = TemplateGrid.tbhot.getDataAtRowProp(RegistryGlobal.r, "DBAreaBranch");
	            	if(DBAreaBranch){
	            		var DBAreaBranchList = TemplateGrid.tbhot.getDataAtRowProp(RegistryGlobal.r, "DBAreaBranch").split(",");
	            		DBAreaBranchList.forEach(function(item, index){
	            			zTree.checkNode(zTree.getNodeByParam("id", item), true, true);
	            		});
	            	}
					RegistryGlobal.chkNodes = zTree.getCheckedNodes();
					var dbList = [], branchList = [], DBAreaBranchList = [], BranchSel = [];
					RegistryGlobal.chkNodes.forEach(function(item, index){
						//DBエリア
						if(item.level=="1"){
							dbList.push(item.name);
						}
						//店舗
						else if(item.level=="3"){
							branchList.push(item.name);
						}
					});
					TemplateGrid.tbhot.setDataAtRowProp(RegistryGlobal.r, "DBArea", dbList.join(","));
					TemplateGrid.tbhot.setDataAtRowProp(RegistryGlobal.r, "Branch", branchList.join(","));
	    		}
	            //イベント開始日、イベント終了日
	            if((colHead=="EventStartDate" || colHead=="EventEndDate") && newvalue){
	            	newvalue=dateDeal(newvalue);
            		TemplateGrid.tbhot.setDataAtRowProp(rowIndex, colHead,newvalue);
	    		}
            }
            TemplateGrid.tbhot.render();
    	});
    }
}

var BtnTreeWindow = function(){
	$.fn.zTree.init($("#branchDialog"), setting, RegistryGlobal.branchList);
	zTree = $.fn.zTree.getZTreeObj("branchDialog");
	var type = { "Y":"ps", "N":"ps"};
	zTree.setting.check.chkboxType = type;
	var DBAreaBranch = TemplateGrid.tbhot.getDataAtRowProp(RegistryGlobal.r, "DBAreaBranch");
	if(DBAreaBranch){
		var DBAreaBranchList = TemplateGrid.tbhot.getDataAtRowProp(RegistryGlobal.r, "DBAreaBranch").split(",");
		DBAreaBranchList.forEach(function(item, index){
			zTree.checkNode(zTree.getNodeByParam("id", item), true, true);
		});
	}
	$("#branchDivDialog").modal("show");
}