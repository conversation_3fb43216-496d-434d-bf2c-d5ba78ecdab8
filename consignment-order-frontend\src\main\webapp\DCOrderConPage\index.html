<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>センター在庫発注</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width, initial-scale=1" name="viewport"/>
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Cache-Control" content="no-cache">
	<meta http-equiv="Expires" content="0">
	<link rel="icon" type="image/png/ico" href="../Common/favicon.ico">
	<!-- basic styles -->
	<link href="../Common/font-awesome/css/font-awesome.min.css" rel="stylesheet">
	<link href="../Common/common.css" rel="stylesheet">
	<link href="../Common/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/bootstrap/css/bootstrap.min.css" rel="stylesheet">
	<link href="../Common/css/layout.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/css/themes/light2.css" rel="stylesheet" type="text/css" id="style_color"/>
	<link href="../Common/bootstrap-multiselect/css/bootstrap-multiselect.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/handsontable/handsontable.full.min.css" rel="stylesheet"/>
	<link href="../Common/jan/jan.css" rel="stylesheet"/>
	<link href="../Common/prettify/prettify.css" rel="stylesheet"/>
	<link href="css/style.css" rel="stylesheet" type="text/css" />
	<script src="../Common/jquery/jquery.min.js" type="text/javascript"></script>
	<script src="../Common/bootstrap-multiselect/js/bootstrap-multiselect.js" type="text/javascript" ></script>
	<script src="../Common/handsontable/handsontable.full.min.js" type="text/javascript" ></script>
	<script src="../Common/handsontable/handsontable.maxlength.full.js" type="text/javascript" ></script>
	<script src="../Common/prettify/prettify.js" type="text/javascript" ></script>
	<script src="../Common/json2csv.js" type="text/javascript" ></script>
	<script src="../Common/xlsx.min.js" type="text/javascript" ></script>
	<script>document.write("<s"+"cript src='../Common/version.js?ver="+Math.random()+"'></scr"+"ipt>"); </script>
	<script>document.write("<s"+"cript src='../Common/common.js?ver="+sysversion+"'></scr"+"ipt>");</script>
	<link rel="shortcut icon" href="../Common/favicon.ico"/>
</head>
<body class="page-header-fixed page-quick-sidebar-over-content page-sidebar-closed-hide-logo page-container-bg-solid">
<!-- BEGIN HEADER -->
<div class="page-header navbar navbar-fixed-top">
	<script>
		var href=window.document.location.href;
		var rankNum=getRank(href);
	</script>
	<!-- BEGIN HEADER INNER -->
	<div class="page-header-inner">
		<!-- BEGIN LOGO -->
		<div class="page-logo">
			<a href="index.html" class="logo-default" style="font-size: 20px;color: black;margin-top: 10px;">
				センター在庫発注
			</a>
			<div class="menu-toggler sidebar-toggler">
			</div>
		</div>
		<!-- END LOGO -->
		<!-- BEGIN RESPONSIVE MENU TOGGLER -->
		<a href="javascript:;" class="menu-toggler responsive-toggler" data-toggle="collapse" data-target=".navbar-collapse">
		</a>
		<!-- END RESPONSIVE MENU TOGGLER -->
		<!-- BEGIN TOP NAVIGATION MENU -->
		<div class="top-menu">
			<ul id="navbarheader" class="nav navbar-nav pull-right">
			</ul>
		</div>
		<div class="top-menu" style="height:46px; margin-top:14px;margin-right:5px;">
			<span class="glyphicon glyphicon-question-sign"></span>
			<a id="manualDown" href="javascript:;" style="color:#484848;">マニュアル</a>
		</div>
		<!-- END TOP NAVIGATION MENU -->
	</div>
	<!-- END HEADER INNER -->
</div>
<!-- END HEADER -->
<div class="clearfix">
</div>
<!-- BEGIN CONTAINER -->
<div class="page-container">
	<!-- BEGIN SIDEBAR -->
	<div class="page-sidebar-wrapper">
		<div id="leftnavlist" class="page-sidebar navbar-collapse collapse">
		</div>
	</div>
	<!-- END SIDEBAR -->
	<!-- BEGIN CONTENT -->
	<div class="page-content-wrapper">
		<div class="page-content">
			<!-- BEGIN PAGE HEADER-->
			<div class="page-bar">
				<ul class="page-breadcrumb">
					<li>
						<i class="fa fa-home"></i>
						<a href="index.html">コンサイメント推奨画面</a><!-- <i class="fa fa-angle-right"></i> -->
					</li>
					<li>
						<a href="#"></a>
					</li>
				</ul>
			</div>
			<!-- END PAGE HEADER-->
			<!--#検索条件 form-->
			<div class="form-horizontal" style="margin-top: 15px;">
				<div class="form-group">
					<div class="btn-group lblWidth"  role="group"><label>DIV</label>
					</div>
					<div class="btn-group selWidth"  role="group">
						<select id="divMulti" size="5" multiple class="form-control"></select>
					</div>
					<div class="btn-group nolblWidth"  role="group"></div>
					<div class="btn-group"  role="group">
						<span class="dropdown btn-group" style="margin-left:5px;">
							<button type="button" id="parse_button" style="width:180px;" class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" title="JANは完全入力でのみ検索可能です" aria-expanded="false">JAN入力
							<span class="caret"></span>
							</button>
							<ul class="dropdown-menu scrollable-menu" id="parse_menu">
								<li>
									<a href="#" style="padding:0 5px;">
										<textarea id="janArea" class="form-control" style="width:180px;" rows="5" placeholder="JANは完全入力でのみ&#13;&#10;検索可能です"  onfocus="this.select();"></textarea>
									</a>
								</li>
								<li role="separator" class="divider"></li>
								<li style="padding:0px 5px;"><button type="button" class="btn btn-success btn-sm" style="display: block; width: 100%;" id="jan_reload">確定</button></li>
							</ul>
						</span>
					</div>
					<div class="btn-group lblWidth"  role="group">
						<label>ベンダー</label>
					</div>
					<div class="btn-group selWidth"  role="group">
						<select id="divSupplierMulti" size="5" multiple class="form-control"></select>
					</div>
					<div class="btn-group nolblWidth"  role="group"></div>
					<div class="btn-group" id="divExceptPro" role="group" style="margin-left: 5px;display:none;">
						<label class="checkbox-inline" ><input type="checkbox" id="chkExceptPro" checked>コンサイメント発注商品除外</label>
					</div>
				</div>
				<div class="form-group">
					<div class="btn-group lblWidth"  role="group"><label>センター</label></div>
					<div class="btn-group selWidth" role="group">
						<select id="storeHouseMulti" size="5" multiple class="form-control"></select>
					</div>
					<div class="btn-group nolblWidth"  role="group"></div>
					<div class="btn-group"  role="group" style="margin-left: 5px;">
						<label class="checkbox-inline"><input type="checkbox" id="inline1" value="option1">推奨数ある商品のみ表示</label>
					</div>
					<div class="btn-group nolblWidth"  role="group"></div>
					<div class="btn-group"  role="group" style="margin-left: -5px;">
						<label class="checkbox-inline"><input type="checkbox" id="chkTimesFlg" value="option1">2回発注</label>
					</div>
					<div class="btn-group btnWidth"  role="group" >
						<input id="btn_search" type="button" class="btn btn-primary btn-block" value="検索">
					</div>
					<div class="btn-group btnWidth"  role="group" >
						<input id="btn_confirm" type="button" class="btn btn-primary btn-block" value="確定">
					</div>
					<div class="btn-group btnWidth"  role="group" >
						<a id="btn_csv" class="btn btn-success btn-block"><i class="glyphicon glyphicon-download-alt"></i>預託CSV</a>
					</div>
				</div>
			</div>
			<!--/検索条件 form-->
			<div class="col-md-12 col-sm-12 col-xs-12" id="grid" style="padding:0px 10px 10px 10px;">
				<div id="dgJiseki" style="width:100%"></div>
			</div>
		</div>
	</div>
	<!-- END CONTENT -->
</div>
<!-- END CONTAINER -->

<!-- BEGIN FOOTER -->
<div class="page-footer" style="display: none; height: 4px; padding: 0px; margin: 0px;">
</div>
<!-- END FOOTER -->

<!--メッセージ表示-->
<div class="modal fade" id="myMessage" tabindex="-2" role="dialog" aria-labelledby="" style="z-index: 2000;margin-top:100px;">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="myMessageTitle">コンサイメント発注専用システム。</h4>
			</div>
			<div class="modal-body" >
				<p id="myMessageBody"></p>
			</div>
			<div class="modal-footer">
				<button type="button" id="msgSysBtnYes" class="btn btn-default" data-dismiss="">OK</button>
				<button type="button" id="msgSysBtnNo" class="btn btn-default" data-dismiss="modal">キャンセル</button>
			</div>
		</div>
	</div>
</div>

<script src="../Common/jquery/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
<script src="../Common/bootstrap/js/bootstrap.min.js"></script>
<script src="../Common/message/message.js"></script>
<!-- <script src="../Common/message/jquery-ui.my.js" type="text/javascript"></script> -->
<script src="../Common/common.js"></script>
<script src="../Common/js/metronic.js" type="text/javascript"></script>
<script src="../Common/js/layout.js" type="text/javascript"></script>
<script>document.write("<s"+"cript src='../Common/app.js?ver="+sysversion+"'></scr"+"ipt>");</script>
<script>document.write("<s"+"cript src='js/gridconfig.js?ver="+sysversion+"'></scr"+"ipt>");</script>
<script>document.write("<s"+"cript src='js/controller.js?ver="+sysversion+"'></scr"+"ipt>");</script>
<script>document.write("<s"+"cript src='js/SplitDetail.js?ver="+sysversion+"'></scr"+"ipt>");</script>
<script>document.write("<s"+"cript src='js/showJanInfo.js?ver="+sysversion+"'></scr"+"ipt>");</script>
<script>document.write("<s"+"cript src='js/showInOutPlanInfo.js?ver="+sysversion+"'></scr"+"ipt>");</script>

<!-- <div id='messageBox' style='display:none;'><span id='spanmessage'></span></div> -->
<script>
	jQuery(document).ready(function() {
		Metronic.init(); // init metronic core componets
		Layout.init(); // init layout
	});
	makeProcessing(rankNum);
</script>
</body>
<!-- END BODY -->
</html>