<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>センター在庫発注</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width, initial-scale=1" name="viewport"/>
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Cache-Control" content="no-cache">
	<meta http-equiv="Expires" content="0">
	<link rel="icon" type="image/png/ico" href="../Common/favicon.ico">
	<!-- basic styles -->
	<link href="../Common/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <link href="../Common/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/bootstrap/css/bootstrap.min.css" rel="stylesheet">
	<link href="../Common/css/layout.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/css/themes/light2.css" rel="stylesheet" type="text/css" id="style_color"/>
	<script src="../Common/jquery/jquery.min.js" type="text/javascript"></script>
	<script>document.write("<s"+"cript src='../Common/version.js?ver="+Math.random()+"'></scr"+"ipt>"); </script>
	<script>document.write("<s"+"cript src='../Common/common.js?ver="+sysversion+"'></scr"+"ipt>");</script>
	<link rel="shortcut icon" href="../Common/favicon.ico"/>
</head>
<body class="page-header-fixed page-quick-sidebar-over-content page-sidebar-closed-hide-logo page-container-bg-solid">
<!-- BEGIN HEADER -->
<div class="page-header navbar navbar-fixed-top">
	<!-- BEGIN HEADER INNER -->
	<div class="page-header-inner">
		<!-- BEGIN LOGO -->
		<div class="page-logo">
			<a href="index.html" class="logo-default" style="font-size: 20px;color: black;margin-top: 10px;">
				センター在庫発注
			</a>
			<div class="menu-toggler sidebar-toggler">
			</div>
		</div>
		<!-- END LOGO -->
		<!-- BEGIN RESPONSIVE MENU TOGGLER -->
		<a href="javascript:;" class="menu-toggler responsive-toggler" data-toggle="collapse" data-target=".navbar-collapse">
		</a>
		<!-- END RESPONSIVE MENU TOGGLER -->
		<!-- BEGIN TOP NAVIGATION MENU -->
		<div class="top-menu">
			<ul id="navbarheader" class="nav navbar-nav pull-right">
			</ul>
		</div>
		<div class="top-menu" style="height:46px; margin-top:14px;margin-right:5px;">
		    <span class="glyphicon glyphicon-question-sign"></span>
			<a id="manualDown" href="javascript:;" style="color:#484848;">マニュアル</a>
		</div>
		<!-- END TOP NAVIGATION MENU -->
	</div>
	<!-- END HEADER INNER -->
</div>
<!-- END HEADER -->
<div class="clearfix">
</div>
<!-- BEGIN CONTAINER -->
<div class="page-container">
	<!-- BEGIN SIDEBAR -->
	<div class="page-sidebar-wrapper">
		<div id="leftnavlist" class="page-sidebar navbar-collapse collapse">
		</div>
	</div>
	<!-- END SIDEBAR -->
	<!-- BEGIN CONTENT -->
	<div class="page-content-wrapper">
		<div class="page-content" style="padding-bottom:0px">
			<!-- BEGIN PAGE HEADER-->
			<div class="page-bar">
				<ul class="page-breadcrumb">
					<li>
						<i class="fa fa-home"></i>
						<a href="index.html">権限設定画面</a>
					</li>
					<li>
						<a href="#"></a>
					</li>
				</ul>
			</div>
			<!-- END PAGE HEADER-->
			<iframe frameborder="0" class="tab-content" id="ifrm" style="height:100%;width:100%;padding-top:5px"
					src="" title="権限設定画面"></iframe>
		</div>
		
	</div>
	<!-- END CONTENT -->
</div>
<!-- END CONTAINER -->

<!-- BEGIN FOOTER -->
<div class="page-footer" style="display: none; height: 4px; padding: 0px; margin: 0px;">
</div>

<!-- END FOOTER -->
	<script src="../Common/jquery/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
	<script src="../Common/bootstrap/js/bootstrap.min.js"></script>
	<script src="../Common/js/metronic.js" type="text/javascript"></script>
	<script src="../Common/js/layout.js" type="text/javascript"></script>
	<script>document.write("<s"+"cript src='../Common/app.js?ver="+sysversion+"'></scr"+"ipt>");</script>
	<script>
		jQuery(document).ready(function() {
			Metronic.init(); // init metronic core compnets
		    Layout.init(); // init layout
		    var userCD = sessionStorage.getItem("userCD");
		    var userName=sessionStorage.getItem("userName");
		    var employeeCD = sessionStorage.getItem("employeeCD");
		    var employeeName=sessionStorage.getItem("employeeName");
			const path = window.location.pathname;
			const contextPath = "/" + path.split('/')[1];
			const baseUrl = contextPath + "/dbmastermaintesys/index.html";
			if(userCD!=null&&userCD!=""){
				$("#ifrm").attr("src",baseUrl + "?usercd="+
						userCD+"&username="+userName+"&page=DBMainteAuthorityPage&ver="+sysversion);
			}else if(employeeCD!=null&&employeeCD!=""){
				$("#ifrm").attr("src",baseUrl + "?employeecd="+
						employeeCD+"&employeename="+employeeName+"&page=DBMainteAuthorityPage&ver="+sysversion);
			}else{
				location.href="ErrorPage/error.html";
			}
		    $(".tab-content").height($(".page-content").height()*0.97);
		    $("#ifrm").load(function() {
			    $("#ifrm").contents().find("#navbarcon").hide();
		    });
		});
	</script>
</body>
<!-- END BODY -->
</html>