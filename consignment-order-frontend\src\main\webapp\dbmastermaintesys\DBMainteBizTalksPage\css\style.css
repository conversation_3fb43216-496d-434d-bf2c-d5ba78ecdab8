@CHARSET "UTF-8";
.vspace{
	height: 10px;
}
.lblWidth{
	width: 9%;
 	text-align: left;
  	padding-left: 10px; 
}
.selover{
	white-space:nowrap; 
	overflow:hidden; 
	text-overflow:ellipsis;
}
.smlblWidth{
	width: 6%;
 	text-align: left;
 	margin-left: 15px;
}
.janlblWidth{
	width: 6%;
 	text-align: left;
 	margin-left: 2%;
}
.selWidth{
	width: 15%;
}
.janWidth{
	width: 14%;
}
.dateWidth{
	width: 24%;
}
.btnWidth{
	width: 10%;
    margin-left: 2%;
    margin-right: 2%;
}
.btn-footer{
	padding: 25px;
    text-align: right;
    border-top: 0px solid #e5e5e5;
    float: right;
}
/* 		table .htCore */
#MainGrid th{
	vertical-align: middle!important;
}
.selWidth .btn-group{
	width:100%;
}
.selWidth .multiselect {
	width:100%;
}
.selWidth ul {
	width:100%;
}
.currentRow{
	background-color: lightskyblue!important;
}