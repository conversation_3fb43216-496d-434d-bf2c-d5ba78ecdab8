@CHARSET "UTF-8";
.vspace{
	height: 10px;
}
.lblWidth{
	width: 80px;
 	text-align: left;
 	margin-left: 15px;
}
.selWidth{
	width: 175px;
}
.selover{
	white-space:nowrap; 
	overflow:hidden; 
	text-overflow:ellipsis;
}
.jan<PERSON>idth{
	width: 120px;
}
.dateWidth{
	width: 145px;
}
.btnWidth{
	width: 100px;
 	margin-left: 40px;
}
.btn-footer{
	padding: 25px;
    text-align: right;
    border-top: 0px solid #e5e5e5;
    float: right;
}
/* 		table .htCore */
#gridAllInfo th{
	vertical-align: middle!important;
}

#gridCenterInfo th{
	vertical-align: middle!important;
}
.selWidth .btn-group{
	width:100%;
}
.selWidth .multiselect {
	width:100%;
}
.selWidth ul {
	width:100%;
}
.chosen-container.chosen-with-drop-top-mul .chosen-drop {
left: 0;
top:-185px;
}
.currentRow{
	background-color: lightskyblue!important;
}