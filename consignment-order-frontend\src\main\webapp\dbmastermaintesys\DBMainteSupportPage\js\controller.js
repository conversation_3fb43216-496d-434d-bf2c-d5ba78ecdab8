/**
 * @description: DBマスタメンテナンス-商品登録画面
 * @author: 10066179 tangnaikun
 * @date: 2018/9/15
 * @description:センター自動発注_機能統合
 * @修正者: 10047496 zhangyunfeng
 * @修正日: 2019/04/01
 * */
var RegistryGlobal = {
	userCD : sessionStorage.getItem("employeeCD"),
	venderCD : sessionStorage.getItem("userCD"),
	CreateDate : new Date().toISOString(),// 日付転換
	divList : [],
	storeHouseList : [],
	fourWeekModel : {},
	showList : [],
	confirmList : [],
	showDataLength : 0,
	tableData : [],
	tableSourceData : [],
	init : null,
	NohiSakiMultiList : [],// 納品先マスタ
	ContinuMultiList : [],// 継続区分
	ShipptypeMultiList : [],// 物流タイプ
	OrderFlgMultiList : [],// 発注区分
	OrderCenterMultiList : [],// 発注センター
	ConsultJanList : [],// 参照JAN
	DivMultiList : [],// Div
	MixCDMultiList : [],
	supplierList : []
//混載
}
//handsontable header
var arrData;
var GridHeaderFlg=1;
//app start
$(document).ready(function () {
	//INIT
	divAndStoreHouseInit();
		
	//画面初期化
	getInitDataList();
	
	//検索
	$("#btn_search").on("click",function(){
		tableData_search();
	});

	//Excel出力
	$("#btn_ExcelPort").on("click",function(){
		//tableData_csv();
		$("#btn_ExcelPort").attr('disabled',true);
		setTimeout(function() {
			$("#btn_ExcelPort").attr('disabled',false);
		}, 3000);//3秒WAIT
		tableData_excel();//edit zhangyunfeng 既存のCSVをなくす、xlsxへ転換 20190402
	});
	
	//フォーマット出力
	$("#btn_ImportFrmat").on("click",function(){
		importFrmat();
	});
	
	//取り込み
	$("#btn_Import").on("click",function(){
		importExcel();
	});
	
	//自動検索
	if("search"==getQueryString("search")){
		tableData_search();
	}
	
	//保存
	$("#btn_confirm").on("click",function(){
		$("#loadMask").show();
		//mixChk_confirm();//edit zhangyunfeng　削除機能追加 2019/06/10
		mixChk_confirm(0);
	});

	//add zhangyunfeng 削除機能追加 2019/06/10
	$("#btn_Del").on("click",function(){
		showDelAskMsg(Message.msg062, [], function() {
			$("#loadMask").show();
			mixChk_confirm(1);
			}, function() {
			return;
		});
	});

　　　//add zhangyunfeng 案再作成 2019/04/01
	$("#btn_orderCase").on("click",function(){
		$("#loadMask").show();
		$.ajax({
			url:`${SystemManage.shiniseAddress}SupportPageControl/getAgainInitList`,
			type:'POST',
			data:{
				userCD:RegistryGlobal.userCD,
				venderCD:RegistryGlobal.venderCD
			},
			headers: {
	        	"user-code": sessionStorage.getItem("userCode") || ""
	    	},
			success:function(response){
				if(response =="false"){
					$("#loadMask").hide();
					showMsgBox(Message.msg001,[],function(){});
				}else{
					var result = JSON.parse(response);
					var orderCase_instance = new orderCaseDetail(jQuery);
					orderCase_instance.init({
						usercd: RegistryGlobal.userCD
						,mixList:result.mixList//混載
						,centerList:result.centerList//センター
					});
					$("#loadMask").hide();
					orderCase_instance.show();
				}
			},
			error:function(){
				$("#loadMask").hide();
				showMsgBox(Message.msg001,[],function(){});
			}
		});
	});

	$("#messageTitle").html(Message.sysTitle);
	
	$("#filesupport").change(function(e){
		var headArr0 = ["適用開始日","センターCD","JAN"];
		var headArr1 = ["センターCD","JAN","発注センター","混載ID","代表商品名","単品ロット入数","単品ロット単位","混載ロット数量","混載ロット条件","重量","体積","安全在庫日数","基準消化日数","発注間隔","LT日","LT月","LT火","LT水","LT木","LT金","LT土","LT基準","継続区分","発注区分","参照区分","参照JAN","DB対象フラグ","システム区分","推奨方法","自動区分","備考"];
		
		if(checkData("filesupport")){
			$("#loadMask").show();
			$("#fileuser").prop("value",RegistryGlobal.userCD);
			$("#filevender").prop("value",RegistryGlobal.venderCD);
			$("#uploadSupportForm").ajaxSubmit({
				url:SystemManage.address+"SupportPageControl/supportimport",
				dataType:"text",
				success:resultMsg,
				error:errorMsg
			});
			function resultMsg(response){
				$("#loadMask").hide();
				$("#filesupport").val("");
				if(response =="false"){
					showMsgBox(Message.msg043,['取込チェック'],function(){});
				}else if(response.indexOf("nokey")!=-1){
					showMsgBox(Message.msg061,[response.split(":")[1]],function(){});
				}else if(response =="nodata"){
					showMsgBox(Message.msg003,[],function(){});
				}else if(response.indexOf("must")!=-1){
					showMsgBox(Message.msg026,[response.split(":")[1]],function(){});
		    	}else if(response =="errdate"){
					showMsgBox(Message.msg049,["適用開始日"],function(){});
				}else if(response =="date"){
					showMsgBox(Message.msg047,[],function(){});
				}else if(headArr1.indexOf(response)!=-1){
					showMsgBox(Message.msg050,[response],function(){});
		    	}else{
		    		var result = JSON.parse(response);
		    		arrDataInit();
		    		RegistryGlobal.showList = arrData[0].concat(result[0]);
		    		TemplateMainGrid.tbhot.loadData(RegistryGlobal.showList);
		    		
		    		//保存リスト更新
		    		saveArr = [];
		    		for(var i=0;i<result[0].length;i++){
		    			saveArr.push(i+2);
		    			RegistryGlobal.showList[i+2].chkFlg = true;
		    			TemplateMainGrid.tbhot.setDataAtRowProp((i+2),"chkFlg",true);
		    		}
		    		RegistryGlobal.showList[0].chkFlg = true;
		    		TemplateMainGrid.tbhot.setDataAtRowProp(0,"chkFlg",true);
		    	}
			}
			function errorMsg(){
				$("#loadMask").hide();
				$("#filesupport").val("");
				showMsgBox(Message.msg043,['取込'],function(){});
			}
		}
	});
	//JAN条件入力
	$("#janArea").keypress(function(event){
		$("#janArea").val(this.value.replace(/[^\d-.\n]/g,''));
		return ((event.charCode >= 48 && event.charCode <= 57)||(event.charCode == 13));
	});
	//JAN条件入力
	$("#janArea_san").keypress(function(event){
		$("#janArea_san").val(this.value.replace(/[^\d-.\n]/g,''));
		return ((event.charCode >= 48 && event.charCode <= 57)||(event.charCode == 13));
	});
	$('ul.dropdown-menu').on('click', function(event){
        event.stopPropagation();
    });
	$("#jan_reload").bind("click", function(){
		 var janlistVal = "";
         var janListTxt=$("#janArea").val();
         if (janlistCheck(janListTxt)){
             var janlistVal = janListTxt.replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
			 var l = janlistVal.split(",").length;
			 if(l>5000){
				 showMsgBox(Message.msg017,[],function(){});//JANは5000件まで入力してください。
			 }else{
				 $("#parse_button").dropdown('toggle');}
	         }else{
	        	 if(janListTxt!=""){
	        		 showMsgBox(Message.msg026,["正しいJAN"],function(){});//正しいJANを入力してください。
		         }else{
		        	 $("#parse_button").dropdown('toggle');
		         }
		    }
	 });
	 $("#jan_reload_san").bind("click", function(){
		var janlistVal = "";
		var janListTxt=$("#janArea_san").val();
		if (janlistCheck(janListTxt)){
		    var janlistVal = janListTxt.replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
			 var l = janlistVal.split(",").length;
			 if(l>5000){
				 showMsgBox(Message.msg017,[],function(){});//JANは5000件まで入力してください。
			 }else{
				 $("#parse_button_san").dropdown('toggle');}
		     }else{
		    	 if(janListTxt!=""){
		    		 showMsgBox(Message.msg026,["正しいJAN"],function(){});//正しいJANを入力してください。
		         }else{
		        	 $("#parse_button_san").dropdown('toggle');
		         }
		    }
	 });
    //add 検索条件の展開と収縮　一覧変更　zhangyunfeng 20190418 bengin
    $('#collapseOne').on('show.bs.collapse', function() {
        TemplateMainGrid.tbhot.updateSettings({
            height : $(".page-content").height() - 265
        });
    });
    $('#collapseOne').on('hidden.bs.collapse', function() {
        TemplateMainGrid.tbhot.updateSettings({
            height : $(".page-content").height() -100
        });
    });
    //add 検索条件の展開と収縮　一覧変更　zhangyunfeng 20190418 end
});

//JANチェック
var janlistCheck = function(janlist) {
	var regex = /^\d+([\s,]*\d+)*[\s,]*$/;
	var r = regex.test(janlist.split("...\r\n")[0].replace(/[ ]/g, ""));
	return r;
}
/*
 * 画面の初期化 @method initListDataList @for null @param null @return null
 */
//div,センター,CreateDate
var getInitDataList = function() {
    $("#loadMask").show();
	$.ajax({
		url:`${SystemManage.shiniseAddress}SupportPageControl/getInitDataList`,
		type:'POST',
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:RegistryGlobal.venderCD
		},
		success:function(response){
			$("#loadMask").hide();
			if(response =="false"){
				showMsgBox(Message.msg001,[],function(){});
	    	}else{
	    		var result = JSON.parse(response);
	    		
	    		getTemplateMainGrid(result[0],result[2],result[4],result[5],result[8],result[9],result[10],result[11],result[12],result[13]);
	    		
	    		//ベンダー
	    		RegistryGlobal.storeHouseList = result[0];
	    		bindData(RegistryGlobal.storeHouseList,"storeHouseCD","storeHouseName","divstoreHouseMulti","");
	    		//納品先
	    		RegistryGlobal.NohiSakiMultiList = result[1];
	    		bindData(RegistryGlobal.NohiSakiMultiList,"nohiSakiCD","nohiSakiName","divNohiSakiMulti","");
	    		//継続区分
	    		RegistryGlobal.ContinuMultiList = result[2];
	    		bindData(RegistryGlobal.ContinuMultiList,"continuCD","continuName","divContinuMulti","");
	    		//物流タイプ
	    		RegistryGlobal.ShipptypeMultiList = result[3];
	    		bindData(RegistryGlobal.ShipptypeMultiList,"shipptypeCD","shipptypeName","divShipptypeMulti","");
	    		//発注区分
	    		RegistryGlobal.OrderFlgMultiList = result[4];
	    		bindData(RegistryGlobal.OrderFlgMultiList,"orderFlgCD","orderFlgName","divOrderFlgMulti","");
	    		//発注センター
	    		RegistryGlobal.OrderCenterMultiList = result[5];
	    		bindData(RegistryGlobal.OrderCenterMultiList,"orderCenterCD","orderCenterName","divOrderCenterMulti","");
	    		//Div
	    		RegistryGlobal.DivMultiList = result[6];
	    		bindData(RegistryGlobal.DivMultiList,"divisionCD","divisionName","divMulti","");
	    		//混載
	    		RegistryGlobal.MixCDMultiList = result[7];
	    		bindData(RegistryGlobal.MixCDMultiList,"mixCD","","divMixCDMulti","");

				<!--edit ベンダー条件追加 zyf 20190402 begin-->
				//ベンダー
				RegistryGlobal.supplierList = result[14];
				bindData(RegistryGlobal.supplierList,"supplierCD","supplierName","divSupplierMulti","");
				<!--edit ベンダー条件追加 zyf 20190402 end-->
	    	}
		},
		error:function(){
			$("#loadMask").hide();
			showMsgBox(Message.msg001,[],function(){});
		}
	});
}

var arrDataInit = function() {
	arrData = [ [ {
		"chkFlg" : false,
		"deliveryPlanStartDate" : "適用開始日",
		"storeHouse" : "センター",
		"dIV" : "ディビジョン",
		"line" : "ライン",
		"department" : "部門",
		"jAN" : "JAN",
		"productName" : "商品名",
		"specName" : "規格",
		"shipptype" : "物流タイプ",
		"orderCenter" : "発注センター",
		"nohiSaki" : "納品先",
		"orderFlg" : "発注フラグ",
		"supplierCD" : "ベンダー",
		"mixID" : "混載ID",
		"titleName" : "代表商品名",
		"qput" : "単品発注ロット",
		"qunt" : "",
		"qty" : "混載発注ロット",
		"qflg" : "",
		"jyuRru" : "重量",
		"taiSeki" : "体積",
		"stockQty" : "安全在庫日数",
		"syokaQty" : "基準消化日数",
		"orderKikan" : "発注間隔",
		"nichi" : "発注LT",
		"getu" : "",
		"kayo" : "",
		"siu" : "",
		"moku" : "",
		"kin" : "",
		"doyo" : "",
		"kiJun" : "",
		"jiXuKubun" : "継続区分",
		"orderKubun" : "発注区分",
		"sanSyoKubun" : "区分&参照JAN",
		"sanSyoKubunJAN" : "",
		"dBFlg" : "DB対象フラグ",
		"systemType" : "システム区分",
		"orderType" : "推奨方法",
		"autoOrderType" : "自動区分",
		"texts" : "備考",
		"isDelFlg" : "削除"//add zhangyunfeng 削除FLG 20190611
	}, {
		"chkFlg" : false,
		"deliveryPlanStartDate" : "適用開始日",
		"storeHouse" : "センター",
		"dIV" : "ディビジョン",
		"line" : "ライン",
		"department" : "部門",
		"jAN" : "JAN",
		"productName" : "",
		"specName" : "規格",
		"shipptype" : "物流タイプ",
		"orderCenter" : "発注センター",
		"nohiSaki" : "納品先",
		"orderFlg" : "発注フラグ",
		"supplierCD" : "ベンダー",
		"mixID" : "混載ID",
		"titleName" : "代表商品名",
		"qput" : "入数",
		"qunt" : "単位",
		"qty" : "数量",
		"qflg" : "条件",
		"jyuRru" : "重量",
		"taiSeki" : "体積",
		"stockQty" : "安全在庫日数",
		"syokaQty" : "基準消化日数",
		"orderKikan" : "発注間隔",
		"nichi" : "日",
		"getu" : "月",
		"kayo" : "火",
		"siu" : "水",
		"moku" : "木",
		"kin" : "金",
		"doyo" : "土",
		"kiJun" : "基準",
		"jiXuKubun" : "継続区分",
		"orderKubun" : "発注区分",
		"sanSyoKubun" : "参照区分",
		"sanSyoKubunJAN" : "参照JAN",
		"dBFlg" : "DB対象フラグ",
		"systemType" : "システム区分",
		"orderType" : "推奨方法",
		"autoOrderType" : "自動区分",
		"texts" : "備考",
		"isDelFlg" : "削除"//add zhangyunfeng 削除FLG 20190611
	} ] ];
}

// 検索
var tableData_search = function(){
	saveArr=[];
	$("#loadMask").show();
	arrDataInit();
	var center = $("#divstoreHouseMulti").val()==null?"":$("#divstoreHouseMulti").val().join(",");
	var inStoreHouseCD = $("#divNohiSakiMulti").val()==null?"":$("#divNohiSakiMulti").val().join(",");
	var shippingTypeCD = $("#divShipptypeMulti").val()==null?"":$("#divShipptypeMulti").val().join(",");
	var mixCD = $("#divMixCDMulti").val()==null?"":$("#divMixCDMulti").val().join(",");
	var continueType = $("#divContinuMulti").val()==null?"":$("#divContinuMulti").val().join(",");
	var orderStopType = $("#divOrderFlgMulti").val()==null?"":$("#divOrderFlgMulti").val().join(",");
	var orderCenterCD = $("#divOrderCenterMulti").val()==null?"":$("#divOrderCenterMulti").val().join(",");
	var consultJan = $("#janArea_san").val().replaceAll("\n",",");
	var divCD = $("#divMulti").val()==null?"":$("#divMulti").val().join(",");
	var jan = $("#janArea").val().replaceAll("\n",",");
	var vendorStr=$("#divSupplierMulti").val()==null?"":$("#divSupplierMulti").val().join(",");
	
	$.ajax({
		url:`${SystemManage.shiniseAddress}SupportPageControl/getGridDataList`,
		type:'POST',
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:isEmpty(vendorStr)?RegistryGlobal.venderCD:vendorStr,
			center:center,
			jan:jan,
			inStoreHouseCD:inStoreHouseCD,
			shippingTypeCD:shippingTypeCD,
			mixCD:mixCD,
			continueType:continueType,
			orderStopType:orderStopType,
			orderCenterCD:orderCenterCD,
			consultJan:consultJan.trim(),
			divCD:divCD
		},
		success:function(response){
			$("#loadMask").hide();
			if(response =="false"){
				showMsgBox(Message.msg002,[],function(){});
	    	}else{
	    		var result = JSON.parse(response)[0];
	    		
	    		if(result.length == 0){
	    			showMsgBox(Message.msg003,[],function(){});
	    		}
	    		for(var i=0; i<result.length;i++){
	    			result[i].chkFlg=false;
	    		}
	    		RegistryGlobal.showList = arrData[0].concat(result);
	    		TemplateMainGrid.tbhot.loadData(RegistryGlobal.showList);
	    	}
		},
		error:function(){
			$("#loadMask").hide();
			showMsgBox(Message.msg002,[],function(){});
		}
	});
}

//EXCEL出力
//edit zhangyunfeng 既存のCSVをなくす、xlsxへ転換 20190402 begin
//var tableData_csv = function() {
var tableData_excel = function() {
	var center = $("#divstoreHouseMulti").val()==null?"":$("#divstoreHouseMulti").val().join(",");
	var inStoreHouseCD = $("#divNohiSakiMulti").val()==null?"":$("#divNohiSakiMulti").val().join(",");
	var shippingTypeCD = $("#divShipptypeMulti").val()==null?"":$("#divShipptypeMulti").val().join(",");
	var mixCD = $("#divMixCDMulti").val()==null?"":$("#divMixCDMulti").val().join(",");
	var continueType = $("#divContinuMulti").val()==null?"":$("#divContinuMulti").val().join(",");
	var orderStopType = $("#divOrderFlgMulti").val()==null?"":$("#divOrderFlgMulti").val().join(",");
	var orderCenterCD = $("#divOrderCenterMulti").val()==null?"":$("#divOrderCenterMulti").val().join(",");
	var consultJan = $("#janArea_san").val().replaceAll("\n",",");
	var divCD = $("#divMulti").val()==null?"":$("#divMulti").val().join(",");
	var jan = $("#janArea").val().replaceAll("\n",",");
	/*$.ajax({
		url:SystemManage.address+'SupportPageControl/getGridDataList',
		type:'POST',
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:RegistryGlobal.venderCD,
			center:center,
			jan:jan,
			inStoreHouseCD:inStoreHouseCD,
			shippingTypeCD:shippingTypeCD,
			mixCD:mixCD,
			continueType:continueType,
			orderStopType:orderStopType,
			orderCenterCD:orderCenterCD,
			consultJan:consultJan,
			divCD:divCD
		},
		success:function(response){
			$("#loadMask").hide();
			if(response =="false"){
				showMsgBox(Message.msg043,['出力'],function(){});//出力失敗しました。
	    	}else{
	    		var result = JSON.parse(response)[0];
	    		
	    		if(result.length == 0){
	    			showMsgBox(Message.msg003,[],function(){});
	    			return false;
	    		}
	    		var fields = ['deliveryPlanStartDate','storeHouse','storeHouseName','dIV','line','department','jAN','productName','specName','shipptype','orderCenter','orderCenterName','nohiSaki','orderFlg','supplierCD','mixID','titleName','qput','qunt','qty','qflg','jyuRru','taiSeki','stockQty','syokaQty','orderKikan','nichi','getu','kayo','siu','moku','kin','doyo','kiJun','jiXuKubun','orderKubun','sanSyoKubun','sanSyoKubunJAN','dBFlg','systemType','orderType','autoOrderType','texts'];
	    		var headNames = ['適用開始日','センターCD','センター名','ディビジョン','ライン','部門','JAN','商品名','規格','物流タイプ','発注センター','発注センター名','納品先','発注フラグ','ベンダー','混載ID','代表商品名','単品ロット入数','単品ロット単位','混載ロット数量','混載ロット条件','重量','体積','安全在庫日数','基準消化日数','発注間隔','LT日','LT月','LT火','LT水','LT木','LT金','LT土','LT基準','継続区分','発注区分','参照区分','参照JAN','DB対象フラグ','システム区分','推奨方法','自動区分','備考'];
	    		if (RegistryGlobal.venderCD != "") {
	    			fields = ['deliveryPlanStartDate','storeHouse','storeHouseName','dIV','jAN','productName','specName','shipptype','orderCenter','orderCenterName','nohiSaki','orderFlg','supplierCD','mixID','titleName','qput','qunt','qty','qflg','jyuRru','taiSeki','stockQty','syokaQty','orderKikan','nichi','getu','kayo','siu','moku','kin','doyo','kiJun','jiXuKubun','orderKubun','sanSyoKubun','sanSyoKubunJAN','dBFlg','systemType','orderType','autoOrderType','texts'];
	    			headNames = ['適用開始日','センターCD','センター名','ディビジョン','JAN','商品名','規格','物流タイプ','発注センター','発注センター名','納品先','発注フラグ','ベンダー','混載ID','代表商品名','単品ロット入数','単品ロット単位','混載ロット数量','混載ロット条件','重量','体積','安全在庫日数','基準消化日数','発注間隔','LT日','LT月','LT火','LT水','LT木','LT金','LT土','LT基準','継続区分','発注区分','参照区分','参照JAN','DB対象フラグ','システム区分','推奨方法','自動区分','備考'];
	    		}
				//json data transform csv format
			    var csv = json2csv({data:result, fields:fields, fieldNames:headNames});
			    var filename = "Support" + "-" + getNowTime() + ".csv";
			    funDownload(csv.replaceAll("\"", ""), filename);
	    	}
		},
		error:function(){
			$("#loadMask").hide();
			showMsgBox(Message.msg043,['出力'],function(){});//出力失敗しました。
		}
	});*/
	var vendorStr=$("#divSupplierMulti").val()==null?"":$("#divSupplierMulti").val().join(",");
	var url =SystemManage.address+"SupportPageControl/downloadExcel";
	var form = $("<form></form>").attr("action", url).attr("method", "post").attr("enctype", "application/x-www-form-urlencoded");
	form.append($("<input></input>").attr("type", "hidden").attr("name", "userCD").attr("value", RegistryGlobal.userCD));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "venderCD").attr("value", isEmpty(vendorStr)?RegistryGlobal.venderCD:vendorStr));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "center").attr("value", center));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "jan").attr("value", jan));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "inStoreHouseCD").attr("value", inStoreHouseCD));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "shippingTypeCD").attr("value", shippingTypeCD));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "mixCD").attr("value", mixCD));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "continueType").attr("value", continueType));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "orderStopType").attr("value", orderStopType));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "orderCenterCD").attr("value", orderCenterCD));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "consultJan").attr("value", consultJan));
	form.append($("<input></input>").attr("type", "hidden").attr("name", "divCD").attr("value", divCD));
	form.appendTo('body').submit().remove();
}

//download csv
/*var funDownload = function (content, filename) {
	if (window.navigator.msSaveOrOpenBlob) {
		// if browser is IE
	  	var blob = new Blob(['\uFEFF',decodeURIComponent(encodeURI(content))], {
	    	type: "text/csv;charset=utf-8;"
	  	});
	  	navigator.msSaveBlob(blob, filename);
	}else{
    	var eleLink = document.createElement('a');
        eleLink.download = filename;
        eleLink.style.display = 'none';
        var sjis_array = UTF8_ShiftJIS(content);
        var uint8_array = new Uint8Array(sjis_array);
        var blob = new Blob([uint8_array],{type: "text/csv"});
        eleLink.href = URL.createObjectURL(blob);   
        document.body.appendChild(eleLink);
        eleLink.click();
        document.body.removeChild(eleLink);
    }
};*/
//edit zhangyunfeng 既存のCSVをなくす、xlsxへ転換 20190402 end
//フォーマット出力
var importFrmat = function(){
	window.location.href = SystemManage.address+'SupportPageControl/getSupportFmt';
}
var checkData = function(id) {
	var fileDir = $("#" + id).val();
	var suffix = fileDir.substr(fileDir.lastIndexOf("."));
	if ("" == fileDir) {
		return false;
	}
	//if (".csv" != suffix) {
	if (".xlsx" != suffix) {
		showMsgBox(Message.msg059,[],function(){});
		return false;
	}
	return true;
}
//取り込み
var importExcel = function() {
	$("#filesupport").click();
}
var venderCheck = function() {
	var rowArr;
	var chkRst = true;
	for (var j = 0; j < saveArr.length; j++) {
		rowArr = TemplateMainGrid.tbhot.getDataAtRow(saveArr[j]);

		if (rowArr[listCol.supplierCD] == "扱い対象ではない") {
			chkRst = false;
			break;
		}
	}

	return chkRst;
}
var isRepeat = function(arr) {
	var hash = {};
	for ( var i in arr) {
		if (hash[arr[i]])
			return true;
		hash[arr[i]] = true;

	}
	return false;
} 

var repeatKeyCheck = function() {
	var rowArr;
	var chkArr = [];
	for (var j = 0; j < saveArr.length; j++) {
		rowArr = TemplateMainGrid.tbhot.getDataAtRow(saveArr[j]);
		// 適用開始日+センターCD+JAN
		chkArr.push(rowArr[listCol.deliveryPlanStartDate]
				+ rowArr[listCol.storeHouse] + rowArr[listCol.jAN]);
	}
	return isRepeat(chkArr);
}

//保存データあるかどうか
var saveDataLenChk = function() {
	if (saveArr.length == 0) {
		return false;
	} else {
		return true;
	}
}
//保存データのキーチャック
var saveDataKeyChk = function(){
	
	var chkrst = true;
	var rowArr;
	for (var j = 0; j < saveArr.length; j++) {
		tmp = [];
		
		rowArr = TemplateMainGrid.tbhot.getDataAtRow(saveArr[j]);
		//適用開始日
		if(rowArr[listCol.deliveryPlanStartDate]==null || rowArr[listCol.deliveryPlanStartDate]==""){
			setRangeCellClass(saveArr[j],listCol.deliveryPlanStartDate,"tdBgColor");
			chkrst = "適用開始日";
			return chkrst;
		}
		//センターCD
		if(rowArr[listCol.storeHouse]==null || rowArr[listCol.storeHouse]==""){
			setRangeCellClass(saveArr[j],listCol.storeHouse,"tdBgColor");
			chkrst = "センター";
			return chkrst;
		}
		//JAN
		if(rowArr[listCol.jAN]==null || rowArr[listCol.jAN]==""){
			setRangeCellClass(saveArr[j],listCol.jAN,"tdBgColor");
			chkrst = "JAN";
			return chkrst;
		}
		if(rowArr[listCol.supplierCD]==null || rowArr[listCol.supplierCD]==""){
			setRangeCellClass(saveArr[j],listCol.jAN,"tdBgColor");
			chkrst = "ベンダー";
			return chkrst;
		}
		//発注センター
		if(rowArr[listCol.orderCenter]==null || rowArr[listCol.orderCenter]==""){
			setRangeCellClass(saveArr[j],listCol.orderCenter,"tdBgColor");
			chkrst = "発注センター";
			return chkrst;
		}
		//混載ID
		if(rowArr[listCol.mixID]==null || rowArr[listCol.mixID]==""){
			setRangeCellClass(saveArr[j],listCol.mixID,"tdBgColor");
			chkrst = "混載ID";
			return chkrst;
		}
	}
	
	return chkrst;
}
//混載パラメータを獲得
var getMixChkParam = function(){

	var str = "";
	var tmp = [];
	var SelectFlg;
	var rowArr;
	for (var i = 0; i < saveArr.length; i++) {
		tmp = [];
		rowArr = TemplateMainGrid.tbhot.getDataAtRow(saveArr[i]);

		tmp.push(rowArr[listCol.jAN]); //JAN
		tmp.push(rowArr[listCol.storeHouse]==null?"":rowArr[listCol.storeHouse]); //センターCD
		tmp.push(rowArr[listCol.orderCenter]==null?"":rowArr[listCol.orderCenter]); //発注センター
		tmp.push(rowArr[listCol.mixID]); //混載ID
		tmp.push(rowArr[listCol.qflg]==null?"":rowArr[listCol.qflg]); //条件
		tmp.push(rowArr[listCol.jyuRru]); //重量
		tmp.push(rowArr[listCol.taiSeki]); //体積
		tmp.push(rowArr[listCol.jiXuKubun]==null?"":rowArr[listCol.jiXuKubun]); //継続区分
		tmp.push(rowArr[listCol.orderKubun]==null?"":rowArr[listCol.orderKubun]); //発注区分
		tmp.push(rowArr[listCol.sanSyoKubun]==null?"":rowArr[listCol.sanSyoKubun]); //参照区分
		tmp.push(rowArr[listCol.sanSyoKubunJAN]); //参照JAN
		tmp.push(rowArr[listCol.orderType]==null?"":rowArr[listCol.orderType]); //推奨方法
		tmp.push(saveArr[i]); //画面で行no
		
		//画面で選択
		if(rowArr[listCol.sanSyoKubun]!=""||rowArr[listCol.sanSyoKubunJAN]!=""){
			SelectFlg = 1;
		}else{
			SelectFlg = 0;
		}
		tmp.push(SelectFlg);
		
		str = str + tmp.join(",") + ";";
	}
	str = str.substring(0,str.length-1);
	return str;
}
//保存パラメータを獲得
var getSaveParam = function(){

	var str = "";
	var tmp = [];
	var rowArr;
	for (var j = 0; j < saveArr.length; j++) {
		tmp = [];
		
		rowArr = TemplateMainGrid.tbhot.getDataAtRow(saveArr[j]);
		if(rowArr[listCol.supplierCD] ==  '扱い対象ではない') continue;
		for(var i=1;i<rowArr.length;i++){
			//DIV、LINE、部門、商品、規格
			if(i != listCol.dIV&& //DIV
				i != listCol.line&& //LINE
				i != listCol.department&& //部門
				i != listCol.productName&& //商品
				i != listCol.specName){ //規格
				
				//センターCD、物流タイプ、発注センター、納品先、発注フラグ、ベンダー、条件、継続区分、発注区分、参照区分、DB対象フラグ、システム区分、推奨方法、自動区分
				if(i == listCol.storeHouse|| //センターCD
					i == listCol.shipptype|| //物流タイプ
					i == listCol.orderCenter|| //発注センター
					i == listCol.nohiSaki|| //納品先
					i == listCol.orderFlg|| //発注フラグ
					i == listCol.supplierCD|| //ベンダー
					i == listCol.qflg|| //条件
					i == listCol.jiXuKubun|| //継続区分
					i == listCol.orderKubun|| //発注区分
					i == listCol.sanSyoKubun|| //参照区分
					i == listCol.dBFlg|| //DB対象フラグ
					i == listCol.systemType|| //システム区分
					i == listCol.orderType|| //推奨方法
					i == listCol.autoOrderType){ //自動区分
					
					tmp.push(rowArr[i]==null?"":rowArr[i].split("-")[0]);
				}else{
					tmp.push(rowArr[i]);
				}
			}
		}
		str = str + tmp.join(",") + ";";
	}
	if(str.length > 1)
		str = str.substring(0,str.length-1);

	return str;
}
//cell's background-color set
var setRangeCellClass = function(row, col, classname) {
	TemplateMainGrid.tbhot.setCellMeta(row, col, "className", classname);
	TemplateMainGrid.tbhot.render();
}
//cell's background-color remove
var removeRangeCellClass = function(row, col, classname) {

	if ("className" in TemplateMainGrid.tbhot.getCellMeta(row, col)) {
		TemplateMainGrid.tbhot.removeCellMeta(row, col, "className", classname)
		TemplateMainGrid.tbhot.render();
	}
}
var removeAllCellClass = function(row,classname) {
	removeRangeCellClass(row,listCol.deliveryPlanStartDate,classname);
	removeRangeCellClass(row,listCol.storeHouse,classname);
	removeRangeCellClass(row,listCol.jAN,classname);
	removeRangeCellClass(row,listCol.orderCenter,classname);
	removeRangeCellClass(row,listCol.mixID,classname);
	removeRangeCellClass(row,listCol.qflg,classname);
	removeRangeCellClass(row,listCol.jyuRru,classname);
	removeRangeCellClass(row,listCol.taiSeki,classname);
	removeRangeCellClass(row,listCol.orderKubun,classname);
	removeRangeCellClass(row,listCol.sanSyoKubunJAN,classname);
	removeRangeCellClass(row,listCol.orderType,classname);
}
//混載パラメータを獲得
var getJanChkParam = function(){
	var str = "";
	var tmp = [];
	var rowArr;
	for (var i = 0; i < saveArr.length; i++) {
		tmp = [];
		
		rowArr = TemplateMainGrid.tbhot.getDataAtRow(saveArr[i]);
		tmp.push(rowArr[listCol.jAN]); //JAN
		tmp.push(rowArr[listCol.storeHouse]==null?"":rowArr[listCol.storeHouse]); //センターCD
		tmp.push(rowArr[listCol.mixID]==null?"":rowArr[listCol.mixID]); //混載ID
		str = str + tmp.join(",") + ";";
	}
	str = str.substring(0,str.length-1);
	return str;
}
//チェック混載
//var mixChk_confirm = function() {//edit zhangyunfeng　削除機能追加 2019/06/10
var mixChk_confirm = function(saveFlg) {
	if (!saveDataLenChk()) {
		showMsgBox(Message.msg053, [], function() {
		});
		$("#loadMask").hide();
		return;
	}
	//前回チェックのエラー色削除
	$(".tdBgColor").removeClass("tdBgColor");

	var sdkc = saveDataKeyChk();
	if (sdkc != true) {
		if(sdkc=="ベンダー"){
			showMsgBox(Message.msg056, [], function() {});
		}else{
			showMsgBox(Message.msg026, [ "「"+sdkc+"」" ], function() {});
		}
		$("#loadMask").hide();
		return;
	}
	//バンドル商品チェック
	var jan = getJanChkParam();
	if(jan == "") {
		$("#loadMask").hide();
		return;
	}
	//登録の場合、バンドル商品チェック
	if(saveFlg==0) {
		$.ajax({
			url: SystemManage.address + 'SupportPageControl/janDataChk',
			type: 'POST',
			dataType: "TEXT",
			data: {
				userCD: RegistryGlobal.userCD,
				venderCD: RegistryGlobal.venderCD,
				jan: jan
			},
			success: function (response) {
				if (response == "false") {
					$("#loadMask").hide();
					showMsgBox(Message.msg043, ['バンドル商品チェック'], function () {
					});
				} else {
					if (repeatKeyCheck()) {
						$("#loadMask").hide();
						showMsgBox(Message.msg046, [], function () {
						});
						return;
					}
					if (!venderCheck()) {
						$("#loadMask").hide();
						//edit zhangyunfeng　削除機能追加 2019/06/10
						/*showConfMsg(Message.msg048, [], mixChk_check, function() {
                            return false;
                        });*/
						showConfMsg(Message.msg048, [], function () {
							if (response != "true") {
								if (response.split(",")[3] == "1") {
									$("#loadMask").hide();
									showMsgBox(Message.msg051, [response.substring(0, response.length - 2)], function () {
									});
								} else {
									showBundleAskMsg(Message.msg052, [response.substring(0, response.length - 2)], function () {
										mixChk_check(saveFlg);
									}, function () {
										var jan = response.split(",")[1];
										var center = response.split(",")[0];
										var rowArr;
										for (var i = 0; i < saveArr.length; i++) {
											rowArr = TemplateMainGrid.tbhot.getDataAtRow(saveArr[i]);
											if (rowArr[listCol.jAN] == jan && rowArr[listCol.storeHouse] == center) {
												TemplateMainGrid.tbhot.selectCellByProp(i + 2, "jAN");
												break;
											}
										}
										return;
									});
								}
							} else {
								mixChk_check(saveFlg);
							}
						}, function () {
							return;
						});
					} else {
						//mixChk_check();//edit zhangyunfeng　削除機能追加 2019/06/10
						if (response != "true") {
							$("#loadMask").hide();
							if (response.split(",")[3] == "1") {
								$("#loadMask").hide();
								showMsgBox(Message.msg051, [response.substring(0, response.length - 2)], function () {
								});
							} else {
								showBundleAskMsg(Message.msg052, [response.substring(0, response.length - 2)], function () {
									mixChk_check(saveFlg);
								}, function () {
									var jan = response.split(",")[1];
									var center = response.split(",")[0];
									var rowArr;
									for (var i = 0; i < saveArr.length; i++) {
										rowArr = TemplateMainGrid.tbhot.getDataAtRow(saveArr[i]);
										if (rowArr[listCol.jAN] == jan && rowArr[listCol.storeHouse] == center) {
											TemplateMainGrid.tbhot.selectCellByProp(i + 2, "jAN");
											break;
										}
									}
									return;
								});
							}
						} else {
							mixChk_check(saveFlg);
						}
					}

				}
			},
			error: function () {
				$("#loadMask").hide();
				showMsgBox(Message.msg043, ['バンドル商品チェック'], function () {
				});//登録チェック失敗しました。
			}
		});
	}else{//削除の場合、バンドル商品チェックしない
		if (repeatKeyCheck()) {
			$("#loadMask").hide();
			showMsgBox(Message.msg046, [], function () {
			});
			return;
		}
		if (!venderCheck()) {
			$("#loadMask").hide();
			showConfMsg(Message.msg048, [], function () {
				mixChk_check(saveFlg);
			}, function () {
				return;
			});
		}else{
			mixChk_check(saveFlg);
		}
	}
}
//var mixChk_check = function(){//edit zhangyunfeng　削除機能追加 2019/06/10
var mixChk_check = function(saveFlg){
	if($("#loadMask").css('display') === 'none'){ 
		$("#loadMask").show();
	}
	var mixparam = getMixChkParam();
	$.ajax({
		url:SystemManage.address+'SupportPageControl/mixChkDataList',
		type:'POST',
		dataType:"TEXT",
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:RegistryGlobal.venderCD,
			saveData:mixparam
		},
		success:function(response){
			if(response =="false"){
				showMsgBox(Message.msg004,[],function(){});
	    	}else{
	    		
	    		var chkrst = true;
	    		var msg="";
	    		var msgparam="";
	    		var rowchk;
	    		var rstJson;
	    		var rstJsonArr = JSON.parse(response)[0];
	    		for(var i=0;i<rstJsonArr.length;i++){
	    			rstJson = rstJsonArr[i];
	    			rowchk = parseInt(rstJson.rowIndex);
                    //混載ロット条件
                    if(rstJson.termsCDChk=="1") {
                    	setRangeCellClass(rowchk,listCol.qflg,"tdBgColor");
                    	chkrst=false;
                    	msg=Message.msg005;
                    	msgparam=rstJson.mixCD;
		    			break;
                    }else if(rstJson.termsCDChk=="2"){
                    	setRangeCellClass(rowchk,listCol.qflg,"tdBgColor");
                    	chkrst=false;
                    	msg=Message.msg006;
                    	msgparam=rstJson.mixCD;
		    			break;
                    }
                    
                    //重量と体積
                    if(rstJson.allWMChk=="1"){
                    	setRangeCellClass(rowchk,listCol.jyuRru,"tdBgColor");
		    			setRangeCellClass(rowchk,listCol.taiSeki,"tdBgColor");
		    			chkrst=false;
                    	msg=Message.msg007;
                    	msgparam=rstJson.mixCD;
		    			break;
                    }
                    //混載区分別重量 
                    if(rstJson.caseWeightChk=="1"){
                    	setRangeCellClass(rowchk,listCol.jyuRru,"tdBgColor");
                    	chkrst=false;
                    	msg=Message.msg008;
                    	msgparam=rstJson.mixCD;
		    			break;
                    }else if(rstJson.caseWeightChk=="2"){
                    	setRangeCellClass(rowchk,listCol.jyuRru,"tdBgColor");
                    	chkrst=false;
                    	msg=Message.msg009;
                    	msgparam=rstJson.mixCD;
 		    			break;
                    }
                    //混載区分別体積 
                    if(rstJson.caseMeasurementChk=="1"){
                    	setRangeCellClass(rowchk,listCol.taiSeki,"tdBgColor");
                    	chkrst=false;
                    	msg=Message.msg010;
                    	msgparam=rstJson.mixCD;
 		    			break;
                    }else if(rstJson.caseMeasurementChk=="2"){
                    	setRangeCellClass(rowchk,listCol.taiSeki,"tdBgColor");
                    	chkrst=false;
                    	msg=Message.msg011;
                    	msgparam=rstJson.mixCD;
 		    			break;
                    }

                    //発注区分 
                    if(rstJson.selectFlg=="1" && rstJson.noSetOrderStopType!="0"){
                    	setRangeCellClass(rowchk,listCol.orderKubun,"tdBgColor");
                    	chkrst=false;
                    	msg=Message.msg019;
		    			break;
                    }
                    
                    //区分と参照JANチェック
                    if(rstJson.selectFlg=="1" && rstJson.isTwoHava!="0"){
                        //入力チェック
		    			setRangeCellClass(rowchk,listCol.sanSyoKubunJAN,"tdBgColor");
		    			chkrst=false;
                    	msg=Message.msg012;
                    	msgparam=rstJson.orderCenterCD+","+rstJson.jAN;
 		    			break;
                    }else if(rstJson.selectFlg=="1" && rstJson.isExist=="1"){
                    	//存在チェック
		    			setRangeCellClass(rowchk,listCol.sanSyoKubunJAN,"tdBgColor");
		    			chkrst=false;
                    	msg=Message.msg013;
                    	msgparam=rstJson.orderCenterCD+","+rstJson.jAN;
 		    			break;
                    }else if(rstJson.selectFlg=="1" && rstJson.isSame=="0"){
                    	//JANと同じチェック
		    			setRangeCellClass(rowchk,listCol.sanSyoKubunJAN,"tdBgColor");
		    			chkrst=false;
                    	msg=Message.msg014;
                    	msgparam=rstJson.orderCenterCD+","+rstJson.jAN;
 		    			break;
                    }

                    //推奨方法
                    if(rstJson.orderTypeChk=="1"){
                    	setRangeCellClass(rowchk,listCol.orderType,"tdBgColor");
                    	chkrst=false;
                    	msg=Message.msg015;
                    	msgparam=rstJson.mixCD;
 		    			break;
                    }else if(rstJson.orderTypeChk=="2"){
                    	setRangeCellClass(rowchk,listCol.orderType,"tdBgColor");
                    	chkrst=false;
                    	msg=Message.msg016;
                    	msgparam=rstJson.mixCD;
 		    			break;
                    }
	    		}
	    		
	    		if(chkrst){		
		    		var saveparam = getSaveParam();
		    		if(saveparam == ""){
		    			$("#loadMask").hide();
		    			showMsgBox(Message.msg004,[],function(){});
		    		}else{
		    			//tableData_confirm(saveparam);//edit zhangyunfeng　削除機能追加 2019/06/10
						tableData_confirm(saveparam,saveFlg);
		    		}
	    		}else{
	    			$("#loadMask").hide();
	    			showMsgBox(msg,msgparam.split(","),function(){});
	    		}
	    	}
		},
		error:function(){
			$("#loadMask").hide();
			showMsgBox(Message.msg043,['データ取得'],function(){});
			
		}
	});
}
//保存
//var tableData_confirm = function(str){//edit zhangyunfeng　削除機能追加 2019/06/10
var tableData_confirm = function(str,saveFlg){
	if($("#loadMask").css('display') === 'none'){ 
		$("#loadMask").show();
	}
	$.ajax({
		url:SystemManage.address+'SupportPageControl/saveGridDataList',
		type:'POST',
		dataType:"TEXT",
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:RegistryGlobal.venderCD,
			saveData:str,
			saveFlg:saveFlg//add zhangyunfeng　削除機能追加 2019/06/10
		},
		success:function(response){
			$("#loadMask").hide();
			if(saveFlg==0){
				if(response =="false"){
					showMsgBox(Message.msg028,[],function(){});
				}else if(response =="ago"){
					showMsgBox(Message.msg047,[],function(){});
				}else{
					showMsgBox(Message.msg029,[],function(){});
				}
			}else{//add zhangyunfeng　削除機能追加 2019/06/10
				if(response =="false"){
					showMsgBox(Message.msg037,[],function(){});
				}else if(response =="ago"){
					showMsgBox(Message.msg047,[],function(){});
				}else{
					showMsgBox(Message.msg038,[],function(){});
				}
			}
		},
		error:function(){
			$("#loadMask").hide();
			if(saveFlg==0) {
				showMsgBox(Message.msg028, [], function () {
				});
			}else{
				showMsgBox(Message.msg038,[],function(){});
			}
		}
	});
}
//センターとディビジョンの連動
var centerToDiv = function(){
	var center = $("#divstoreHouseMulti").val()==null?"":$("#divstoreHouseMulti").val().join(",");
	
	$.ajax({
		url:`${SystemManage.shiniseAddress}SupportPageControl/centerToDiv`,
		type:'POST',
		dataType:"TEXT",
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		data:{
			userCD:RegistryGlobal.userCD,
			venderCD:RegistryGlobal.venderCD,
			center:center
		},
		success:function(response){
			if(response =="false"){
				showMsgBox(Message.msg043,['DIV取得'],function(){});//DIV取得失敗しました。
	    	}else{
	    		var result = JSON.parse(response);
	    		
	    		//Div
	    		RegistryGlobal.DivMultiList = result[0];
	    		bindData(RegistryGlobal.DivMultiList,"divisionCD","divisionName","divMulti","");
	    	}
		},
		error:function(){
			showMsgBox(Message.msg043,['DIV取得'],function(){});
		}
	});
}

var divAndStoreHouseInit = function(){	
	$('#divMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て  ',
		allSelectedText: '全Div',
		nonSelectedText: '全て ',
		nSelectedText: 'Div',
		numberDisplayed:1
	});
	$('#divstoreHouseMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て  ',
		allSelectedText: '全センター',
		nonSelectedText: '全て ',
		nSelectedText: 'センター',
		numberDisplayed:1,
		onChange : function(element, checked) {
			centerToDiv();
        }
	});	
	
	$('#divShipptypeMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全物流タイプ',
		nonSelectedText: '全て',
		nSelectedText: ' 物流タイプ',
		numberDisplayed:1
	});
	$('#divNohiSakiMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全納品先',
		nonSelectedText: '全て',
		nSelectedText: ' 納品先',
		numberDisplayed:1
	});	
	//divContinuMulti
	$('#divContinuMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全継続区分',
		nonSelectedText: '全て',
		nSelectedText: ' 継続区分',
		numberDisplayed:1
	});
	//divOrderFlgMulti
	//発注区分
	$('#divOrderFlgMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全発注区分',
		nonSelectedText: '全て',
		nSelectedText: ' 発注区分',
		numberDisplayed:1
	});
	//divOrderCenterMulti
	$('#divOrderCenterMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全発注センター',
		nonSelectedText: '全て',
		nSelectedText: ' 発注センター',
		numberDisplayed:1
	});		
	//divMixCDMulti
	$('#divMixCDMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '混載ID',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全混載',
		nonSelectedText: '全て',
		nSelectedText: ' 混載',
		numberDisplayed:1
	});
	//ベンダー
	$('#divSupplierMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: 'ベンダー',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全ベンダー',
		nonSelectedText: '全て',
		nSelectedText: 'ベンダー',
		numberDisplayed:1
	});
}

function getQueryString(name){
     var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
     var r = window.location.search.substr(1).match(reg);
     if(r!=null){    	 
    	 return unescape(r[2]);
     }else{    	 
    	 return null;
     }
}

/////////////////////////////////////////////////////////
//bindDataとonchange事件
//data     Json
//valuecol 項目CDString
//labelcol 項目名 String
//id       String
//multiple  "all":全選(複数選択のみ有効) 
//          "single":初期に選択項目
//          Array:選択したデータ  []:すべて選択しない
////////////////////////////////////////////////////////
function bindData(filtdata ,valuecol, labelcol, id, multiple){ 
   var data = filtdata;
   var options=[];
   for(var i=0;i<data.length;i++){
	   if(labelcol == ""){
		   options.push({
			   label:data[i][valuecol],
			   title:data[i][valuecol],
			   value:data[i][valuecol]
		   });
	   }else{
		   options.push({
			   label:data[i][valuecol]+"-"+data[i][labelcol],
			   title:data[i][labelcol],
			   value:data[i][valuecol]
		   });
	   }
   }
   $('#'+id).multiselect('dataprovider', options);
   if(multiple=="all"){
  	 	//全選  及び　onChange trigger
  	 	$('#'+id).multiselect('selectAll',false,true);
  	 	$('#'+id).multiselect('updateButtonText');				   
   } else if(multiple=="single"){
  	 	//第一の項目選択　及び　onChange trigger
  	 	$('#'+id).multiselect('select',options[0].value,true);			     
   } else {
	   	//選択設定   及び　onChange trigger
		$('#'+id).multiselect('select',multiple,true);
   }
}