<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>jp.trial</groupId>
	<artifactId>DCOrderConsignment</artifactId>
	<packaging>war</packaging>
	<version>0.0.2-SNAPSHOT</version>
	<name>DCOrderConsignment Maven Webapp</name>
	<url>http://maven.apache.org</url>
	<!-- zhangyunfeng 2019/04/25　jdk11+tomcat9の関連jar文件バージョンアップ -->
	<properties>
		<project.build.finalName>DCOrderConsignment</project.build.finalName>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
		<java.version>11</java.version>
	</properties>
  	<dependencies>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>4.0.1</version>
			<scope>provided</scope>
		</dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.11.2</version>
        </dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.11.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-web</artifactId>
			<version>2.11.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-1.2-api</artifactId>
			<version>2.11.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.struts</groupId>
			<artifactId>struts2-core</artifactId>
			<version>2.5.20</version>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.5</version>
		</dependency>
		<dependency>
		  <groupId>junit</groupId>
		  <artifactId>junit</artifactId>
		  <version>4.12</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.3</version>
		</dependency>
<!--		<dependency>-->
<!--		  <groupId>com.microsoft.sqlserver</groupId>-->
<!--		  <artifactId>sqljdbc4</artifactId>-->
<!--		  <version>4.0</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>sqljdbc4</artifactId>
			<version>4-3.0</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/lib/sqljdbc4-3.0.jar</systemPath>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.1.0</version>
		</dependency>
		<dependency>
		   <groupId>net.sourceforge.javacsv</groupId>
		   <artifactId>javacsv</artifactId>
		   <version>2.0</version>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
			<version>3.24.0-GA</version>
		</dependency>
  	</dependencies>
  	<build>
    	<finalName>DCOrderConsignment</finalName>
<!--		<resources>-->
<!--			<resource>-->
<!--				<directory>${basedir}/src/main/java</directory>-->
<!--				<includes>-->
<!--					<include>**/*.xls</include>-->
<!--				</includes>-->
<!--			</resource>-->
<!--		</resources>-->
	  	<plugins>
			<!-- mvn war 共通jar -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.2.2</version>
				<!--<configuration>
					<packagingExcludes>
						WEB-INF/lib/commons-codec-1.12.jar,
						WEB-INF/lib/commons-collections4-4.3.jar,
						WEB-INF/lib/commons-fileupload-1.4.jar,
						WEB-INF/lib/commons-io-2.6.jar,
						WEB-INF/lib/commons-lang3-3.8.1.jar,
						WEB-INF/lib/commons-math3-3.6.1.jar,
						WEB-INF/lib/fastjson-1.2.3.jar,
						WEB-INF/lib/freemarker-2.3.28.jar,
						WEB-INF/lib/gson-2.8.5.jar,
						WEB-INF/lib/hamcrest-core-1.3.jar,
						WEB-INF/lib/javacsv-2.0.jar,
						WEB-INF/lib/javassist-3.24.0-GA.jar,
						WEB-INF/lib/junit-4.12.jar,
						WEB-INF/lib/poi-4.1.0.jar,
						WEB-INF/lib/sqljdbc4-4.0.jar
					</packagingExcludes>
				</configuration>-->
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
				  <source>${java.version}</source>
				  <target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.sonarsource.scanner.maven</groupId>
				<artifactId>sonar-maven-plugin</artifactId>
				<version>3.5.0.1254</version>
			</plugin>
	  	</plugins>
  	</build>
	<profiles>
		<profile>
			<id>sonar</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<!-- Optional URL to server. Default value is http://localhost:9000 -->
				<sonar.login>user</sonar.login>
				<sonar.password>user</sonar.password>
				<sonar.host.url>http://***********:9000</sonar.host.url>
				<sonar.projectVersion>${project.version}</sonar.projectVersion>
				<sonar.sourceEncoding>UTF-8</sonar.sourceEncoding>
				<sonar.sources>src/main/java,src/main/resources,src/main/webapp/DBMainteAreaPage,src/main/webapp/DBMainteAuthorityPage,src/main/webapp/DBMainteBizTalksPage,src/main/webapp/DBMainteEventPage,src/main/webapp/DBMainteNotDeliveryPage,src/main/webapp/DBMainteOrderSplitPage,src/main/webapp/DBMainteSupportPage,src/main/webapp/DBMainteVenShousePage,src/main/webapp/DBOrderSupportPage,src/main/webapp/DCOrderConPage,src/main/webapp/ErrorPage,src/main/webapp/MenuPage</sonar.sources>
			</properties>
		</profile>
	</profiles>

</project>
