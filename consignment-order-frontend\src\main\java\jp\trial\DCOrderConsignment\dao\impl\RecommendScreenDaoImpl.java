package jp.trial.DCOrderConsignment.dao.impl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import jp.trial.DCOrderConsignment.common.BaseDao;
import jp.trial.DCOrderConsignment.dao.RecommendScreenDao;
import jp.trial.DCOrderConsignment.model.*;

import org.apache.log4j.Logger;

public class RecommendScreenDaoImpl extends BaseDao implements RecommendScreenDao {
	/** Connection */
	private Connection conn;
	/** CallableStatement */
	private CallableStatement cstmt;
	/** ResultSet */
	private ResultSet rs = null;
	/** Logger */
	private static final Logger logger=Logger.getLogger(Thread.currentThread().getStackTrace()[1].getClassName());
	
	/** DIVとセンターとFOUR WEEKSを取得する*/
	@Override
	public List<Object> getInitDataList(String userCD,String employeeCD) throws Exception {
		// DB接続確立
		conn=super.getConnection();
		List<Object> objList = new ArrayList<Object>();
		List<DIVModel> divList = new ArrayList<DIVModel>();
		List<StoreHouseModel> storeHouseList = new ArrayList<StoreHouseModel>();
		List<SupplierModel> supplierList = new ArrayList<SupplierModel>();
		try {
			cstmt = conn.prepareCall("{call PROC_DCOrderAutoSys_GetDivAndStoreHouseList_Web (?,?)}");
			cstmt.setString(1, employeeCD);
			cstmt.setString(2, userCD);
			// ストアドプロシージャ実行
			cstmt.execute();
			rs = cstmt.getResultSet();
			DIVModel divModel;
			while(rs.next()){
				divModel = new DIVModel();
				divModel.setDivisionCD(rs.getString("divisionCD"));
				divModel.setDivisionName(rs.getString("divisionName"));
				divList.add(divModel);
			}
			if(cstmt.getMoreResults()){
				rs = cstmt.getResultSet();
				StoreHouseModel storeHouseModel;
				while(rs.next()){
					storeHouseModel = new StoreHouseModel();
					storeHouseModel.setStoreHouseCD(rs.getString("storeHouseCD"));
					storeHouseModel.setStoreHouseName(rs.getString("storeHouseName"));
					storeHouseList.add(storeHouseModel);
				}
			}
			
			cstmt = conn.prepareCall("{call PROC_DCOrderAutoSys_GetFourWeek_Select}");
			cstmt.execute();
			rs = cstmt.getResultSet();
			FourWeekModel fourWeekModel = new FourWeekModel();
			if(rs.next()){
				fourWeekModel.setFirst(rs.getString(1));
				fourWeekModel.setSecond(rs.getString(2));
				fourWeekModel.setThird(rs.getString(3));
				fourWeekModel.setFourth(rs.getString(4));
			}
			// add ベンダー条件追加 zhangyunfeng 2019/04/08 begin
			cstmt = conn.prepareCall("{call [LOI_DBOrder].dbo.PROC_DBMaint_Valid_GetSupplierInfo (?,?)}");
			cstmt.setString(1, userCD);
			cstmt.setString(2, "");
			cstmt.execute();
			rs = cstmt.getResultSet();
			SupplierModel supplierModel;
			while(rs.next()){
				supplierModel = new SupplierModel();
				supplierModel.setSupplierCD(rs.getString("SupplierCD"));
				supplierModel.setSupplierName(rs.getString("SupplierName"));
				supplierList.add(supplierModel);
			}
			objList.add(divList);
			objList.add(storeHouseList);
			objList.add(fourWeekModel);
			objList.add(supplierList);
			//add ベンダー条件追加 zhangyunfeng 2019/04/08 end
		} catch (Exception e) {
			logger.error(e.getMessage());
			e.printStackTrace();
			throw new Exception("false");
		}finally{
			// DB接続解放
			super.closeAll(conn, cstmt, null);
		}
		return objList;
	}
	
	/** 検索*/
	@Override
	public List<Object> tableData_search(String divStr, String storeHouseStr, String janStr, String userCD, String dataFlg,String employeeCD,String venderFlg,String chkTimesFlg) throws Exception {
		// DB接続確立
		conn=super.getConnection();
		List<Object> objList = new ArrayList<Object>();
		List<ShowModel> showList = new ArrayList<ShowModel>();
		List<ConfirmModel> confirmList = new ArrayList<ConfirmModel>();
		try {
			cstmt = conn.prepareCall("{call PROC_DCOrderAutoSys_GetSearchInfo_Web (?,?,?,?,?,?,?,?)}");
			int pram = 1;
			cstmt.setString(pram++, divStr);
			cstmt.setString(pram++, storeHouseStr);
			cstmt.setString(pram++, janStr);
			cstmt.setString(pram++, employeeCD);
			cstmt.setString(pram++, userCD);
			cstmt.setString(pram++, dataFlg);
			cstmt.setString(pram++, venderFlg);
			cstmt.setString(pram++, chkTimesFlg);
			// ストアドプロシージャ実行
			cstmt.execute();
			
			ResultSet rs = cstmt.getResultSet();
			ShowModel showModel;
			while(rs.next()){
				showModel = new ShowModel();
				showModel.setChkFlg(rs.getString("ChkFlg"));
				showModel.setStoreHouse(rs.getString("StoreHouse"));
				showModel.setMixCD(rs.getString("MixCD"));
				showModel.setDIV(rs.getString("DIV"));
				showModel.setJAN(rs.getString("JAN"));
				showModel.setProductName(rs.getString("ProductName"));
				showModel.setSpecName(rs.getString("SpecName"));
				showModel.setWareHouseQty(rs.getString("WareHouseQty"));
				showModel.setWeek1(rs.getString("week1"));
				showModel.setWeek2(rs.getString("week2"));
				showModel.setWeek3(rs.getString("week3"));
				showModel.setWeek4(rs.getString("week4"));
				showModel.setTweek(rs.getString("Tweek"));
				showModel.setOrderPlanDate(rs.getString("OrderPlanDate"));
				showModel.setDeliveryPlanQty(rs.getString("DeliveryPlanQty"));
				showModel.setSalesDay(rs.getString("SalesDay"));
				showModel.setCenterSalesDay(rs.getString("CenterSalesDay"));
				showModel.setOrderSalesDay(rs.getString("OrderSalesDay"));
				showModel.setOrderPlanQty(rs.getString("OrderPlanQty"));
				showModel.setCaseQty(rs.getString("CaseQty"));
				showModel.setDeliveryPlanDate(rs.getString("DeliveryPlanDate"));
				showModel.setFlgConsultJAN(rs.getString("FlgConsultJAN"));
				showModel.setComment(rs.getString("comment"));
				showModel.setOrderCaseID(rs.getString("OrderCaseID"));
				showModel.setTimes(rs.getString("Times"));
				showModel.setOrderCaseDetailID(rs.getString("OrderCaseDetailID"));
				showModel.setVerNO(rs.getString("VerNO"));
				showModel.setStoreHouseCD(rs.getString("StoreHouseCD"));
				showModel.setInnerCaseQty(rs.getString("InnerCaseQty"));
				showModel.setApprovalFlag(rs.getString("ApprovalFlag"));
				showModel.setSplitSetFlg(rs.getString("SplitSetFlg"));
				showModel.setProLotQtyCheck(rs.getString("ProLotQtyCheck"));
				showModel.setNotDeliveryCheck(rs.getString("NotDeliveryCheck"));
				showModel.setAvgDigestQty(rs.getString("AvgDigestQty"));
				showModel.setOrderCaseCreateDate(rs.getString("OrderCaseCreateDate"));
				showModel.setOrderUnit(rs.getString("OrderUnit"));
				showModel.setOrderStatus(rs.getString("OrderStatus"));
				showModel.setLotQty(rs.getString("LotQty"));
				showModel.setShippingTypeCD(rs.getString("ShippingTypeCD"));
				showModel.setOrderUnitQty(rs.getString("OrderUnitQty"));
				showModel.setLotUnit(rs.getString("LotUnit"));
				showModel.setTerms(rs.getString("Terms"));
				showModel.setIsVerNODiff(rs.getString("IsVerNODiff"));
				showModel.setOrderPlanQtyAfter(rs.getString("OrderPlanQtyAfter"));
				showModel.setAdjustRate(rs.getString("AdjustRate"));
				showModel.setCurrentOrderFlg(rs.getString("CurrentOrderFlg"));
				showList.add(showModel);
			}
			
			if(cstmt.getMoreResults()){
				rs = cstmt.getResultSet();
				ConfirmModel confirmModel;
				while(rs.next()){
					confirmModel = new ConfirmModel();
					confirmModel.setMixCD(rs.getString("MixCD"));
					confirmModel.setStoreHouseCD(rs.getString("StoreHouseCD"));
					confirmModel.setVenderCD(rs.getString("VenderCD"));
					confirmModel.setNotDeliveryWeekDay(rs.getString("NotDeliveryWeekDay"));
					confirmModel.setNotDeliveryBeginDate(rs.getString("NotDeliveryBeginDate"));
					confirmModel.setNotDeliveryEndDate(rs.getString("NotDeliveryEndDate"));
					confirmList.add(confirmModel);
				}
			}
			objList.add(showList);
			objList.add(confirmList);
		} catch (Exception e) {
			logger.error(e.getMessage());
			e.printStackTrace();
			throw new Exception("false");
		}finally{
			// DB接続解放
			super.closeAll(conn, cstmt, null);
		}
		return objList;
	}
	
	/** 確定*/
	@Override
	public String tableData_confirm(String sendData, String userCD,String employeeCD) throws Exception {
		// DB接続確立
		conn=super.getConnection();
		String returnValue="false";
		try {
			cstmt = conn.prepareCall("{call PROC_DCOrderAutoSys_OrderCaseApply_Insert_Web (?,?,?)}");
			int pram = 1;
			cstmt.setString(pram++, sendData);
			cstmt.setString(pram++, userCD);
			cstmt.setString(pram++, employeeCD);
			// ストアドプロシージャ実行
			boolean moreResultSets=cstmt.execute();
			int i=0;
			while(moreResultSets){
				i=i+1;
				ResultSet rs=cstmt.getResultSet();
				while(rs.next()){
					if(i==2&&rs.getInt(1)==1){
						returnValue="confirm";
					}else if(i==3&&rs.getInt(1)==2){
						returnValue="stop";
					}else if(i>2&&rs.getInt(1)==1){//"RETURN"
						returnValue="true";
					}else if(i>2&&rs.getInt(1)==0){//"RETURN"
						returnValue="false";
					}
				}
				rs.close();
				moreResultSets=cstmt.getMoreResults();
			}
		}
		catch (Exception e) {
			logger.error(e.getMessage());
			e.printStackTrace();
			//throw new Exception("false");
		}finally{
			// DB接続解放
			super.closeAll(conn, cstmt, null);
		}
		return returnValue;
	}

	/**センター入出荷予定結果一覧*/
	public List<InOutPlanModel> GetInOutPlanInfo(String jan,String storeHouseCD) throws Exception{
		// データベースの接続 OPEN
		conn = super.getConnection();
		List<InOutPlanModel> inOutPlanModelList = new ArrayList<InOutPlanModel>();
		String sql = "{Call PROC_DCOrderAutoSys_CenterDeliveryPlan_Select_Web(?,?)}";
		try {
			// 実行SQL
			cstmt = conn.prepareCall(sql);
			cstmt.setString(1, storeHouseCD);
			cstmt.setString(2, jan);
			// ストアドプロシージャ実行
			ResultSet rs = cstmt.executeQuery();
			InOutPlanModel inOutPlanModel;
			while(rs.next()){
				inOutPlanModel = new InOutPlanModel();
				inOutPlanModel.setStoreHouseCD(rs.getString("StoreHouseCD"));
				inOutPlanModel.setStoreHouseName(rs.getString("StoreHouseName"));
				inOutPlanModel.setPlanName(rs.getString("PlanName"));
				inOutPlanModel.setSlipNo(rs.getString("SlipNo"));
				inOutPlanModel.setDeliveryPlanDate(rs.getString("DeliveryPlanDate"));
				inOutPlanModel.setDeliveryPlanQty(rs.getString("DeliveryPlanQty"));
				inOutPlanModel.setAvgDigestQty(rs.getString("AvgDigestQty"));
				inOutPlanModel.setCenterCD(rs.getString("CenterCD"));
				inOutPlanModel.setJAN(rs.getString("JAN"));
				inOutPlanModel.setSaleDays(rs.getString("SaleDays"));
				inOutPlanModelList.add(inOutPlanModel);
			}
		}catch (Exception e) {
			logger.error(e.getMessage());
			e.printStackTrace();
			throw new Exception("false");
		} finally {
			// データベースの接続　CLOSE
			super.closeAll(conn, cstmt, null);
		}
		return inOutPlanModelList;
	}
}
