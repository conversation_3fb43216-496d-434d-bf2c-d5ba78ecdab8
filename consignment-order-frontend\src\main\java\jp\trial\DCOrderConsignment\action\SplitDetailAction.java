package jp.trial.DCOrderConsignment.action;

import java.io.IOException;

import jp.trial.DCOrderConsignment.common.BaseAction;
import jp.trial.DCOrderConsignment.service.SplitDetailService;
import jp.trial.DCOrderConsignment.service.impl.SplitDetailServiceImpl;

public class SplitDetailAction extends BaseAction{
	private static final long serialVersionUID = -6482322206006561307L;
	/** Service */
	private SplitDetailService splitService;
	public String orderCaseID;
	public String sendData;
	public String userCD;
	// ストラクター
	public SplitDetailAction() throws IOException {
		super();
		splitService = new SplitDetailServiceImpl();
	}
	/**
	 * Description: 初期化：伝票分け一覧
	 * <AUTHOR>
	 * Date: 2017/01/05
	 */
	public void GetsplitDetailInfo() {
		String res = "";
		try {
			res = splitService.GetSplitDetailInfo(orderCaseID);
			super.print.print(res);
		}catch(Exception e){
			// データをプリンター
			super.print.print("false");
		}finally {
			super.print.flush();
			super.print.close();
		}
	}

	/**
	 * Description: 確定
	 * <AUTHOR>
	 * Date: 2017/01/05
	 */
	public void SaveSplitDetailInfo() {
		String res = "";
		try {
			res=splitService.SaveSplitDetailInfo(sendData,userCD);
			super.print.print(res);
		}catch(Exception e){
			// データをプリンター
			super.print.print("false");
		}finally {
			super.print.flush();
			super.print.close();
		}
	}
}
