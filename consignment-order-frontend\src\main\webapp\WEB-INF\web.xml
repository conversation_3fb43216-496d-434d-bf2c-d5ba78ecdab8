<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://java.sun.com/xml/ns/javaee" xmlns:web="http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
	version="3.0">
	<display-name>DCOrderConsignment</display-name>
	<filter>
		<filter-name>EncodeFilter</filter-name>
		<filter-class>jp.trial.DCOrderConsignment.common.EncodingFilter</filter-class>
		<init-param>
			<param-name>encode</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>EncodeFilter</filter-name>
		<url-pattern>*.html</url-pattern>
		<url-pattern>*.jsp</url-pattern>
		<url-pattern>*.action</url-pattern>
		<url-pattern>*.js</url-pattern>
	</filter-mapping>

	
<!-- 	STRUTS 2 -->
	<filter>
		<filter-name>struts2</filter-name>
		<filter-class>org.apache.struts2.dispatcher.filter.StrutsPrepareAndExecuteFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>struts2</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<welcome-file-list>
		<welcome-file>index.html</welcome-file>
	</welcome-file-list>
	<!-- sessionタイムアウト，単位分-->
	<session-config>
		<session-timeout>15</session-timeout>
	</session-config>

</web-app>
