package jp.trial.DCOrderConsignment.dao;

import java.util.List;

/**
 * Description: 伝票分け明細
 * <AUTHOR>
 * 階層:　DAO
 * Date: 2017/01/05
 */
public interface MenuMainDao {
	//メニューリスト取得
	public List<Object> getSidebarMenuSelList(String userCD,String employeeCD) throws Exception;
	//推奨案情報取得
	public List<Object> getOrderCaseInfoData(String userCD,String employeeCD) throws Exception;
	//ユーザー名取得
	public List<Object> getLoginInfo(String venderCD,String employeeCD) throws Exception;
	
}
