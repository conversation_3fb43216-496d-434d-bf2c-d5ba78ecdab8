package jp.trial.DCOrderConsignment.service.impl;

import java.util.List;

import jp.trial.DCOrderConsignment.dao.impl.SplitDetailDaoImpl;
import jp.trial.DCOrderConsignment.service.SplitDetailService;

import com.alibaba.fastjson.JSON;

public class SplitDetailServiceImpl implements SplitDetailService{
	
	private SplitDetailDaoImpl splitDetailDao=new SplitDetailDaoImpl();

	/** 検索*/
	public String GetSplitDetailInfo(String orderCaseID) throws Exception {
		String res = "";
		List<Object> objList;
		try{
			objList = splitDetailDao.GetSplitDetailInfo(orderCaseID);
			res = JSON.toJSONString(objList);
		}catch(Exception e){
			throw new Exception(e.getMessage());
		}
		return res;
	}
	
	/** 確定*/
	public String SaveSplitDetailInfo(String sendData,String userCD) throws Exception {
		String res = "";
		try{
			res=splitDetailDao.SaveSplitDetailInfo(sendData, userCD);
		}catch(Exception e){
			throw new Exception(e.getMessage());
		}
		return res;
	}
}
