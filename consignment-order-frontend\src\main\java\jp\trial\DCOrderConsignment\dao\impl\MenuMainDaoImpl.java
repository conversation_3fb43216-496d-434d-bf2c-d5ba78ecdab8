package jp.trial.DCOrderConsignment.dao.impl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import jp.trial.DCOrderConsignment.common.BaseDao;
import jp.trial.DCOrderConsignment.dao.MenuMainDao;
import jp.trial.DCOrderConsignment.model.SidebarMenuModel;
import jp.trial.DCOrderConsignment.model.OrderCaseInfoModel;
import jp.trial.DCOrderConsignment.model.LoginUserInfoModel;

import org.apache.log4j.Logger;

public class MenuMainDaoImpl extends BaseDao implements MenuMainDao{
	//データベース接続
	private Connection conn = null;
	// 実行Statement
	private CallableStatement cstmt = null;

	// LOG
	private static final Logger logger = Logger.getLogger(MenuMainDaoImpl.class);
	/** メニューリスト取得*/
	@Override
	public List<Object> getSidebarMenuSelList(String userCD,String employeeCD) throws Exception {
		// データベースの接続 OPEN
		conn = super.getConnection();
		List<Object> objList = new ArrayList<Object>();
		List<SidebarMenuModel> sidebarMenuList = new ArrayList<SidebarMenuModel>();
		String sql = "{Call PROC_DCOrderAutoSys_GetMenuList_Web(?,?)}";
		try {
			// 実行SQL
			cstmt = conn.prepareCall(sql);
			cstmt.setString(1, userCD);	
			cstmt.setString(2, employeeCD);	
			// ストアドプロシージャ実行
			ResultSet rs = cstmt.executeQuery();
			SidebarMenuModel sidebarMenuModel;
			while(rs.next()){
				sidebarMenuModel = new SidebarMenuModel();
				sidebarMenuModel.setAppNo(rs.getString("APPNo"));
				sidebarMenuModel.setSystemName(rs.getString("SystemName"));
				sidebarMenuModel.setAppName(rs.getString("AppName"));
				sidebarMenuModel.setClassName(rs.getString("className"));
				sidebarMenuList.add(sidebarMenuModel);
			}
			objList.add(sidebarMenuList);
		}catch (Exception e) {
			logger.error(e.getMessage());
			e.printStackTrace();
			throw new Exception("false");
		} finally {
			// データベースの接続　CLOSE
			super.closeAll(conn, cstmt, null);
		}
		return objList;
	}
	
	/**推奨案情報取得*/
	@Override
	public List<Object> getOrderCaseInfoData(String userCD,String employeeCD) throws Exception {
		// DB接続確立
		conn=super.getConnection();
		List<Object> objList = new ArrayList<Object>();
		List<OrderCaseInfoModel> OrderCaseInfoList = new ArrayList<OrderCaseInfoModel>();
		try {
			cstmt = conn.prepareCall("{Call PROC_DCOrderAutoSys_GetSuppOrderCaseInfo_Web (?,?)}");
			cstmt.setString(1, userCD);
			cstmt.setString(2, employeeCD);
			// ストアドプロシージャ実行
			cstmt.execute();
			ResultSet rs = cstmt.getResultSet();
			OrderCaseInfoModel orderCaseInfoModel;
			if(null!=rs){
				while(rs.next()){
					orderCaseInfoModel = new OrderCaseInfoModel();
					orderCaseInfoModel.setOrderCaseInfo(rs.getString("OrderCaseInfo"));
					OrderCaseInfoList.add(orderCaseInfoModel);
				}
			}
			objList.add(OrderCaseInfoList);
		}catch (Exception e) {
			logger.error(e.getMessage());
			e.printStackTrace();
			throw new Exception("false");
		} finally {
			// データベースの接続　CLOSE
			super.closeAll(conn, cstmt, null);
		}
		return objList;
	}
	//ユーザー名取得
	public List<Object> getLoginInfo(String venderCD,String employeeCD) throws Exception{
		// データベースの接続 OPEN
		conn = super.getConnection();
		List<Object> objList = new ArrayList<Object>();
		String sql = "{Call LOI_DBOrder.dbo.PROC_DBMaint_GetLoginUserCheck(?,?)}";
		try {
			// 実行SQL
			cstmt = conn.prepareCall(sql);
			cstmt.setString(1, employeeCD);	
			cstmt.setString(2, venderCD);	
			// ストアドプロシージャ実行
			ResultSet rs = cstmt.executeQuery();
			LoginUserInfoModel loginInfo = new LoginUserInfoModel();
			while(rs.next()){
				loginInfo.setSupplierCD(rs.getString("SupplierCD"));
				loginInfo.setSupplierName(rs.getString("SupplierName"));
				loginInfo.setEmployeeCode(rs.getString("EmployeeCode"));
				loginInfo.setEmployeeName(rs.getString("EmployeeName"));
			}
			objList.add(loginInfo);
		}catch (Exception e) {
			logger.error(e.getMessage());
			e.printStackTrace();
			throw new Exception("false");
		} finally {
			// データベースの接続　CLOSE
			super.closeAll(conn, cstmt, null);
		}
		return objList;
	};
}
