/*
 * @description:1）3種類改修、前者の数値変更したら、後者も合わせて、変わるうん
 * ①ロット数　（　ケース数　→　推奨数　一緒に変わる）
 * ②ケース数　（　推奨数　一緒に変わる　）
 * ③推奨数
 * 2）発注済み、再作成案判断追加
 * @修正者: 10047496 zhangyunfeng
 * @修正日: 2019/04/26
 * */
var windowWidth= window.screen.availWidth;
var windowHeight= window.screen.availHeight;
var dayWeeks = ['日','月','火','水','木','金','土'];
var PosX = 0; 
var PosY = 0;
var split_instance=null;//発注推奨案伝票分け結果一覧画面
var janInfo_instance=null;//商品ロット情報画面
var inOutPlanInfo_instance=null;//センター入出荷予定結果一覧画面
var styleRender = function(instance, td, row, col, prop, value, cellProperties){
	var color;
	var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', row);
	if("ChkFlg"==prop){//checkbox
		if("0"!=value && "1"!=value){
			td.style.textAlign = "center";
			return;
		}
		Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
		td.style.textAlign = "center";
	}
	else if("LotQty"==prop || "CaseQty"==prop ||"OrderPlanQty"==prop ||"AdjustRate"==prop ||"DeliveryPlanDate"==prop){//推奨数 || 納品予定日
		td.style.textAlign = "right";
		if("LotQty"==prop|| "CaseQty"==prop){//推奨数
			Handsontable.renderers.NumericRenderer.apply(this, arguments);
			td.style.backgroundColor = "#FFFF99";
		}else if("AdjustRate"==prop){//推奨数
			Handsontable.renderers.TextRenderer.apply(this, arguments);
			td.style.backgroundColor = "#FFFF99";
			if(!isEmpty(value)){
				td.innerText=value+"%";
			}
		}else if("OrderPlanQty"==prop){
			Handsontable.renderers.NumericRenderer.apply(this, arguments);
			if(RegistryGlobal.showList[realRow] &&RegistryGlobal.showList[realRow].OrderPlanQty >=100000){
				td.title = Message.msg027;
			    td.style.backgroundColor = "red";
			}else if(RegistryGlobal.showList[realRow] && 
			   RegistryGlobal.showList[realRow].OrderPlanQty &&
			   RegistryGlobal.showList[realRow].ProLotQtyCheck &&
			   parseInt(RegistryGlobal.showList[realRow].ProLotQtyCheck)!=0 &&
			   parseFloat(RegistryGlobal.showList[realRow].OrderPlanQty)
					% parseInt(RegistryGlobal.showList[realRow].ProLotQtyCheck) != 0){
				td.title = Message.msg008.replace("$1",parseInt(RegistryGlobal.showList[realRow].ProLotQtyCheck));
				td.style.backgroundColor = "#99FFFF";
			}else{
				td.title = "";
				td.style.backgroundColor = "#FFFF99";
			}
		}else if("DeliveryPlanDate"==prop){//納品予定日
			Handsontable.renderers.TextRenderer.apply(this, arguments);
			if(!value||isDate(convertToCorrectDate(value))){				
				$(arguments[1]).removeClass("htInvalid");
			}
			if(value && !isDate(value)){
				value = "";
			}else if( value && 
				(
					// 10105066 start   
//				new Date(new Date(value).toLocaleDateString())<
//        		   new Date(new Date(RegistryGlobal.OrderCaseCreateDate).toLocaleDateString()) ||
//        		new Date(new Date(value).toLocaleDateString())>=
//        		   new Date(
//        			   new Date(
//        				   Number(new Date(RegistryGlobal.OrderCaseCreateDate).getFullYear()+1)+"/"+
//        				   Number(new Date(RegistryGlobal.OrderCaseCreateDate).getMonth()+1)+"/"+
//        				   new Date(RegistryGlobal.OrderCaseCreateDate).getDate()
//        		       ).toLocaleDateString()
//        		   )
				new Date(new Date(value))<
        		   new Date(new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10))) || //new Date(str)方法，str格式影响其在IE浏览器上的解析
        		new Date(new Date(value))>=
        		   new Date(
        			   new Date(
        				   Number(new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10)).getFullYear()+1)+"/"+
        				   Number(new Date(RegistryGlobal.OrderCaseCreateDate).getMonth()+1)+"/"+
        				   new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10)).getDate()
        		       )
        		   )
        		// 10105066 end
        		)
			){
				color = "#EEEEEE";
			}else if(0==new Date(value).getDay()){
				color = "red";
			}else{				
				for(var i=0; i<RegistryGlobal.confirmList.length; i++){
					if(RegistryGlobal.showList[realRow] && RegistryGlobal.confirmList[i] &&
							RegistryGlobal.showList[realRow].MixCD==RegistryGlobal.confirmList[i].MixCD){
						if( (	value &&
								RegistryGlobal.confirmList[i].NotDeliveryWeekDay &&
								//10105066 start
								//RegistryGlobal.confirmList[i].NotDeliveryWeekDay.includes( dayWeeks[new Date(value).getDay()] )//
								RegistryGlobal.confirmList[i].NotDeliveryWeekDay.indexOf(dayWeeks[new Date(value).getDay()]) > -1//includes()方法对IE浏览器不兼容，修改为indexOf()方法，该方法兼容IE9及其以上版本
								//10105066 end 
						) || 
						(
								new Date(value)>=new Date(RegistryGlobal.confirmList[i].NotDeliveryBeginDate) &&
								new Date(value)<=new Date(RegistryGlobal.confirmList[i].NotDeliveryEndDate)
						)
						){
							color = "red";
							break;
						}
					}
				}
			}
			
			if(!color){
				color = '#FFFF99';
			}
			td.style.backgroundColor = color;
		}
		
		if(RegistryGlobal.showList[realRow] && "1"==RegistryGlobal.showList[realRow].SplitSetFlg){//伝票
			cellProperties.readOnly = true;
		}
	}else if("OrderPlanDate"==prop||"Times"==prop){//col==13 発注予定日
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		td.style.textAlign = "right";
	}else if("OrderStatus"==prop){//進捗
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		td.style.textAlign = "center";
	//}else if(col>=8 && col<=21 && col!=14 && col!=19&& col!=21){
	}else if("WareHouseQty"==prop||"week1"==prop||"week2"==prop||"week3"==prop||"week4"==prop ||"Tweek"==prop
		||"DeliveryPlanQty"==prop ||"SalesDay"==prop||"CenterSalesDay"==prop||"OrderSalesDay"==prop
		||"OrderPlanQtyAfter"==prop||"CaseQty"==prop){//数字列
		Handsontable.renderers.NumericRenderer.apply(this, arguments);
	}else{
		Handsontable.renderers.TextRenderer.apply(this, arguments);
	}

	if(RegistryGlobal.showDataLength>0 && RegistryGlobal.showList[realRow]){
		//if(RegistryGlobal.showList[realRow].OrderStatus&&""!=RegistryGlobal.showList[realRow].OrderStatus ){//OrderStatus
		//if(!isEmpty(RegistryGlobal.showList[realRow].OrderStatus)&&"0"==RegistryGlobal.showList[realRow].IsVerNODiff ){//OrderStatus
		if("1"==RegistryGlobal.showList[realRow].CurrentOrderFlg ){//この案で発注済み存在⇒1、選択できない
			cellProperties.readOnly = true;
			td.style.backgroundColor = "#EEEEEE";
		}else if("4"==RegistryGlobal.showList[realRow].ShippingTypeCD ){
			cellProperties.readOnly = true;
			td.style.backgroundColor = "#EEEEEE";
		}
		else if(!RegistryGlobal.showList[realRow].OrderCaseID){//発注案ID
			cellProperties.readOnly = true;
			td.style.backgroundColor = "#F0FFF0";
		}
	}
}

/** DataGrid Creat*/
var TemplateGrid = {
	tbhot:null,
	colWidths: [60,40,32,80,80,80,110,120,60,70,60,60,60,60,60,80,70,70,80,60,70,70,70,70,60,80,130,100,//60,60,
	            0.01,0.01,0.01,0.01,0.01,0.01,0.01,0.01,0.01,0.01,0.01,0.01,0.01,0.01,0.01,0.01,0.01],
	columns: [
	    {data: 'ChkFlg',    	type: 'checkbox',    checkedTemplate: '1', uncheckedTemplate: '0'},//全選
	    {data: 'OrderStatus',	type: 'text',     	 readOnly:true},//進捗
		{data: 'Times',			type: 'text',        readOnly:true},
	    {data: 'StoreHouse',	type: 'text',     	 readOnly:true},//センターCD
	    {data: 'MixCD',			type: 'text',        readOnly:true},//混載区分
	    {data: 'DIV',			type: 'text',        readOnly:true},//DIV
	    {data: 'JAN',			type: 'text',        readOnly:true},//JAN
	    {data: 'ProductName',	type: 'text',     	 readOnly:true},//商品名
	    {data: 'SpecName',		type: 'text',        readOnly:true},//規格
	    {data: 'WareHouseQty',	type: 'numeric',     format: '0,0',readOnly:true},//センター在庫
	    {data: 'week1',			type: 'numeric',     format: '0,0',readOnly:true},//-4週売数
	    {data: 'week2',			type: 'numeric',     format: '0,0',readOnly:true},//-3週売数
	    {data: 'week3',			type: 'numeric',     format: '0,0',readOnly:true},//-2週売数
	    {data: 'week4',			type: 'numeric',     format: '0,0',readOnly:true},//-1週売数
	    {data: 'Tweek',			type: 'numeric',     format: '0,0',readOnly:true},//今週
	    {data: 'OrderPlanDate',	type: 'text',     	 readOnly:true},//発注予定日
	    {data: 'DeliveryPlanQty',	type: 'numeric',     format: '0,0',readOnly:true},//納品予定数
	    {data: 'SalesDay',		type: 'numeric',     format: '0,0',readOnly:true},//現在庫消化
	    {data: 'CenterSalesDay',type: 'numeric',     format: '0,0',readOnly:true},//在庫＋納品予定分消化日数
	    {data: 'OrderSalesDay',	type: 'numeric',     format: '0,0',readOnly:true},//発注後消化日数
		{data: 'OrderPlanQtyAfter',	type: 'numeric', format: '0,0',readOnly:true},//推奨数
	    {data: 'LotQty',	    type: 'text',     	 format: '0,0'},//ロット数
	    {data: 'CaseQty',		type: 'numeric',     format: '0,0'},//ケース数
		{data: 'AdjustRate',	type: 'text'},//伸び率
	    {data: 'OrderPlanQty',	type: 'numeric',     	 format: '0,0'},//発注数
	    {data: 'DeliveryPlanDate',
	    	type: 'date',
            dateFormat: 'YYYY/MM/DD',
	        allowEmpty: true,
	        datePickerConfig: {
	          firstDay: 1,
	          showWeekNumber: true,
	          numberOfMonths: 3,
	          disableDayFn: function(date) {
	        	  // 10105066 start
//	            return new Date(date.toLocaleDateString())<
//	            		   new Date(new Date(RegistryGlobal.OrderCaseCreateDate).toLocaleDateString()) ||
//	            	   new Date(date.toLocaleDateString())>=
//	            		   new Date(
//	            			   new Date(
//	            				   Number(new Date(RegistryGlobal.OrderCaseCreateDate).getFullYear()+1)+"/"+
//	            				   Number(new Date(RegistryGlobal.OrderCaseCreateDate).getMonth()+1)+"/"+
//	            				   new Date(RegistryGlobal.OrderCaseCreateDate).getDate()
//	            		       ).toLocaleDateString()
//	            		   );
        	  return new Date(date)<
		       		   new Date(new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10))) ||  //new Date(str)方法，str格式影响其在IE浏览器上的解析
		       	   new Date(date)>=
		       		   new Date(
		       			   new Date(
		       				   Number(new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10)).getFullYear()+1)+"/"+
		       				   Number(new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10)).getMonth()+1)+"/"+
		       				   new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10)).getDate()
		       		       )
		       		   );
		       	   // 10105066 end
	          },
	          i18n: {
			    previousMonth : 'Previous Month',
			    nextMonth     : 'Next Month',
			    months        : ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
			    weekdays      : ['日曜日','月曜日','火曜日','水曜日','木曜日','金曜日','土曜日'],
			    weekdaysShort : ['日','月','火','水','木','金','土']
			  }
	        }
	    },//納品予定日
	    {data: 'FlgConsultJAN',		type: 'text',        readOnly:true},//区分/JAN
	    {data: 'comment',			type: 'text',        readOnly:true},//備考
//	    {data: 'flg',				type: 'text',     	 readOnly:true},//今週のみ参照フラグ
//	    {data: 'flgday',			type: 'text',     	 readOnly:true},//フラグ期間
	    {data: 'OrderCaseID',		type: 'text',        readOnly:true},
	    //{data: 'Times',				type: 'text',        readOnly:true},
	    {data: 'OrderCaseDetailID',	type: 'text',        readOnly:true},
	    {data: 'VerNO',				type: 'text',        readOnly:true},
	    {data: 'StoreHouseCD',		type: 'text',        readOnly:true},
	    {data: 'InnerCaseQty',		type: 'text',        readOnly:true},//C/S入数
	    {data: 'ApprovalFlag',		type: 'text',        readOnly:true},
	    {data: 'SplitSetFlg',		type: 'text',        readOnly:true},
	    {data: 'ProLotQtyCheck',	type: 'text',        readOnly:true},
	    {data: 'NotDeliveryCheck',	type: 'text',        readOnly:true},
	    {data: 'AvgDigestQty',		type: 'text',        readOnly:true},
	    {data: 'OrderUnit',			type: 'text',        readOnly:true},
	    {data: 'ShippingTypeCD',	type: 'text',        readOnly:true},
	    {data: 'OrderUnitQty',		type: 'text',        readOnly:true},
	    {data: 'LotUnit',			type: 'text',        readOnly:true},
	    {data: 'Terms',	            type: 'text',        readOnly:true},
		{data: 'IsVerNODiff',	    type: 'text',        readOnly:true},//発注済み、再作成案判断追加
		{data: 'CurrentOrderFlg',	    type: 'text',        readOnly:true}//この案で発注済み存在⇒1、選択できない
	],
	isChecked: function(){
		if(!this.tbhot){
			return false;
		}
		if("afterChg"==chk){
			RegistryGlobal.tableSourceData = TemplateGrid.tbhot.getSourceData();
			for (var i=0; i<RegistryGlobal.tableSourceData.length; i++) {
				//if ((!RegistryGlobal.tableSourceData[i].OrderStatus||""==RegistryGlobal.tableSourceData[i].OrderStatus) && RegistryGlobal.tableSourceData[i].OrderCaseID){
				if ((isEmpty(RegistryGlobal.tableSourceData[i].OrderStatus)||RegistryGlobal.tableSourceData[i].IsVerNODiff=="1") && RegistryGlobal.tableSourceData[i].OrderCaseID&& RegistryGlobal.tableSourceData[i].CurrentOrderFlg!="1"){//発注済み、再作成案判断追加
					if(RegistryGlobal.tableSourceData[i].ChkFlg!="1"){						
						return false;
					}
				}
			}
			return true;
		}else{
			return chk;
		}
	},
	//create grid
	CreatHotGrid: function(hotgrid,arrData){
		//まず対象内容クリア
		if(this.tbhot){
			this.tbhot.destroy();
		}
		this.tbhot = new Handsontable(document.getElementById(hotgrid),{
			height:window.parent.$(".page-content").height()-150,
			rowHeaders: false,
			colHeaders: function(col){
							switch(col){
							case 0:
								var txt = '<label class="checkbox-inline"><input id="chkAll" onclick="chkAll();" type="checkbox"';
									txt += TemplateGrid.isChecked() ? 'checked="checked"' : '';
									txt += '/>全選</label>';
								return txt;
							case 1: return '進捗';
							case 2: return '回数';
							case 3: return 'センターCD';
							case 4: return '混載区分';
							case 5: return 'DIV';
							case 6: return 'JAN';
							case 7: return '商品名';
							case 8: return '規格';
							case 9: return 'センター在庫';
							case 10: return '4週前週売数';
							case 11: return '3週前週売数';
							case 12: return '2週前週売数';
							case 13: return '1週前週売数';
							case 14: return '今週';
							case 15: return '発注予定日';
							case 16: return '納品予定数';
							case 17: return '現在庫消化';
							case 18: return '在庫＋納品予<br>定分消化日数';
							case 19: return '発注後<br>消化日数';
							case 20: return '推奨数';
							case 21: return 'ロット数';
							case 22: return 'ケース数';
							case 23: return '伸び率';
							case 24: return '発注数';
							case 25: return '納品予定日';
							case 26: return '区分/JAN';
							case 27: return '備考';
//							case 25: return '今週のみ<br>参照フラグ';
//							case 26: return 'フラグ期間';
							case 28: return 'OrderCaseID';
							//case 26: return 'Times';
							case 29: return 'OrderCaseDetailID';
							case 30: return 'VerNO';
							case 31: return 'StoreHouseCD';
							case 32: return 'InnerCaseQty';
							case 33: return 'ApprovalFlag';
							case 34: return 'SplitSetFlg';
							case 35: return 'ProLotQtyCheck';
							case 36: return 'NotDeliveryCheck';
							case 37: return 'AvgDigestQty';
							case 38: return 'OrderUnit';
							case 39: return 'ShippingTypeCD';
							case 40: return 'OrderUnitQty';
							case 41: return 'LotUnit';
							case 42: return 'Terms';
							case 43: return 'IsVerNODiff';//発注済み、再作成案判断追加
							case 44: return 'CurrentOrderFlg';//この案で発注済み存在⇒1、選択できない
							}
						},
			colWidths: this.colWidths,
			columns: this.columns,
			overflow: scroll,
		    fillHandle:false,
		    wordWrap:false,
		    search: true ,
		    startRows: 27,
		    columnSorting: true,
		    sortIndicator: true,
		    manualColumnResize: true,
		    fixedColumnsLeft: 7,
			currentRowClassName: 'currentRow',
			cells: function (row, col, prop) {
				//
				if("LotQty"==prop||"CaseQty"==prop||"OrderPlanQty"==prop){
					this.maxLength = 5;
				}
				if("AdjustRate"==prop){
					this.maxLength = 4;
				}
//				if("LotQty"==prop){
//					this.maxLength = 5;
//				}
				var cellProperties = {};
				cellProperties.renderer = styleRender;
				return cellProperties;
	    	},
	    	beforeChange : function(changes, source){
				if (changes && changes[0].length) {
					for(var i=0; i<changes.length; i++) {
						if(changes[i][1]=="DeliveryPlanDate"){
							var dt = convertToCorrectDate(changes[i][3]);
							if(dt && dt!=changes[i][3]){
								TemplateGrid.tbhot.setDataAtRowProp(changes[i][0], changes[i][1], dt);
							}
						}
					}
				}
	    	},
	    	afterChange: function(changes, source) {
 				afterChg(changes, source);
 			},
 			beforeOnCellMouseDown:function(event, cellCoords,td){
 				if (event.pageX || event.pageY) {
					PosX = event.pageX;
					PosY = event.pageY;
				} 
				else { 
					PosX = event.clientX + document.body.scrollLeft - document.body.clientLeft; 
					PosY = event.clientY + document.body.scrollTop - document.body.clientTop; 
				};
 			},
 			afterSelectionByProp: function(r, p, r2, p2) {
 				afterSel(r, p, r2, p2);
 			}
		});
		if(RegistryGlobal.init){			
			this.tbhot.loadData(arrData);
		}
	    this.tbhot.render();
	}
}
var chk;
var chkAll = function(){
	if(chk==true){
		chk = false;
	}else{
		chk = true;
	}
	if(TemplateGrid.tbhot.getSourceData().length>0){		
		$.each(RegistryGlobal.showList, function(i, data){ 
			//if((data.OrderStatus&&""!=data.OrderStatus) || !data.OrderCaseID||"4"==data.ShippingTypeCD){
			//if((!isEmpty(data.OrderStatus)&&"0"==data.IsVerNODiff) || !data.OrderCaseID||"4"==data.ShippingTypeCD){
			if(data.CurrentOrderFlg=="1" || !data.OrderCaseID||"4"==data.ShippingTypeCD){
				
			}else{
				data.ChkFlg = chk?"1":"0";
			}
		});
	}
	TemplateGrid.tbhot.render();
}

var afterChg = function(changes, source){
	if (changes && changes[0].length) {
		for(var i=0; i<changes.length; i++){
			rowidx = changes[i][0];
			colName = changes[i][1];
			preValue = changes[i][2];
			newValue = changes[i][3];

			var realRow;
			//RegistryGlobal.tableData = TemplateGrid.tbhot.getData();
			RegistryGlobal.tableSourceData = TemplateGrid.tbhot.getSourceData();
			if((typeof(preValue)=="undefined"||preValue==""|| preValue==null)&& (typeof(newValue)=="undefined"||newValue==""|| newValue==null)) {
            }else if("ChkFlg"==colName){
				chk = "afterChg";
				realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', rowidx);
				$.each(RegistryGlobal.tableSourceData, function(j, data){
					//if(!RegistryGlobal.tableData[j][1] && RegistryGlobal.tableData[j][25] &&
					//if((!RegistryGlobal.tableSourceData[j].OrderStatus||RegistryGlobal.tableSourceData[j].IsVerNODiff=="1") && RegistryGlobal.tableSourceData[j].ShippingTypeCD!="4"&&
					if(RegistryGlobal.tableSourceData[j].CurrentOrderFlg!="1" && RegistryGlobal.tableSourceData[j].ShippingTypeCD!="4"&&
						RegistryGlobal.tableSourceData[j].OrderCaseID &&RegistryGlobal.tableSourceData[j].OrderCaseID==RegistryGlobal.tableSourceData[realRow].OrderCaseID&&
						RegistryGlobal.tableSourceData[j].Times&&RegistryGlobal.tableSourceData[j].Times==RegistryGlobal.tableSourceData[realRow].Times
					){
						//realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', j);
						if("0"==newValue||"1"==newValue){
							//RegistryGlobal.tableSourceData[realRow].ChkFlg=newValue;
							RegistryGlobal.tableSourceData[j].ChkFlg=newValue;
						}
					}
				});
			}else if("LotQty"==colName && preValue!=newValue && RegistryGlobal.tableSourceData[rowidx]){//ロット数
				realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', rowidx);
				if(RegistryGlobal.tableSourceData[realRow].ProLotQtyCheck){
					RegistryGlobal.tableSourceData[realRow].OrderPlanQty = //推奖数
						Math.round(
							(newValue?parseFloat(Number(newValue)):0)* parseInt(RegistryGlobal.tableSourceData[realRow].ProLotQtyCheck)
						);//発注ロット単位=C/S:入力ロット値*発注ロット入数*内箱入数    発注ロット単位=個:入力ロット値*発注ロット入数
				}
				else{
					RegistryGlobal.tableSourceData[realRow].OrderPlanQty = "0"//推奖数
				}
				//伸び率 クリア
				if( "1"==RegistryGlobal.showList[realRow].SplitSetFlg){
					var OrderPlanQtyAfter=RegistryGlobal.tableSourceData[realRow].OrderPlanQtyAfter?Number(RegistryGlobal.tableSourceData[realRow].OrderPlanQtyAfter):0;
					var OrderPlanQty=(RegistryGlobal.tableSourceData[realRow].OrderPlanQty);
					if(OrderPlanQtyAfter!=0&&OrderPlanQty!=0){
						RegistryGlobal.tableSourceData[realRow].AdjustRate = Math.round(parseFloat(OrderPlanQty)/OrderPlanQtyAfter*100);
					}
				}else{
					RegistryGlobal.tableSourceData[realRow].AdjustRate = null;
				}
				if(!RegistryGlobal.tableSourceData[realRow].AvgDigestQty || "0"==RegistryGlobal.tableSourceData[realRow].AvgDigestQty){
					RegistryGlobal.tableSourceData[realRow].OrderSalesDay = 0; //発注後消化日数
				}else{
					/*RegistryGlobal.tableSourceData[realRow].OrderSalesDay = //発注後消化日数
						Math.round(
								(parseInt(RegistryGlobal.tableSourceData[realRow].WareHouseQty) +
										parseInt(RegistryGlobal.tableSourceData[realRow].DeliveryPlanQty) + (RegistryGlobal.tableSourceData[realRow].OrderPlanQty?parseFloat(Number(RegistryGlobal.tableSourceData[realRow].OrderPlanQty)):0)) 
										/ parseInt(RegistryGlobal.tableSourceData[realRow].AvgDigestQty)
						);//発注後消化日数 = (センター在庫+納品予定数+推奖数)/AvgDigestQty
					if(isNaN(RegistryGlobal.tableSourceData[realRow].OrderSalesDay)){
						RegistryGlobal.tableSourceData[realRow].OrderSalesDay = null;
					}*/
					setOrderSalesDay(realRow);//発注後消化日数,2回発注の消化日数計算追加
				}
				if(!RegistryGlobal.tableSourceData[realRow].InnerCaseQty || "0"==RegistryGlobal.tableSourceData[realRow].InnerCaseQty){
					RegistryGlobal.tableSourceData[realRow].CaseQty = 0; //ケース数
				}else{					
					RegistryGlobal.tableSourceData[realRow].CaseQty = //ケース数
						//ケース数 = 推奖数/InnerCaseQty
						RegistryGlobal.tableSourceData[realRow].OrderPlanQty?Math.round(parseFloat(Number(RegistryGlobal.tableSourceData[realRow].OrderPlanQty)) / parseInt(RegistryGlobal.tableSourceData[realRow].InnerCaseQty)):null;
						if(isNaN(RegistryGlobal.tableSourceData[realRow].CaseQty)){
							RegistryGlobal.tableSourceData[realRow].CaseQty = null;
						}
				}
			}else if("CaseQty"==colName && preValue!=newValue && RegistryGlobal.tableSourceData[rowidx]){//ケース数
				realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', rowidx);
				//ロット数クリア
				RegistryGlobal.tableSourceData[realRow].LotQty = null;
				//伸び率 クリア
				if( "1"!=RegistryGlobal.showList[realRow].SplitSetFlg){
					RegistryGlobal.tableSourceData[realRow].AdjustRate = null;
				}
				if(RegistryGlobal.tableSourceData[realRow].InnerCaseQty){
					//推奖数=ケース数*InnerCaseQty
					RegistryGlobal.tableSourceData[realRow].OrderPlanQty =
						(newValue?parseFloat(Number(newValue)):0)* parseInt(RegistryGlobal.tableSourceData[realRow].InnerCaseQty);
				}
				else{
					RegistryGlobal.tableSourceData[realRow].OrderPlanQty = "0"//推奖数
				}
				if(!RegistryGlobal.tableSourceData[realRow].AvgDigestQty || "0"==RegistryGlobal.tableSourceData[realRow].AvgDigestQty){
					RegistryGlobal.tableSourceData[realRow].OrderSalesDay = 0; //発注後消化日数
				}else{
					/*RegistryGlobal.tableSourceData[realRow].OrderSalesDay = //発注後消化日数
						Math.round(
								(parseInt(RegistryGlobal.tableSourceData[realRow].WareHouseQty) +
										parseInt(RegistryGlobal.tableSourceData[realRow].DeliveryPlanQty) + (RegistryGlobal.tableSourceData[realRow].OrderPlanQty?parseFloat(Number(RegistryGlobal.tableSourceData[realRow].OrderPlanQty)):0)) 
										/ parseInt(RegistryGlobal.tableSourceData[realRow].AvgDigestQty)
						);//発注後消化日数 = (センター在庫+納品予定数+推奖数)/AvgDigestQty
					if(isNaN(RegistryGlobal.tableSourceData[realRow].OrderSalesDay)){
						RegistryGlobal.tableSourceData[realRow].OrderSalesDay = null;
					}*/
					setOrderSalesDay(realRow);//発注後消化日数,2回発注の消化日数計算追加
				}
			}
			else if("AdjustRate"==colName && preValue!=newValue && RegistryGlobal.tableSourceData[rowidx]) {//伸び率
				realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', rowidx);
				if(RegistryGlobal.tableSourceData[realRow].OrderPlanQty&&"1"!=RegistryGlobal.showList[realRow].SplitSetFlg){
					//ロット数クリア
					RegistryGlobal.tableSourceData[realRow].LotQty = null;
					//ケース数 クリア
					RegistryGlobal.tableSourceData[realRow].CaseQty = null;
					if(newValue){
						var OrderPlanQtyAfter=RegistryGlobal.tableSourceData[realRow].OrderPlanQtyAfter?Number(RegistryGlobal.tableSourceData[realRow].OrderPlanQtyAfter):0;
						//推奖数=推奖数*伸び率
						RegistryGlobal.tableSourceData[realRow].OrderPlanQty =
							Math.round(OrderPlanQtyAfter*(newValue?toDecimal(Number(newValue)/100+"",2):0));
					}else{
						//推奖数=推奖数/旧伸び率
						RegistryGlobal.tableSourceData[realRow].OrderPlanQty =RegistryGlobal.tableSourceData[realRow].OrderPlanQtyAfter;
					}
					if(!RegistryGlobal.tableSourceData[realRow].AvgDigestQty || "0"==RegistryGlobal.tableSourceData[realRow].AvgDigestQty){
						RegistryGlobal.tableSourceData[realRow].OrderSalesDay = 0; //発注後消化日数
					}else{
						/*RegistryGlobal.tableSourceData[realRow].OrderSalesDay = //発注後消化日数
							Math.round(
								(parseInt(RegistryGlobal.tableSourceData[realRow].WareHouseQty) +
									parseInt(RegistryGlobal.tableSourceData[realRow].DeliveryPlanQty) + (RegistryGlobal.tableSourceData[realRow].OrderPlanQty?parseFloat(Number(RegistryGlobal.tableSourceData[realRow].OrderPlanQty)):0))
								/ parseInt(RegistryGlobal.tableSourceData[realRow].AvgDigestQty)
							);//発注後消化日数 = (センター在庫+納品予定数+推奖数)/AvgDigestQty
						if(isNaN(RegistryGlobal.tableSourceData[realRow].OrderSalesDay)){
							RegistryGlobal.tableSourceData[realRow].OrderSalesDay = null;
						}*/
						setOrderSalesDay(realRow);//発注後消化日数,2回発注の消化日数計算追加
					}
				}
			}
			else if("OrderPlanQty"==colName && preValue!=newValue && RegistryGlobal.tableSourceData[rowidx]){//推奖数
				realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', rowidx);
				//ロット数クリア
				RegistryGlobal.tableSourceData[realRow].LotQty = null;
				//ケース数 クリア
				RegistryGlobal.tableSourceData[realRow].CaseQty = null;
				//伸び率 クリア
				if( "1"!=RegistryGlobal.showList[realRow].SplitSetFlg){
					RegistryGlobal.tableSourceData[realRow].AdjustRate = null;
				}
				if(!RegistryGlobal.tableSourceData[realRow].AvgDigestQty || "0"==RegistryGlobal.tableSourceData[realRow].AvgDigestQty){
					RegistryGlobal.tableSourceData[realRow].OrderSalesDay = 0; //発注後消化日数
				}else{
					/*RegistryGlobal.tableSourceData[realRow].OrderSalesDay = //発注後消化日数
						Math.round(
								(parseInt(RegistryGlobal.tableSourceData[realRow].WareHouseQty) +
										parseInt(RegistryGlobal.tableSourceData[realRow].DeliveryPlanQty) + (newValue?parseFloat(Number(newValue)):0)) 
										/ parseInt(RegistryGlobal.tableSourceData[realRow].AvgDigestQty)
						);//発注後消化日数 = (センター在庫+納品予定数+推奖数)/AvgDigestQty
					if(isNaN(RegistryGlobal.tableSourceData[realRow].OrderSalesDay)){
						RegistryGlobal.tableSourceData[realRow].OrderSalesDay = null;
					}*/
					setOrderSalesDay(realRow);//発注後消化日数,2回発注の消化日数計算追加
				}
				
			}
			TemplateGrid.tbhot.render();
		}
	}
}
//発注後消化日数計算
var setOrderSalesDay=function(realRow){
	var firstOrderPlanQty=0;
	var secondOrderPlanQty=0;
	if(RegistryGlobal.tableSourceData[realRow].Times=="1"){
		RegistryGlobal.tableSourceData[realRow].OrderSalesDay=//発注後消化日数
			Math.round(
				(parseInt(RegistryGlobal.tableSourceData[realRow].WareHouseQty) +
					parseInt(RegistryGlobal.tableSourceData[realRow].DeliveryPlanQty) + (RegistryGlobal.tableSourceData[realRow].OrderPlanQty?parseFloat(Number(RegistryGlobal.tableSourceData[realRow].OrderPlanQty)):0))
				/ parseInt(RegistryGlobal.tableSourceData[realRow].AvgDigestQty)
			);//発注後消化日数 = (センター在庫+納品予定数+推奖数)/AvgDigestQty
		if(isNaN(RegistryGlobal.tableSourceData[realRow].OrderSalesDay)){
			RegistryGlobal.tableSourceData[realRow].OrderSalesDay = null;
		}
		firstOrderPlanQty=RegistryGlobal.tableSourceData[realRow].OrderPlanQty?parseFloat(Number(RegistryGlobal.tableSourceData[realRow].OrderPlanQty)):0;
		//2回発注の消化日数計算
		$.each(RegistryGlobal.tableSourceData, function(j){
			if(RegistryGlobal.tableSourceData[j].OrderCaseID==RegistryGlobal.tableSourceData[realRow].OrderCaseID
				&&RegistryGlobal.tableSourceData[j].JAN==RegistryGlobal.tableSourceData[realRow].JAN
				&&RegistryGlobal.tableSourceData[j].StoreHouseCD==RegistryGlobal.tableSourceData[realRow].StoreHouseCD
				&&RegistryGlobal.tableSourceData[j].Times == "2"
			){
				RegistryGlobal.tableSourceData[j].OrderSalesDay = //発注後消化日数
					Math.round(
						(parseInt(RegistryGlobal.tableSourceData[j].WareHouseQty) +
							parseInt(RegistryGlobal.tableSourceData[j].DeliveryPlanQty) + (RegistryGlobal.tableSourceData[j].OrderPlanQty?parseFloat(Number(RegistryGlobal.tableSourceData[j].OrderPlanQty)):0)+firstOrderPlanQty)
						/ parseInt(RegistryGlobal.tableSourceData[j].AvgDigestQty)
					);//発注後消化日数 = (センター在庫+納品予定数+推奖数)/AvgDigestQty
				if(isNaN(RegistryGlobal.tableSourceData[j].OrderSalesDay)){
					RegistryGlobal.tableSourceData[j].OrderSalesDay = null;
				}
				return false;
			}
		});
	}else{
		var firstOrder=RegistryGlobal.tableSourceData.filter(function(item) {
			return item.OrderCaseID==RegistryGlobal.tableSourceData[realRow].OrderCaseID
				&&item.JAN==RegistryGlobal.tableSourceData[realRow].JAN
				&&item.StoreHouseCD==RegistryGlobal.tableSourceData[realRow].StoreHouseCD
				&&item.Times == "1";
		});
		if(firstOrder.length > 0){
			firstOrderPlanQty=firstOrder[0].OrderPlanQty?parseInt(firstOrder[0].OrderPlanQty):0;
		}
		secondOrderPlanQty=RegistryGlobal.tableSourceData[realRow].OrderPlanQty?parseFloat(Number(RegistryGlobal.tableSourceData[realRow].OrderPlanQty)):0;
		RegistryGlobal.tableSourceData[realRow].OrderSalesDay = //発注後消化日数
			Math.round(
				(parseInt(RegistryGlobal.tableSourceData[realRow].WareHouseQty) +
					parseInt(RegistryGlobal.tableSourceData[realRow].DeliveryPlanQty) + firstOrderPlanQty+secondOrderPlanQty)
				/ parseInt(RegistryGlobal.tableSourceData[realRow].AvgDigestQty)
			);//発注後消化日数 = (センター在庫+納品予定数+推奖数)/AvgDigestQty
		if(isNaN(RegistryGlobal.tableSourceData[realRow].OrderSalesDay)){
			RegistryGlobal.tableSourceData[realRow].OrderSalesDay = null;
		}
	}
}
var toDecimal = function(val, decLen) {
	if (val.indexOf(".") != -1 && val.indexOf(".") > 6) {
		return "";
	}
	if (val.indexOf(".") == -1 && val.length > 6) {
		return "";
	}
	var f = parseFloat(val);
	if (isNaN(f)) {
		return "";
	}
	var s = val.toString();
	var rs = s.indexOf('.');
	if (rs < 0) {
		rs = s.length;
		s += '.';
	}
	while (s.length <= rs + decLen) {
		s += '0';
	}
	s = s.substring(0, rs + decLen + 1);
	s = s.substring(0, 11);

	return s;
}

var afterSel = function(r, p, r2, p2){
	var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', r);
	subPageDestroy();//サブ画面⇒廃棄
	if("OrderPlanQty"==p && r==r2 && p==p2 && RegistryGlobal.showList[realRow] && "1"==RegistryGlobal.showList[realRow].SplitSetFlg){//推奨数
		split_instance = new splitDetail(jQuery);
		split_instance.init({
			row: r,
			usercd: RegistryGlobal.userCD,
		    orderCaseID: RegistryGlobal.showList[realRow].OrderCaseID,
		    strJAN: RegistryGlobal.showList[realRow].JAN,
		    OrderCaseCreateDate: RegistryGlobal.OrderCaseCreateDate,
		    OrderStatus: RegistryGlobal.showList[realRow].OrderStatus,
			IsVerNODiff: RegistryGlobal.showList[realRow].IsVerNODiff//発注済み、再作成案判断追加
		});
		split_instance.show();
	}
	if("JAN"==p && r==r2 && p==p2 && RegistryGlobal.showList[realRow]){//JAN
		janInfo_instance = new janInfo(jQuery);
		janInfo_instance.init({
			row: r,
			PosX:PosX, 
			PosY:PosY, 
		    //toppos: mouse.y + divoffset,//計算マウスの位値
			OrderUnitQty:RegistryGlobal.showList[realRow].OrderUnitQty,
			OrderUnit:RegistryGlobal.showList[realRow].OrderUnit,
			LotUnit:RegistryGlobal.showList[realRow].LotUnit,
			Terms:RegistryGlobal.showList[realRow].Terms
		});
		janInfo_instance.show();
	}
	//センター入出荷予定画面
	if("DeliveryPlanQty"==p && r==r2 && p==p2 && RegistryGlobal.showList[realRow]&&RegistryGlobal.showList[realRow].DeliveryPlanQty!=0){//JAN
		inOutPlanInfo_instance = new inOutPlanInfo(jQuery);
		inOutPlanInfo_instance.init({
			row: r,
			PosX:PosX,
			PosY:PosY,
			JAN:RegistryGlobal.showList[realRow].JAN,
			StoreHouseCD:RegistryGlobal.showList[realRow].StoreHouse.split(' ')[0]
		});
		inOutPlanInfo_instance.show();
	}
}
//add zhangyunfeng  表示したサブ画面⇒廃棄　20190611
var subPageDestroy=function(){
	if(split_instance){
		split_instance.hide();
	}
	if(janInfo_instance){
		janInfo_instance.hide();
	}
	if(inOutPlanInfo_instance){
		inOutPlanInfo_instance.hide();
	}
}