var TemplateGrid;
/** DataGrid Creat*/
var getTemplateMainGrid = function(arrStandard, showlist) {
	var sArr = [];
	for (var i = 0; i < arrStandard.length; i++) {
		var obj = {};
		obj.id = arrStandard[i].standard;
		obj.label = arrStandard[i].standard;
		sArr.push(obj);
	}
	TemplateGrid = {
		tbhot:null,
		colWidths:[50,100,180,160,150,110,90,110],
		columns: [
            {data: 'chkFlg',    		type: 'checkbox',    checkedTemplate: '1', uncheckedTemplate: '0'},//全
            {data: 'mixCD',	    		type: 'text',     	 readOnly:true},//混載区分
            {data: 'center',	    	type: 'text',     	 readOnly:true},//センター
            {data: 'supplier',			type: 'text',     	 readOnly:true},//ベンダー
            {data: 'division',			type: 'text',     	 readOnly:true},//DIV
	        {data: 'slipSplitQty',		type: 'text'     },//伝票分け単位数
	        {data: 'dayMaxOrderQty',	type: 'text'     },//1日最大数
	        {
            	data: 'sellingReferenceStandard',	    	
                editor: "chosen",
                chosenOptions: {
                    data: sArr
                }
            }//売数参照基準
		],
		isChecked: function(){
			if(!this.tbhot){
				return false;
			}
			if("afterChg"==chk){
				RegistryGlobal.tableSourceData = TemplateGrid.tbhot.getSourceData();
				for (var i=0; i<RegistryGlobal.tableSourceData.length; i++) {
					if(RegistryGlobal.tableSourceData[i].chkFlg!="1"){						
						return false;
					}
				}
				return true;
			}else{
				return chk;
			}
		},
		//create grid
		CreatHotGrid: function(hotgrid,arrData){
			//まず対象内容クリア
			if(this.tbhot){
				this.tbhot.destroy();
				chk=false;
			}
			this.tbhot = new Handsontable(document.getElementById(hotgrid),{
				height:$(".page-content").height()-150,
				rowHeaders: false,
				colHeaders: function(col){
					switch(col){
						case 0:
							var txt = '<label class="checkbox-inline"><input id="chkAll" onclick="chkAll();" type="checkbox"';
							txt += TemplateGrid.isChecked() ? 'checked="checked"' : '';
							txt += '/>全</label>';
							return txt;
						case 1: return '混載区分';	
						case 2: return 'センター';
						case 3: return 'ベンダー';
						case 4: return 'DIV';
						case 5: return '伝票分け単位数';
						case 6: return '1日最大数';
						case 7: return '売数参照基準';
					}
				},
				colWidths: this.colWidths,
				columns: this.columns,
				overflow: scroll,
			    fillHandle:false,
			    wordWrap:false,
			    search: true,
			    columnSorting: true,
			    sortIndicator: true,
			    manualColumnResize: true,
				currentRowClassName: 'currentRow',
				cells: function (row, col, prop) {
					if("slipSplitQty"==prop){
						this.maxLength = 5;
					}
					if("dayMaxOrderQty"==prop){
						this.maxLength = 3;
					}
					var cellProperties = {};
					cellProperties.renderer = styleRender;
					return cellProperties;					
		    	},
		    	beforeChange : function(changes, source){
		    	},
		    	afterChange: function(changes, source) {
		    		afterChg(changes, source);
	 			},
	 			afterSelectionByProp: function(r, p, r2, p2) {
	 			}
			});
			chk=false;
			this.tbhot.loadData(arrData);
		    this.tbhot.render();
		}
		
	}
	TemplateGrid.CreatHotGrid('MainGrid',showlist);
}

var afterChg=function(changes, source){
	if (source === 'loadData'||source ==='external') {
        return; //don't save this change
    }
	if (changes && changes[0].length) {
		for(var i=0; i<changes.length; i++){
			var rowidx = changes[i][0];
			var colName = changes[i][1];
			var preValue = changes[i][2];
			var newValue = changes[i][3];
			if (preValue == undefined) {
				preValue = "";
			}
			RegistryGlobal.tableSourceData = TemplateGrid.tbhot.getSourceData();
			var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', rowidx);
			if("chkFlg"==colName){
				chk = "afterChg";
				if("0"==newValue||"1"==newValue){
					RegistryGlobal.tableSourceData[realRow].chkFlg=newValue;
				}else{
					TemplateGrid.tbhot.setDataAtRowProp(rowidx,"chkFlg",preValue);
				}
			}else if (preValue!=newValue && ("slipSplitQty"==colName || "dayMaxOrderQty"==colName || "sellingReferenceStandard"==colName)) {
				RegistryGlobal.tableSourceData[realRow].chkFlg="1";
				if (newValue == 0 && "slipSplitQty"==colName) {
					RegistryGlobal.tableSourceData[realRow].slipSplitQty="";
				}
				if (newValue == 0 && "dayMaxOrderQty"==colName) {
					RegistryGlobal.tableSourceData[realRow].dayMaxOrderQty="";
				}
			}
			TemplateGrid.tbhot.render();
		}
	}
}
var chk;
var chkAll = function(){
	if(chk==true){
		chk = false;
	}else{
		chk = true;
	}
	if(TemplateGrid.tbhot.getSourceData().length>0){		
		$.each(RegistryGlobal.showList, function(i, data){ 
				data.chkFlg = chk?"1":"0";
		});
	}
	TemplateGrid.tbhot.render();
}

var styleRender=function(instance, td, row, col, prop, value, cellProperties){
	Handsontable.renderers.TextRenderer.apply(this, arguments);
	var color;
	var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', row);
	if("chkFlg"==prop){//checkbox
		Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
		td.style.textAlign = "center";
	}else if("slipSplitQty"==prop||"dayMaxOrderQty"==prop){//checkbox
		Handsontable.renderers.NumericRenderer.apply(this, arguments);
		td.style.textAlign = "right";
		td.style.backgroundColor = "#FFFF99";
	}else if("sellingReferenceStandard"==prop){//checkbox
		if(value == null){
			Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
			td.style.textAlign = "left";
			td.style.backgroundColor = "#FFFF99";
		}else{
			var selectedId;
		    var optionsList = cellProperties.chosenOptions.data;
		    if(typeof optionsList === "undefined" || typeof optionsList.length === "undefined" || !optionsList.length) {
		        Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
		        return td;
		    }
		    var values = (value + "").split(",");
		    var value = [];
		    for (var index = 0; index < optionsList.length; index++) {
		        if (values.indexOf(optionsList[index].id + "") > -1) {
		            selectedId = optionsList[index].id;
		            value.push(optionsList[index].label);
		        }
		    }
		    value = value.join(", ");
		    Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
		    td.style.textAlign = "left";
			td.style.backgroundColor = "#FFFF99";
		    if(TemplateGrid.tbhot!=null && value==""){
		    	TemplateGrid.tbhot.setDataAtCell(row, col, null);
			}
		}
	}
	return td;
}	



