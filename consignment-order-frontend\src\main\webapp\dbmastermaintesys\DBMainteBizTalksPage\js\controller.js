/**
 * @description: DBマスタメンテナンス-イベント設定画面
 * @author: 10104911 zhoushaoh<PERSON>
 * @date: 2018/9/26
 */
var RegistryGlobal = {
	userCD: sessionStorage.getItem("userCD"),
	divMixList:[],//混載
	divCenterList:[],//センター
	divSupplierList:[],
	divList:[],
	divNewCenterList:[],
	tableData: [],
	tableSourceData: [],
	showList:[],
	init: null
}

$(document).ready(function () {
	$("#loadMask").show();
	$(document).ajaxStart(function(){
		$("#loadMask").show();
	}).ajaxStop(function(){
		$("#loadMask").hide();
	});
	//INIT
	DivMultiLoadInit();
	//ベンダー用画面Grid表示する
	getInitDataList();

	//検索
	$("#btn_search").on("click",function(){
		tableData_search();
	});
	//保存
	$("#btn_csv").on("click",function(){
		tableData_csv();
	});

	//JAN条件入力
	$("#cutJanArea").keypress(function(event){
		$("#cutJanArea").val(this.value.replace(/[^\d-.\n]/g,''));
		return ((event.charCode >= 48 && event.charCode <= 57)||(event.charCode == 13));
	});
	$("#newJanArea").keypress(function(event){
		$("#newJanArea").val(this.value.replace(/[^\d-.\n]/g,''));
		return ((event.charCode >= 48 && event.charCode <= 57)||(event.charCode == 13));
	});
	$('#cutJan_menu').on('click', function(event){
		event.stopPropagation();
	});
	$('#newJan_menu').on('click', function(event){
		event.stopPropagation();
	});

	//Cut商品入力
	$("#cutJan_reload").bind("click", function(){
		var janlistVal = "";
		var janListTxt=$("#cutJanArea").val();
		if (janlistCheck(janListTxt)){
			var janlistVal = janListTxt.replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
			var l = janlistVal.split(",").length;
			if(l>5000){
				showMsgBox(Message.msg020,[],function(){});//JANは5000件まで入力してください。
			}else{
				$("#btnCutJan").dropdown('toggle');}
		}else{
			if(janListTxt!=""){
				showMsgBox(Message.msg021,[],function(){});//正しいJANを入力してください。
			}else{
				$("#btnCutJan").dropdown('toggle');
			}
		}
	});
	//NEW商品入力
	$("#newJan_reload").bind("click", function(){
		var janlistVal = "";
		var janListTxt=$("#newJanArea").val();
		if (janlistCheck(janListTxt)){
			var janlistVal = janListTxt.replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
			var l = janlistVal.split(",").length;
			if(l>5000){
				showMsgBox(Message.msg020,[],function(){});//JANは5000件まで入力してください。
			}else{
				$("#btnNewJan").dropdown('toggle');}
		}else{
			if(janListTxt!=""){
				showMsgBox(Message.msg021,[],function(){});//正しいJANを入力してください。
			}else{
				$("#btnNewJan").dropdown('toggle');
			}
		}
	});
});
//JANチェック
janlistCheck=function(janlist){
	var regex = /^\d+([\s,]*\d+)*[\s,]*$/;
    var r = regex.test(janlist.split("...\r\n")[0].replace(/[ ]/g,""));
	return r;
}
//リストの設定
var DivMultiLoadInit=function(){
	$('#divCUTCenterMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全CUTセンター',
		nonSelectedText: '全て',
		nSelectedText: ' CUTセンター',
		numberDisplayed:1
	});
	
	$('#divSupplierMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全ベンダー',
		nonSelectedText: '全て',
		nSelectedText: ' ベンダー',
		numberDisplayed:1
	});
		
	$('#divCutDivisionMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全CUTDIV',
		nonSelectedText: '全て',
		nSelectedText: ' CUTDIV',
		numberDisplayed:1
	});
	
	$('#divNEWCenterMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全NEWセンター',
		nonSelectedText: '全て',
		nSelectedText: ' NEWセンター',
		numberDisplayed:1
	});
	
	//プラグイン呼び出し	
	$('#datepicker').datepicker({	
		language:  'ja',
		format:"yyyy/mm/dd",
		weekStart:1,
		todayBtn: "linked",
		autoclose: true,
		todayHighlight: true,
		orientation:"top auto",
		calendarWeeks:true
	});
	
}
//初期化
var getInitDataList=function(){
	$.ajax({
		url:SystemManage.address+'BizTalksController/getInitList',
		type:'POST',
		data:{userCD:sessionStorage.getItem("employeeCD"),//社員CD
	        venderCD:sessionStorage.getItem("userCD")//ベンダーCD
	    },
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg001,[],function(){});//初期化失敗しました。
			}else{				
				//var result = eval('('+response+')');
				var result = JSON.parse(response);
				RegistryGlobal.divCenterList = result[0].CUTCenterList;
				RegistryGlobal.divSupplierList = result[0].SupplierList;
				RegistryGlobal.divList = result[0].CutDivisionList;
				RegistryGlobal.divNewCenterList = result[0].NewStorehouseList;
				//センター
				bindData(RegistryGlobal.divCenterList,"centerCD","centerName","divCUTCenterMulti","");
				//ベンダー
				bindData(RegistryGlobal.divSupplierList,"supplierCD","supplierName","divSupplierMulti","");
				//div
				bindData(RegistryGlobal.divList,"divisionCD","divisionName","divCutDivisionMulti","");
				//新センター
				bindData(RegistryGlobal.divNewCenterList,"centerCD","centerName","divNEWCenterMulti","");
				//ベンダー選択不可用
				if(RegistryGlobal.userCD){
					$('#divSupplierMulti').multiselect("disable");
				}
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg001,[],function(){});//初期化失敗しました。
		}
	});
	//商談日設定
	$("#ddl_day1").datepicker("setDate", new Date());
	$("#ddl_day2").datepicker("setDate", new Date());
	//Grid　初期データロード
	TemplateGrid.CreatHotGrid('MainGrid',RegistryGlobal.showList);
}

//検索
var tableData_search = function(){
	RegistryGlobal.init = "search";
	var supplierStr;//ベンダー
	var cutProduct;//CUT商品
	var newProduct;//NEW商品
	var cutDivision;//CUTDIV
	var cutCenterStr;//CUTセンター
	var newCenterStr;//NEWセンター
	var startDate;
	var endDate;
	
	if (!$("#divSupplierMulti").val()) {
		supplierStr = sessionStorage.getItem("userCD")//ベンダーCD
	} else {
		supplierStr = $("#divSupplierMulti").val();
		if (supplierStr) {
			supplierStr = supplierStr.join(",");
		}
	}
	cutDivision 	= $("#divCutDivisionMulti").val();
	cutCenterStr 	= $("#divCUTCenterMulti").val();
	newCenterStr 	= $("#divNEWCenterMulti").val();
	startDate 		= $("#ddl_day1").val();
	endDate 		= $("#ddl_day2").val();
	if (cutDivision) {
		cutDivision = cutDivision.join(",");
	}
	if (cutCenterStr) {
		cutCenterStr = cutCenterStr.join(",");
	}
	if (newCenterStr) {
		newCenterStr = newCenterStr.join(",");
	}
	//CUT商品/NEW商品
	cutProduct = $("#cutJanArea").val().replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
	newProduct = $("#newJanArea").val().replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
	$.ajax({
		url:SystemManage.address+'BizTalksController/getSearchInfo',
		type:'POST',
		data:{
			supplierStr 	:	supplierStr, 						
			userCD			:	sessionStorage.getItem("employeeCD"),//社員CD
			cutProduct 		:	cutProduct,
			newProduct 		:	newProduct,
			cutDivision 	:	cutDivision,
			cutCenterStr	:	cutCenterStr,
			newCenterStr 	:	newCenterStr,
			startDate 		:	startDate,
			endDate 		:	endDate
		},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
			}else{				
				var result = JSON.parse(response);
				RegistryGlobal.showList = result;
				//showList["ChkFlg"] = 0;
				TemplateGrid.CreatHotGrid('MainGrid',RegistryGlobal.showList);
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
		}
	});
}

//download csv
var funDownload = function (content, filename) {
	if (window.navigator.msSaveOrOpenBlob) {
		// if browser is IE
	  	var blob = new Blob(['\uFEFF',decodeURIComponent(encodeURI(content))], {
	    	type: "text/csv;charset=utf-8;"
	  	});
	  	navigator.msSaveBlob(blob, filename);
	}else{
    	var eleLink = document.createElement('a');
        eleLink.download = filename;
        eleLink.style.display = 'none';
        var sjis_array = UTF8_ShiftJIS(content);
        var uint8_array = new Uint8Array(sjis_array);
        var blob = new Blob([uint8_array],{type: "text/csv"}); 
        eleLink.href = URL.createObjectURL(blob);   
        document.body.appendChild(eleLink);
        eleLink.click();
        document.body.removeChild(eleLink);
    }
};

//CSV出力
var tableData_csv = function(){
	var supplierStr;//ベンダー
	var cutProduct;//CUT商品
	var newProduct;//NEW商品
	var cutDivision;//CUTDIV
	var cutCenterStr;//CUTセンター
	var newCenterStr;//NEWセンター
	var startDate;
	var endDate;
	
	if (!$("#divSupplierMulti").val()) {
		supplierStr = sessionStorage.getItem("userCD")//ベンダーCD
	} else {
		supplierStr = $("#divSupplierMulti").val();
		if (supplierStr) {
			supplierStr = supplierStr.join(",");
		}
	}
	cutDivision 	= $("#divCutDivisionMulti").val();
	cutCenterStr 	= $("#divCUTCenterMulti").val();
	newCenterStr 	= $("#divNEWCenterMulti").val();
	startDate 		= $("#ddl_day1").val();
	endDate 		= $("#ddl_day2").val();
	if (cutDivision) {
		cutDivision = cutDivision.join(",");
	}
	if (cutCenterStr) {
		cutCenterStr = cutCenterStr.join(",");
	}
	if (newCenterStr) {
		newCenterStr = newCenterStr.join(",");
	}
	//CUT商品/NEW商品
	cutProduct = $("#cutJanArea").val().replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
	newProduct = $("#newJanArea").val().replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
	$.ajax({
		url:SystemManage.address+'BizTalksController/getSearchInfo',
		type:'POST',
		data:{
			supplierStr 	:	supplierStr, 						
			userCD			:	sessionStorage.getItem("employeeCD"),//社員CD
			cutProduct 		:	cutProduct,
			newProduct 		:	newProduct,
			cutDivision 	:	cutDivision,
			cutCenterStr	:	cutCenterStr,
			newCenterStr 	:	newCenterStr,
			startDate 		:	startDate,
			endDate 		:	endDate
		},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg043,['出力'],function(){});//出力失敗しました。
			}else{				
				var result = JSON.parse(response);
				var fields = ['bizTalksID', 'bizTalksTitle', 'author', 'registered', 'approved', 'cutProduct', 'newProduct', 'cutStorehouse', 'newStorehouse'];
			    var headNames = ['商談ID', 'タイトル', '提出者', '提出日', '承認日', 'CUT商品', '新商品', 'CUTセンター', '新センター'];
				//json data transform csv format
			    var csv = json2csv({data:result, fields:fields, fieldNames:headNames});
			    var filename = "BizTalk" + "-" + getNowTime() + ".csv";
			    funDownload(csv.replaceAll("\"", ""), filename);
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg043,['出力'],function(){});//出力失敗しました。
		}
	});
}

/////////////////////////////////////////////////////////
//bindDataとonchange事件
//data     Json
//valuecol 項目CDString
//labelcol 項目名 String
//id       String
//multiple  "all":全選(複数選択のみ有効) 
//        "single":初期に選択項目
//        Array:選択したデータ  []:すべて選択しない
////////////////////////////////////////////////////////
function bindData(filtdata ,valuecol, labelcol, id, multiple){ 
 var data = filtdata;
 var options=[];
 for(var i=0;i<data.length;i++){
	   options.push({
		   label:data[i][valuecol]+"-"+data[i][labelcol],
		   title:data[i][labelcol],
		   value:data[i][valuecol]
	   });
 }
 $('#'+id).multiselect('dataprovider', options);
 if(multiple=="all"){
	 	//全選  及び　onChange trigger
	 	$('#'+id).multiselect('selectAll',false,true);
	 	$('#'+id).multiselect('updateButtonText');				   
 } else if(multiple=="single"){
	 	//第一の項目選択　及び　onChange trigger
	 	$('#'+id).multiselect('select',options[0].value,true);			     
 } else {
	   	//選択設定   及び　onChange trigger
	 $('#'+id).multiselect('select',multiple,true);
 }
}