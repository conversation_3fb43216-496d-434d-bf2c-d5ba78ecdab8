<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>商品登録画面</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width, initial-scale=1" name="viewport"/>
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Cache-Control" content="no-cache">
	<meta http-equiv="Expires" content="0">
	<link rel="icon" type="image/png/ico" href="../Common/favicon.ico">
	<!-- basic styles -->
	<link href="../Common/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <link href="../Common/common.css" rel="stylesheet">
    <link href="../Common/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/bootstrap/css/bootstrap.min.css" rel="stylesheet">
	<link href="../Common/css/layout.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/css/themes/light2.css" rel="stylesheet" type="text/css" id="style_color"/>
	<link href="../Common/bootstrap-multiselect/css/bootstrap-multiselect.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/handsontable/handsontable.full.min.css" rel="stylesheet"/>
	<link href="../Common/jan/jan.css" rel="stylesheet"/>
	<link href="../Common/prettify/prettify.css" rel="stylesheet"/>
	<link href="css/style.css" rel="stylesheet" type="text/css" />
	<script src="../Common/json2csv.js" type="text/javascript" ></script>
    <script src="../Common/jquery/jquery.min.js" type="text/javascript"></script>
	<script src="../Common/bootstrap-multiselect/js/bootstrap-multiselect.js" type="text/javascript" ></script>
	<script src="../Common/handsontable/handsontable.full.min.js" type="text/javascript" ></script>
	<script src="../Common/handsontable/handsontable.maxlength.full.js" type="text/javascript" ></script>
    <script src="../Common/prettify/prettify.js" type="text/javascript" ></script>
    <script src="../Common/jquery/jquery.form.js" type="text/javascript" ></script>
	<script src="../Common/js/ui.min.js"></script>
    <link href="../Common/handsontable/chosen.css" rel="stylesheet"/>
	<script>document.write("<s"+"cript src='../Common/version.js?ver="+Math.random()+"'></scr"+"ipt>"); </script>
	<script>document.write("<s"+"cript src='../Common/common.js?ver="+sysversion+"'></scr"+"ipt>");</script>
	<link rel="shortcut icon" href="../Common/favicon.ico"/>
</head>
<body class="page-container-bg-solid">
<script>
	var href=window.document.location.href;
	var rankNum=getRank(href);
</script>
<!-- BEGIN CONTAINER -->
<div class="page-container">
	<!-- BEGIN CONTENT -->
	<div class="page-content-wrapper">
		<div class="page-content">	
			<!--#検索条件 form-->
			<div class="form-horizontal">
				<div class="panel panel-inverse">
						<div class="panel-heading">
							<h3 class="panel-title">
								<a class="accordion-toggle accordion-toggle-styled" data-toggle="collapse"
								   data-parent="#accordion" href="#collapseOne" aria-expanded="true">
									<i class="fa fa-plus-circle pull-right"></i>
									検索条件
								</a>
							</h3>
						</div>
						<div id="collapseOne" class="panel-collapse collapse in" aria-expanded="true">
							<div class="panel-body">
								<div class="form-group" >
									<div class="btn-group lblMaxWidth"  role="group" style="margin-left: 10px;">
										<label>センター</label>
									</div>
									<div class="btn-group selWidth"  role="group">
										<select id="divstoreHouseMulti" size="5" multiple class="form-control"></select>
									</div>
									<div class="btn-group lblWidth"  role="group">
										<label>JAN</label>
									</div>
									<div class="btn-group selWidth"  role="group">
										<span class="dropdown btn-group" style="margin-left:0px;">
											<button type="button" id="parse_button" style="width:100%;"
													class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown"
													aria-haspopup="true" title="JANは完全入力でのみ検索可能です"
													aria-expanded="false">JAN入力
												<span class="caret"></span>
											</button>
											<ul class="dropdown-menu scrollable-menu" id="parse_menu">
												<li>
													<a href="javascript:void(0)" style="padding:0 5px;">
															<textarea id="janArea" class="form-control" style="width:100%;" rows="5" placeholder="JANは完全入力でのみ&#13;&#10;検索可能です" onfocus="this.select();"></textarea>
													</a>
												</li>
												<li role="separator" class="divider"></li>
												<li style="padding:0px 5px;">
													<button type="button" class="btn btn-success btn-sm"
														style="display: block; width: 100%;" id="jan_reload">確定
													</button>
												</li>
											</ul>
										</span>
									</div>
									<div class="btn-group lblWidth"  role="group">
										<label>DIV</label>
									</div>
									<div class="btn-group selWidth"  role="group">
										<select id="divMulti" size="5" multiple class="form-control"></select>
									</div>
									<div class="btn-group lblWidth"  role="group">
										<label>発注区分</label>
									</div>
									<div class="btn-group selWidth"  role="group">
										<select id="divOrderFlgMulti" size="5" multiple class="form-control"></select>
									</div>
									<div class="btn-group lblMaxWidth"  role="group">
										<label>納品先</label>
									</div>
									<div class="btn-group selWidth"  role="group">
										<select id="divNohiSakiMulti" size="5" multiple class="form-control"></select>
									</div>
								</div>
								<div class="form-group">
									<div class="btn-group lblMaxWidth"  role="group" style="margin-left:10px;">
										<label>発注センター</label>
									</div>
									<div class="btn-group selWidth"  role="group">
										<select id="divOrderCenterMulti" size="5" multiple class="form-control"></select>
									</div>
									<div class="btn-group lblWidth"  role="group">
										<label>参照JAN</label>
									</div>
									<div class="btn-group selWidth"  role="group">
										<span class="dropdown btn-group" style="margin-left:0px;">
											<button type="button" id="parse_button_san" style="width:100%;"
												class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown"
													aria-haspopup="true" title="参照JANは完全入力でのみ検索可能です"
													aria-expanded="false">参照JAN入力
											<span class="caret"></span>
											</button>
											<ul class="dropdown-menu scrollable-menu" id="parse_menu_san">
												<li>
													<a href="#" style="padding:0 5px;">
														<textarea id="janArea_san" class="form-control" style="width:100%;" rows="5" placeholder="参照JANは完全入力でのみ&#13;&#10;検索可能です" onfocus="this.select();"></textarea>
													</a>
												</li>
												<li role="separator" class="divider"></li>
												<li style="padding:0px 5px;">
													<button type="button" class="btn btn-success btn-sm"
													style="display: block; width: 100%;" id="jan_reload_san">確定</button>
												</li>
											</ul>
										</span>
									</div>
									<!-- <div class="btn-group selWidth"  role="group">
										<select id="divConsultJanMulti" size="5" multiple class="form-control"></select>
									</div>	 -->
									<div class="btn-group lblWidth"  role="group" >
										<label>混載ID</label>
									</div>
									<div class="btn-group selWidth"  role="group">
										<select id="divMixCDMulti" size="5" multiple class="form-control"></select>
									</div>
									<div class="btn-group lblWidth"  role="group" >
										<label>継続区分</label>
									</div>
									<div class="btn-group selWidth"  role="group">
										<select id="divContinuMulti" size="5" multiple class="form-control"></select>
									</div>
									<div class="btn-group lblMaxWidth"  role="group" >
										<label>物流タイプ</label>
									</div>
									<div class="btn-group selWidth" role="group">
										<select id="divShipptypeMulti" size="5" multiple class="form-control"></select>
									</div>
								</div>
								<div class="form-group" style="margin-bottom: 0px;">
									<div class="btn-group lblMaxWidth"  role="group" style="margin-left:10px;">
										<label>ベンダー</label>
									</div>
									<div class="btn-group selWidth"  role="group">
										<select id="divSupplierMulti" size="5" multiple class="form-control"></select>
									</div>
								</div>
							</div>
						</div>
				</div>
				<div class="form-group" style="margin-left: 28%;">
					<div class="btn-group btnWidth"  role="group" >
						<input id="btn_search" type="button" class="btn btn-primary btn-block" value="検索">
					</div>
					<div class="btn-group btnWidth"  role="group" >
						<input id="btn_confirm" type="button" class="btn btn-primary btn-block" value="登録">
					</div>	
					<div class="btn-group btnFmtWidth"  role="group" >
						<!-- <input id="btn_ExcelPort" type="button" class="btn btn-primary btn-block" value="CSV出力"> -->
						<a id="btn_ExcelPort" class="btn btn-success btn-block">
							<i class="glyphicon glyphicon-download-alt">
							</i>Excel出力</a>
					</div>	
					<div class="btn-group btnFmtWidth"  role="group" >
						<input id="btn_ImportFrmat" type="button" class="btn btn-primary btn-block" value="フォーマット出力">
					</div>						
					<div class="btn-group btnWidth"  role="group" >
						<input id="btn_Import" type="button" class="btn btn-primary btn-block" value="取込">
					</div>
					<div class="btn-group btnWidth"  role="group" >
						<input id="btn_Del" type="button" class="btn btn-primary btn-block" value="削除">
					</div>
					<div class="btn-group btnWidth"  role="group" >
						<input id="btn_orderCase" type="button" class="btn btn-primary btn-block" value="案再作成">
					</div>
				</div>
			</div>
			<!--/Upload form-->
			<div class="col-md-12 col-sm-12 col-xs-12" id="supportupload" style="display:none; padding:0px 10px 10px 10px;">
				<form action="" id="uploadSupportForm" method="POST" enctype="multipart/form-data">
					<hr>
					<input id ="filesupport" type="file" name="supportfile" class="file" data-upload-url="#">
					<input id ="fileuser" type="text" name="userCD"  data-upload-url="#">
					<input id ="filevender" type="text" name="venderCD"  data-upload-url="#">
				</form>
			</div>
			<!--/検索条件 form-->
			<div class="col-md-12 col-sm-12 col-xs-12" id="grid" style="padding:0px;">
				<div id="MainGrid" style="width:100%"></div>
			</div> 
		</div>
	</div>
	<!-- END CONTENT -->
</div>
<!-- END CONTAINER -->

<!-- BEGIN FOOTER -->
<div class="page-footer" style="display: none; height: 4px; padding: 0px; margin: 0px;">
</div>
<!-- END FOOTER -->

<!--メッセージ表示-->
<div class="modal fade" id="myMessage" tabindex="-2" role="dialog" aria-labelledby="" style="z-index: 2000;margin-top:100px;">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-header">
	        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			</button>
	        <h4 class="modal-title" id="myMessageTitle">DBマスタメンテ</h4>
	      </div>
	      <div class="modal-body" >
	      	  <p id="myMessageBody"></p>
	      </div>
	      <div class="modal-footer">
	          <button type="button" id="msgSysBtnYes" class="btn btn-default">OK</button>
	          <button type="button" id="msgSysBtnNo" class="btn btn-default" data-dismiss="modal">キャンセル</button>
	      </div>
	    </div>
	  </div>
</div>
<!--確認メッセージ表示-->
<div class="modal fade" id="confMessage" tabindex="-2" role="dialog" aria-labelledby="" style="z-index: 2000;margin-top:100px;">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-header">
	        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span></button>
	        <h4 class="modal-title" id="confMessageTitle">DBマスタメンテ</h4>
	      </div>
	      <div class="modal-body" >
	      	  <p id="confMessageBody"></p>
	      </div>
	      <div class="modal-footer">
	          <button type="button" id="msgConfSysBtnYes" class="btn btn-default">OK</button>
	          <button type="button" id="msgConfSysBtnNo" class="btn btn-default" data-dismiss="modal">キャンセル</button>
	      </div>
	    </div>
	  </div>
</div>
<!--削除用メッセージ表示-->
<div class="modal fade" id="delMessage" tabindex="-2" role="dialog" aria-labelledby="" style="z-index: 2000;margin-top:100px;">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="delMessageTitle">DBマスタメンテ</h4>
			</div>
			<div class="modal-body" >
				<p id="delMessageBody"></p>
			</div>
			<div class="modal-footer">
				<button type="button" id="msgDelSysBtnYes" class="btn btn-default">OK</button>
				<button type="button" id="msgDelSysBtnNo" class="btn btn-default" data-dismiss="modal">キャンセル</button>
			</div>
		</div>
	</div>
</div>
<!--バンドル用メッセージ表示-->
<div class="modal fade" id="bundleMessage" tabindex="-2" role="dialog" aria-labelledby="" style="z-index: 2000;margin-top:100px;">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="bundleMessageTitle">DBマスタメンテ</h4>
			</div>
			<div class="modal-body" >
				<p id="bundleMessageBody"></p>
			</div>
			<div class="modal-footer">
				<button type="button" id="msgBundleSysBtnYes" class="btn btn-default">OK</button>
				<button type="button" id="msgBundleSysBtnNo" class="btn btn-default" data-dismiss="modal">キャンセル</button>
			</div>
		</div>
	</div>
</div>
<script src="../Common/jquery/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
<script src="../Common/bootstrap/js/bootstrap.min.js"></script>
<script src="../Common/message/message.js"></script>
<script src="../Common/common.js"></script>
<script src="../Common/js/metronic.js" type="text/javascript"></script>
<script src="../Common/js/layout.js" type="text/javascript"></script>
<script src="../Common/handsontable/chosen.jquery.js" type="text/javascript" ></script>
 <script src="../Common/handsontable/handsontable-chosen-editor.js" type="text/javascript" ></script>
<script>
	jQuery(document).ready(function() {    
	   Metronic.init(); // init metronic core componets
	   Layout.init(); // init layout
	});
	makeProcessing(rankNum);
</script>
<script>document.write("<s"+"cript src='js/gridconfig.js?ver="+sysversion+"'></scr"+"ipt>");</script>
<script>document.write("<s"+"cript src='js/controller.js?ver="+sysversion+"'></scr"+"ipt>");</script>
<script>document.write("<s"+"cript src='js/ordercasedetail.js?ver="+sysversion+"'></scr"+"ipt>");</script>
</body>
<!-- END BODY -->
</html>