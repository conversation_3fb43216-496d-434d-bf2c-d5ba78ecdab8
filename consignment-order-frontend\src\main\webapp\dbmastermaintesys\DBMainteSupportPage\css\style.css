@CHARSET "UTF-8";
.vspace{
	height: 10px;
}
.lblMaxWidth{
	width: 8%;
 	text-align: left;
 	padding-left: 10px; 
}
.lblWidth{
	width: 7%;
 	text-align: left;
 	padding-left: 10px; 
}
.selWidth{
	width: 11%;
}
.ordSelWidth{
	width: 15%;
}
.selover{
	white-space:nowrap; 
	overflow:hidden; 
	text-overflow:ellipsis;
}
.janWidth{
	width: 11%;
}
.dateWidth{
	width: 12%;
}
.btnWidth{
	width: 10%;
 	margin-left: 2%;
}
.btnFmtWidth{
	width: 14%;
 	margin-left: 1%;
}
.btn-footer{
	padding: 25px;
    text-align: right;
    border-top: 0px solid #e5e5e5;
    float: right;
}
/* 		table .htCore */
#dgJiseki th{
	vertical-align: middle!important;
}
.handsontable .tdBgColor{
	/* color:red; */
	background-color: #ff0000 !important;
}
.handsontable .tdBgColor{
	/* color:red; */
	background-color: #FFFF99;
}
.dispalynone{
	display:none;
}
.selWidth .btn-group{
	width:100%;
}
.selWidth .multiselect {
	width:100%;
}
.selWidth ul {
	min-width: 170px;
}

/*案再作成画面ｃｓｓ*/
.ordSelWidth .btn-group{
	width:100%;
}
.ordSelWidth .multiselect {
	width:100%;
}
.ordSelWidth ul {
	min-width: 170px;
}
.currentRow{
	background-color: lightskyblue!important;
}
/*.htCore td{
	vertical-align: middle;
}*/
.orderCaseTbhot th{
	background-color: lightcyan!important;
}
.ordLblWidth{
	width: 6%;
	text-align: left;
	padding-left: 8px;
}

/*検索条件*/
.panel-title .accordion-toggle {
	margin: -10px -15px;
	padding: 10px 15px
}

.panel-title .accordion-toggle.accordion-toggle-styled .fa:before {
	content: '\f056'
}

.panel-title .accordion-toggle.accordion-toggle-styled.collapsed .fa:before {
	content: '\f055'
}
.panel-body{
	border-top: 1px solid #CCC;
}
.ht_clone_left.handsontable tbody td{
	height:25px!important;
}
.noteTitle{
	color:#ff0000;
	vertical-align: bottom;
	margin-bottom: 0px;
	font-size: 12px;
	padding-top: 6px;
}
.panel-heading {
	padding: 5px 15px;
}
.panel{
	margin-bottom: 10px;
}
.page-content-wrapper .page-content {
	padding-top: 2px;
}
*{ touch-action: pan-y; }
.btnOrdWidth{
	width: 8%;
	margin-left: 2%;
}