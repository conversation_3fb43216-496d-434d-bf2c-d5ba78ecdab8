/**
 * Model: 
 * Page: index.html
 * Function: controller.js
 */
var leftmenu = false;
$(document).ready(function () {
	//page root direct path
	var parentPath = ProjectPath.getPageParentDireactPath();
	//page root direct name 
	var parentDireactName = ProjectPath.getPageParentDireactName();
	$("#loadMask").show();
	/**left menu html load*/
	$.ajax({
		url:'../Page/menu_getOrderCaseInfoData.action',
		type:'POST',
		data:{
			login_id:userCD,
			login_employee:employeeCD
		},
		success:function(response){
			$("#loadMask").hide();
			if(response =="false"){
				showMsgBox(Message.msg022,[],function(){});
	    	}else{
	    		//var result = eval("("+response+")");
				var result = JSON.parse(response)
				if(result[0][0]==null||result[0][0]==''){
					$("#orderCaseInfo").hide();
					$("#orderCaseChk").html("本日にトライアルからシステム自動推奨発注案がありません。");
					return;
				}else if(employeeCD!=null && employeeCD!=""){
					$("#orderCaseInfo").hide();
					$("#orderCaseChk").html("本日にトライアルからシステム自動推奨発注案があります。");
				}else{
					$("#orderCaseInfo").show();
					$("#orderCaseChk").html("本日にトライアルからシステム自動推奨発注案があります。");
					$("#orderCaseLink").html("推奨案："+result[0][0].orderCaseInfo);
				}
	    	}
		},
		error:function(){
			$("#loadMask").hide();
			showMsgBox(Message.msg022,[],function(){});
		}
	});
});