package jp.trial.DCOrderConsignment.common;

import java.io.UnsupportedEncodingException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

/**Description: UTF-8 check*/
public class GetHttpServletRequestWrapper extends HttpServletRequestWrapper{
	
	private String charset = "UTF-8";
	
	public GetHttpServletRequestWrapper(HttpServletRequest request) {
		super(request);
	}
	
	public GetHttpServletRequestWrapper(HttpServletRequest request,
			String charset) {
		super(request);
		this.charset = charset;
	}
	
	public String getParameter(String name){
		String value = super.getParameter(name);
		return value == null ? null : convert(value);
	}
	
	public String convert(String target){
		String targetConvert = null;
		try {
			targetConvert = new String(target.trim().getBytes("ISO-8859-1"), charset);
		} catch (UnsupportedEncodingException e1) {
			targetConvert = target;
		}
		return targetConvert;
	}
}
