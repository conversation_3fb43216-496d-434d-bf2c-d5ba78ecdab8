/**
* 商品JAN選択共通モジュール
* 前提条件：Jquery,zTree,handsontableが必須
* href="../static/zTree/css/zTreeStyle.css"
* src="../static/zTree/js/jquery.ztree.core-3.5.min.js"
* src="../static/zTree/js/jquery.ztree.excheck-3.5.min.js"
* href="../static/handsontable/handsontable.full.min.css"
* src="../static/handsontable/handsontable.full.min.js"
*
* author sugar.Tang  2015/10/19
*/
var jan = (function ($) {
    var id=Math.floor(Math.random() * 1000);
    var $dialog = $(
    '<div id="jan-dialog-module'+id+'" class="modal fade" tabindex="-1">' +
        '<div class="modal-dialog" style="width: 550px;" >' +
            '<div class="modal-content" id="jan-modal-content'+id+'">' +
                '<div class="modal-header" style="padding:10px 15px;">' +
                    '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '<h4 class="modal-title" style="display:inline-block;">単品選択</h4>' +
                    '<span class="btn-group" style="margin-left:290px;">' +
                        '<button type="button" id="btnFavShow'+id+'" class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i class="glyphicon glyphicon-star-empty"></i>お気に入り' +
                            '<span class="caret"></span>' +
                        '</button>' +
                        '<ul class="dropdown-menu scrollable-menu" id="Fav'+id+'">' +
                            '<li><a href="#" style="padding:0 5px;">'+
                                '<div class="input-group">' +
                                    '<input type="text" class="form-control input-sm" id="txtFav'+id+'" placeholder="お気に入り名">' +
                                    '<span class="input-group-btn">' +
                                        '<button class="btn btn-default btn-sm" id="InsertFav'+id+'" type="button"><i class="glyphicon glyphicon-plus"></i></button>' +
                                    '</span>' +
                                '</div>' +
                            '</a></li>' +
                            '<li role="separator" class="divider"></li>' +
                        '</ul>' +
                    '</span>' +
                '</div>' +
                '<div class="panel panel-default" id="content'+id+'" style="margin:5px 5px 5px 5px;height:450px;border:0px;">'+
                    '<div id="tabContent'+id+'" class="tab-content" style="margin-left:17px;">'+
                        '<div class="tab-pane fade in active" id="cate'+id+'">'+
                            '<form class="form-horizontal" role="form" style="margin-top:10px;">' +
                                '<fieldset>' +
                                    '<div class="form-group">' +
                                        '<label class="col-sm-2 control-label" style="font-weight:normal;padding-left: 5px;" >階層選択：</label>' +
                                        '<div class="col-sm-3" style="padding-left: 0px;">' +
                                            '<input id="categorySel'+id+'" data-toggle="dropdown"  type="button" class="btn btn-default" value="商品選択" style="text-align:left;color:gray;font-size:12px;padding-top: 5px;height:26px;visibility: visible;width:120px;">' +
                                            '<div id="menuContent'+id+'" aria-labelledby="categorySel'+id+'" class="menuContent" style="z-index:999;display:none;border: 1px solid #CCCCCC; background-color: white;width: 400px;height: 420px;overflow: auto;position: absolute;">' +
                                                '<ul id="jan-tree-module'+id+'" class="ztree" style="margin-top:0; width:100%;overflow-y: scroll;height:85%"></ul>' +
                                                '<div class="modal-footer" style="height:15%">' +
                                                    '<button class="btn btn-default btn-sm" id="treeOk'+id+'" type="button">決定</button>' +
                                                    '<button class="btn btn-default btn-sm" id="treeCancle'+id+'" type="button">キャンセル</button>' +
                                                '</div>' +
                                            '</div>' +
                                        '</div>' +
                                        '<div class="col-sm-3" style="width: 100px;padding-right: 0px;">' +
                                            '<label style="font-weight:normal;"> キーワード： </label>' +
                                        '</div>' +
                                        '<div class="col-sm-4">' +
                                            '<input id="inputKey'+id+'" type="text" value="" style="height:25px;width:180px;border: 1px solid #ccc;border-radius: 3px;font-size:12px;padding-top: 3px;padding-left: 3px;" placeholder=" 商品名/規格/メーカー/ブランド">' +
                                        '</div>' +
                                    '</div>' +
                                    '<div class="form-group">' +
                                        '<label class="col-sm-2 control-label" style="font-weight:normal;padding-left: 5px;padding-top: 0px"> JAN入力： </label>' +
                                        '<div class="col-sm-4" style="padding-left: 0px;">' +
                                            '<textarea  id="janArea'+id+'" style="border: 1px solid #ccc;border-radius: 5px;resize: none;width:160px;height:50px;font-size: 12px;"placeholder="JANは改行して入力してください" ></textarea>' +
                                        '</div>' +
                                        '<div class="col-sm-4" style="width: 150px;margin-top: 30px;">' +
                                            '<input id="privProdCate'+id+'" type="checkbox">' +
                                            ' <label id="privProdCateTxt'+id+'" style="width:100px;font-weight:normal;font-size:13px;">自社商品のみ</label>' +
                                        '</div>' +
                                        '<div class="col-sm-2" style="margin-top: 20px;">' +
                                            '<button class="btn btn-success btn-sm" id="searchKeyCate'+id+'" type="button"  style="width:60px;">検索</button></p>' +
                                        '</div>' +
                                    '</div>' +
                                    '<div id="datatableCate'+id+'" style="margin:-15px 0px 5px 0px;height:340px;overflow:hidden;z-index:10;" style="height:490px;overflow:hidden;z-index:10;"></div>'+
                                '</fieldset>' +
                            '</form>' +
                        '</div>'+
                    '</div>'+
                '</div>'+
                '<div class="modal-footer">' +
                    '<div class="btn-group pull-left" data-toggle="buttons" id="pre'+id+'">' +
                        '<label class="btn btn-default btn-sm" id="lp'+id+'">' +
                            '<input type="checkbox" id="preview'+id+'" autocomplete="off" checked=true><span>選択項目のみ表示</span>' +
                        '</label>'+
                    '</div>' +
                    '<span class="pull-left label label-warning" style="margin-top:5px;" id="jan_num'+id+'">0件</span>'+
                    '<span id="showMess'+id+'" style="color:red;margin-right:5px;font-size:12px;display:none !important;">未確定状態</span>'+
                    '<button type="button" id="btnOK'+id+'" class="btn btn-default" data-dismiss="modal">選択決定</button>' +
                    '<button type="button" class="btn btn-default" id="btnClear'+id+'">画面クリア</button>' +
                    '<button type="button" class="btn btn-default" data-dismiss="modal">閉じる</button>' +
                '</div>' +
            '</div>' +
            '<div id="overlay'+id+'" class="overlay" style="display:none;"></div>' +
        '</div>' +
    '</div>'
    );
    var $treeUrl = "/mdlink/module/APP/CGI/JAN_MODULE_ALL.CGI";
    var $favListUrl = "/mdlink/module/APP/CGI/JAN_LOAD_FAV_LIST.CGI";
    var $favDelUrl = "/mdlink/module/APP/CGI/JAN_DEL_FAV.CGI";
    var $favReadUrl = "/mdlink/module/APP/CGI/JAN_FAV_READ.CGI";
    var $favInsertUrl = "/mdlink/module/APP/CGI/JAN_FAV_INSERT.CGI";
    var $favJanSearch = "/mdlink/module/APP/CGI/JAN_FAV_SEARCH.CGI";
    //var $searchKeyCateJanUrl = "/mdlink/module/APP/CGI/JAN_SEARCH.CGI_20151113";
    var $searchKeyCateJanUrl = "/mdlink/module/APP/CGI/JAN_SEARCH.CGI";
    var $isCaptain = "/mdlink/module/APP/CGI/JAN_IS_CAPTAIN.CGI";
    var $setJanValueUrl = "/mdlink/module/APP/CGI/JAN_SET_FAV.CGI";
    var $fun = {
        handsontableCate : [],
        chartdata : [],
        handsontableDataCate : [],
        handsontableDataTabCate : [],
        janDataCate : [],
        handsontableOkData : [],
        favTabType: "",
        param : {},
        firstLevel:"-d-",
        expandFirst: [],/*最初に展開する必要ノード*/
        expandParam: [],/*展開する必要のノード*/
        checkParam: [],/*チェックを入れる必要ノード*/
        flg:["-d-", "-l-", "-b-", '-c-', '-sc-', '-sg-', '-ssg-'],/*各階層間の区別フラグ*/
        flgNm:["ディビジョン", "ライン", "部門", 'カテゴリ', 'サブカテゴリ', 'セグメント', 'サブセグメント'],/*各階層間の区別フラグ*/
        selectedParamArray: [],/*選択されたノード*/
        selectedParam: [],/*選択されたノード、処理済み、最終のデータ形*/
        dialog: function(){
            return $dialog;
        },
        menuContent: function(){
            return $("#menuContent"+id);
        },
        favLoad : function(){/*お気に入りロード*/
            $.ajax({
                type: 'POST',
                url: $favListUrl,
                data: "usercd=" + $fun.param.usercd,
                success: function(response) {
                    $("#Fav"+id+" li").remove(".child");
                    var json = eval(response);
                    for(var i = 0; i < json.length; i++){
                        var child_li = $('<li class="child"></li>');
                        var child_a = $('<a href="#"></a>');
                        var del = $('<i class="glyphicon glyphicon-remove"></i>');
                        del.bind('click',{name : json[i].name}, $fun.favDelete);
                        var span = $('<div style="display:inline-block;width:100%;">'+json[i].name+'</div>');
                        span.bind('click', {name : json[i].name}, $fun.favReCheck);
                        child_a.append(del);
                        child_a.append(span);
                        child_li.append(child_a);
                        $("#Fav"+id).append(child_li);
                    }
                }
            });
        },
        favDelete : function(e){/*お気に入り削除*/
            if(!window.confirm("削除しますか？")){
                return;
            };
            $.ajax({
                type: 'POST',
                url: $favDelUrl,
                data: "usercd=" + $fun.param.usercd + "&name=" + e.data.name,
                success: function(response) {
                    if (response.replace("\n","") == "success"){
                        $fun.favLoad();
                    }
                }
            });
        },
        checkMsg:function(){
            var janNum=[];
            var janArr = $fun.handsontableCate.getDataAtCol(0);
            for (var i = 0; i < janArr.length; i++) {
                if (janArr[i] == true) {
                    janNum.push(janArr[i]);
                };
            };
            $fun.janNumber(janNum);
        },
        checkallCate:function(){
            var chkArr = $fun.handsontableCate.getDataAtCol(0);
            if (chkArr[0]==null) {return;};
            if (chkArr.join("_").indexOf("false") == -1 ) {
                if (false==$("#jan-modal-content"+id+" .checkallCate").prop("checked")) {
                    $("#jan-modal-content"+id+" .checkallCate").prop("checked",true);
                    $fun.checkMsg();
                }
            }else{
                if (true==$("#jan-modal-content"+id+" .checkallCate").prop("checked")) {
                    $("#jan-modal-content"+id+" .checkallCate").prop("checked",false);
                    $fun.checkMsg();
                };
            };
        },
        chkMsgHdstable:function(data){
            var janNum=[];
            for (var i = 0; i < data.length; i++) {
                if (data[i].available == true) {
                    janNum.push(data[i]);
                };
            };
            $fun.janNumber(janNum);
        },
        handsontableLoadCate: function(data){
            $fun.handsontableDataCate = data.slice(0,data.length);
            $fun.handsontableDataTabCate = data.slice(0,data.length);
            //$fun.janNumber($fun.handsontableDataTabCate);
            $fun.chkMsgHdstable($fun.handsontableDataTabCate);
            $fun.chartdata = data;
            var datatableCate=document.getElementById('datatableCate'+id);
            $fun.handsontableCate = new Handsontable(datatableCate,{
                data : data,
                colHeaders: [ '<input id="checkCate'+id+'" class="checkallCate" type="checkbox" checked=true />', 'JAN', '商品名', '規格', 'メーカー', 'ブランド'],
                colWidths: [30, 100, 130, 80, 80, 80],
                startRows: 6,
                startCols: 6,
                minRows: 12,
                minCols: 6,
                maxCols: 6,
                readOnly: true,
                contextMenu: false,
                copyRowsLimit: 5000,
                columns : [
                    {
                        data: 'available',
                        type: 'checkbox',
					//	className: "htCenter"
                    },
                    {data: "jan"},
                    {data: "janname"},
                    {data: "kikaku"},
                    {data: "meka"},
                    {data: "burando"}
                ],
                cells: function (row, col, prop) {
                    var cellProperties = {};
                    if (col == 0) {
                        cellProperties.readOnly = false;
                        //cellProperties.className = 'htMiddle htCenter';
                    }
                    return cellProperties;
                },
                afterRender: function () {
                    $('#datatableCate'+id).find('td.htDimmed').each(function(index,element){
                        if((index+1)%5 == 2){
                            $(this).prop("title",$(this).text());
                        }
                    });
					//set checkbox show center
					for(var c in $('#datatableCate'+id).find('input')){
						$($('#datatableCate'+id).find('input')[c].parentNode).addClass("htCenter");
					};
                    $("#jan-modal-content"+id+" .checkallCate").on('click', function () {
                        $("#showMess"+id).show();
                        $('#datatableCate'+id).find('input.htCheckboxRendererInput').prop("checked", this.checked);
                        var dataArr = $fun.handsontableCate.getData();
                        if (dataArr.length==12) {
                            for(var i in dataArr){
                                if (dataArr[i].jan != null) {
                                    dataArr[i].available = this.checked;
                                };
                            }
                        } else{
                            for(var i in dataArr){
                                dataArr[i].available = this.checked;
                            }
                        };
                        $fun.handsontableCate.loadData(dataArr);
                        if (this.checked == true) {
                            $fun.checkMsg();
                        } else{
                            $fun.janNumber([]);
                        };
                        $('#datatableCate'+id).find('input.htCheckboxRendererInput.noValue').prop("checked", false);
                        $fun.checkallCate();
                    });
                    $('#datatableCate'+id+' input.htCheckboxRendererInput').on('click', function(event){
                        $("#showMess"+id).show();
                        $fun.checkallCate();
                    });
                    $fun.checkallCate();
                    $('#datatableCate'+id).find('input.htCheckboxRendererInput.noValue').prop("disabled",true);
                }
            });
            $fun.checkallCate();
        },
        favKeyInit:function(data){
            var tmp = data.slice(1,data.length);
            $fun.handsontableLoadCate(tmp);
        },
        loadToHandsontable: function(favname){
            $.ajax({
                type: 'POST',
                url: $favJanSearch,
                data:{
                    usercd:$fun.param.usercd ,
                    favname:favname
                },
                success: function(response){
                    $fun.hideOverlay();
                    if( response.length == 0){
                        alert("お気に入りロード失敗");
                    }else{
                        $fun.favKeyInit(response);
                    }
                },
                error : function(response){
                    $fun.hideOverlay();
                }
            });
        },
        favReCheck: function(e){/*お気に入りから呼び出す*/
            $fun.showOverlay();
            $fun.previewReturn();
            $fun.clearValue();
            $.ajax({
                type: 'POST',
                url: $favReadUrl,
                data: 'usercd=' + $fun.param.usercd + "&name=" + e.data.name,
                success: function(response){
                    if( response.replace("\n", "") == "failure"){
                        alert("お気に入りロード失敗");
                    }else{
                        $fun.loadToHandsontable(e.data.name);
                        $("#btnFavShow"+id).dropdown('toggle');
                        $fun.onInitialize();
                        $("#showMess"+id).show();
                    }
                }
            });
        },
        setJans:function(janlist){
            $fun.showOverlay();
            $fun.previewReturn();
            $fun.clearValue();
            $.ajax({
                type: 'POST',
                url: $setJanValueUrl,
                data: {
                    usercd:$fun.param.usercd,
                    janlist:janlist
                },
                success: function(response){
                    $fun.hideOverlay();
                    $fun.handsontableLoadCate(response);
                    $fun.onInitialize();
                    $fun.selectOK();
                    $("#showMess"+id).show();
                }
            });
        },
        init: function(obj){/*モジュール初期入口*/
            if(!obj.usercd || obj.usercd == ""){
                alert("ユーザーCDを指定してください");
                return;
            };
            $dialog.appendTo('body');
            if(!obj.level || obj.level == ""){
                obj.level = "1-1-1-1-1-1-1";
            };
            var startName, endName;
            var levelArray = obj.level.split("-");
            for (var i = 0; i < levelArray.length; i++){
                if(levelArray[i] == "1"){
                    $fun.firstLevel = $fun.flg[i];
                    startName = $fun.flgNm[i];
                    break;
                }
            };
            for (var i = (levelArray.length -1); i >= 0; i--){
                if(levelArray[i] == "1"){
                    endName = $fun.flgNm[i];
                    break;
                }
            };   
            $fun.param = obj;
            $fun.handsontableCate = [];
            $fun.chartdata = [];
            $fun.handsontableDataCate = [];
            $fun.handsontableDataTabCate = [];
            $fun.janDataCate = [];
            $fun.handsontableOkData = [];
            $fun.expandFirst = [];
            $fun.expandParam = [];
            $fun.checkParam  = [];
            $fun.selectedParamArray = [];
            $fun.selectedParam = [];
            $fun.favTabType = "";
            $("#searchKeyCate"+id).bind("click", $fun.searchKeyCate);
            $("#searchClear"+id).bind("click", $fun.clearSearch);
            $("#btnOK"+id).bind("click",$fun.selectOK);
            $("#treeOk"+id).bind("click",$fun.selectValue);
            $("#treeCancle"+id).bind("click",$fun.treeCancle);
            $("#btnClear" +id).bind("click", $fun.clearValue);
            $('ul.dropdown-menu').on('click', function(event){
                event.stopPropagation();
            });
            $("#InsertFav"+id).bind("click", $fun.favInsert);
            $("#lp"+id).on("click", $fun.preview);
            $("#categorySel"+id).bind("click", $fun.displayTree);
            $("#janArea"+id).bind("keypress", $fun.janInputcontrol);
            $("#inputKey"+id).bind("keypress", $fun.inputKey);
            $(document).click(function(e){
                if(e.target.id == ("jan-tree-module"+id) || e.target.id == ("menuContent"+id) || e.target.id == ("categorySel"+id)
                    || e.target.id.indexOf("jan-tree-module") != -1
                    || (e.target.parentElement && (e.target.parentElement.id == ('menuContent'+id)))
                    || (e.target.parentElement && (e.target.parentElement.id == ('jan-tree-module'+id)))
                    || (e.target.parentElement && (e.target.parentElement.id.indexOf("jan-tree-module") != -1))){
                }else{
                    $fun.hideTree();
                }
            });
            $("#btnFavShow"+id).mousedown(function(e){
                $fun.hideTree();
            });
            $fun.dialog().on("hidden.bs.modal",function(){
                $("#overlay"+id).hide();
            });
            $fun.dialog().on("shown.bs.modal",function(){
                if ($("#overlay"+id).css("display") != "none") {
                    $fun.showOverlay(); 
                };
                $fun.handsontableCate.render();
                if ($fun.handsontableCate.getData()[0].jan == "") {
                    $("#jan-modal-content"+id+" .checkallCate").prop("checked",false);
                    $("#jan-modal-content"+id+" .checkallCate").prop("disabled",true);
                    $('#datatableCate'+id).find('input.htCheckboxRendererInput').addClass("noValue");
                    $('#datatableCate'+id).find('input.htCheckboxRendererInput').prop("disabled",true)
                };
            });
            $fun.dialog().on("show.bs.modal",function(){
                $fun.handsontableCate.render();
                if ($fun.handsontableCate.getData()[0].jan == "") {
                    $("#jan-modal-content"+id+" .checkallCate").prop("checked",false);
                    $("#jan-modal-content"+id+" .checkallCate").prop("disabled",true);
                    $('#datatableCate'+id).find('input.htCheckboxRendererInput').addClass("noValue");
                    $('#datatableCate'+id).find('input.htCheckboxRendererInput').prop("disabled",true)
                };
            });
            $fun.dialog().on("hide.bs.modal",function(){
                if ($("#overlay"+id).css("display") != "none") {
                    return false;
                };
            });
            $("#jan-dialog-module"+id).scroll(function(){
                $fun.adjust();
            });
            $("#jan-dialog-module"+id).resize(function(){
                $fun.adjust();
            });
            $fun.overlayLoad();
            $fun.isCaptain();
            $fun.favLoad();
            $fun.treeLoad();
            $fun.datatableCateInit();
        },
        overlayLoad:function(){
            $fun.hideOverlay();
            $("#overlay"+id).html(
            '<div class="spinner">'+
              '<div class="spinner-container container11">'+
                '<div class="circle11"></div>'+
                '<div class="circle22"></div>'+
                '<div class="circle33"></div>'+
                '<div class="circle44"></div>'+
              '</div>'+
              '<div class="spinner-container container22">'+
                '<div class="circle11"></div>'+
                '<div class="circle22"></div>'+
                '<div class="circle33"></div>'+
                '<div class="circle44"></div>'+
              '</div>'+
              '<div class="spinner-container container33">'+
                '<div class="circle11"></div>'+
                '<div class="circle22"></div>'+
                '<div class="circle33"></div>'+
                '<div class="circle44"></div>'+
              '</div>'+
            '</div>');
        },
        janNumber:function(data){
            if (data.length == 1 && data[0].jan=="") {
                $("#jan_num"+id).text("0件");
            } else{
                $("#jan_num"+id).text(data.length+"件");
            };
        },
        isCaptain:function(){
            $.ajax({
                type:'POST',
                url: $isCaptain,
                data:{
                    usercd:$fun.param.usercd 
                },
                success: function(response){
                    if (response == "0") {
                        $("#privProdCate"+id).css("display","none");
                        $("#privProdCateTxt"+id).css("display","none");
                        $("#datatableCate"+id).css("margin-top","-15px");
                    };
                }
            });
        },
        treeCancle:function(){
            $fun.treeLoad();
            $fun.selectedParam = [];
            $("#categorySel"+id).val('商品選択');
        },
        treeLoad:function(){
            var setting = {
                check : { 
                    enable : true,
                    chkStyle: "checkbox",
                    autoCheckTrigger: true
                },
                view: {
                    nameIsHTML: true,
                    showTitle:false
                },
                data : {
                    simpleData : { enable : true }
                },
                async : {
                    enable : true,
                    dataType: "jsonp",
                    url : $treeUrl,
                    autoParam : [ "id", "name"],
                    otherParam: ["flg", id, 'usercd', $fun.param.usercd, 'level', $fun.param.level],
                    type : "get"
                },
                callback: {
                    onAsyncSuccess: $fun.onAsyncSuccess,
                    beforeClick : $fun.beforeClick,
                    onCheck : $fun.onCheck
                }
            };
            $.fn.zTree.init($("#jan-tree-module"+id), setting);
        },
        onCheck : function (event, treeId, treeNode){/*選択すると共に、データ保存*/
            $fun.selectedParam=[];
            $("#categorySel"+id).val('商品選択');
        },
        onInitialize: function(){
            $fun.janDataCate=[];
            $fun.handsontableOkData=[];
        },
        datatableCateInit:function(){
            var datatableCate=document.getElementById('datatableCate'+id);
            $fun.handsontableCate = new Handsontable(datatableCate,{
                colHeaders: [ '<input id="checkCate'+id+'" class="checkallCate" type="checkbox" disabled=true />', 'JAN', '商品名', '規格', 'メーカー', 'ブランド'],
                colWidths: [30, 100, 130, 80, 80, 80],
                minRows: 12,
                minCols: 6,
                maxCols: 6,
                readOnly: true
            });
        },
        inputKey: function(){
            return ((event.charCode != 13));
        },
        lastOkData:function(){
            if ($fun.handsontableOkData.length > 0) {
                var dataArr=[];
                for (var i = 0; i < $fun.handsontableOkData.length; i++) {
                    if (typeof($fun.handsontableOkData[i].jan)=="string" && $fun.handsontableOkData[i].jan!="") {
                        dataArr.push($.extend(true, {}, $fun.handsontableOkData[i]));
                    };
                };
                $fun.handsontableLoadCate(dataArr);
                $("#showMess"+id).hide();
            };
        },
        show: function(){
            $fun.overlayLoad();
            $fun.previewReturn();
            $dialog.modal("show");
            setTimeout($fun.lastOkData, 200);
        },
        hide: function(){
            $("#overlay"+id).hide();
            $dialog.modal('hide');
        },
        displayTree: function(){
            $("#menuContent"+id).css("display")=="none"?$fun.showTree():$fun.hideTree();
        },
        showTree: function() {
            $("#menuContent"+id).show();
        },
        hideTree: function() {
            $("#menuContent"+id).fadeOut("fast");
        },
        beforeClick : function(treeId, treeNode) {
            var zTree = $.fn.zTree.getZTreeObj("jan-tree-module" + id);
            zTree.checkNode(treeNode, !treeNode.checked, true, true);
            return false;
        },
        onAsyncSuccess: function(event, treeId, treeNode, msg){
            if(!treeNode) return;
            var child = treeNode.children;
            var zTree = $.fn.zTree.getZTreeObj("jan-tree-module"+id);
            for(var i = 0; i < child.length; i++){
                if($fun.isInExpandParam(child[i].id)){
                    if(child[i].zAsync){
                        if(child[i].isParent){
                            $fun.onAsync(child[i]);
                        }
                    }else{
                        if(child[i].isParent){
                            zTree.reAsyncChildNodes(child[i], "refresh", false);
                        }
                    }
                }
            }
            for(var i = 0; i < child.length; i++){
                if(treeNode.checked){
                    zTree.checkNode(child[i], true, true);
                }
            }
            for(var i = 0; i < child.length; i++){
                if($fun.isInCheckParam(child[i].id)){
                    zTree.checkNode(child[i], true, true);
                    zTree.selectNode(child[i], false);
                    zTree.cancelSelectedNode(child[i]);
                }
            }
        },
        getJanvalueCate : function(){
            var total = $fun.handsontableCate.countRows();
            var janArr = $fun.handsontableCate.getDataAtCol(0);
            if (janArr.join("_").indexOf("true") == -1) {return;};
            var janValueArr = $fun.handsontableCate.getDataAtCol(1);
            $fun.janDataCate=[];
            for (var i = 0; i < total; i++) {
                if (janArr[i] == true) {
                    $fun.janDataCate.push(janValueArr[i]);
                };
            };
            var janValueArr = $fun.handsontableCate.getData();
            $fun.handsontableOkData=[];
            var total = $fun.handsontableCate.getData().length;
            for (var i = 0; i < total; i++) {
                $fun.handsontableOkData.push($.extend(true, {}, janValueArr[i]));
            };
        },
        clearJans:function(){
            $fun.handsontableOkData=[];
            $fun.datatableCateInit();
            $fun.janNumber([]);
        },
        getJans:function(){
            return $fun.janDataCate;
        },
        previewReturn: function(){
            if($("#lp"+id+" span").html() == "戻る"){
                if ($fun.handsontableCate.getDataAtCol(0).join(",").indexOf("false") != -1 || $fun.handsontableCate.getDataAtCol(0).join(",").indexOf("true") != -1){
                    $fun.previewHideCate();
                }
                $("#pre" + id +" label").button('toggle');
            };
        },
        selectOK:function(){
            $fun.getJanvalueCate();
            $("#showMess"+id).hide();
            $fun.setButton();
            //$fun.handsontableOkData = $fun.handsontableCate.getData().slice(0,$fun.handsontableCate.getData().length);
            //$fun.handsontableOkData = $fun.handsontableCate.getData().concat();
        },
        clearValue : function(e){
            $fun.previewReturn();
            $fun.treeLoad();
            $fun.datatableCateInit();
            $("#inputKey" + id).val("");
            $("#privProdCate"+id).prop("checked",false);
            $("#janArea" + id).val("");
            $("#privProdJan"+id).prop("checked",false);
            $('#jan-modal-content'+id+' .checkallCate').prop("checked",false);
            //$fun.onInitialize();
            $("#showMess"+id).show();
            $fun.handsontableDataTabCate = [];
            $fun.selectedParam = [];
            $("#jan_num"+id).text("0件");
            $("#categorySel"+id).val('商品選択');
        },
        previewShowCate: function(){
            $("#lp"+id+" span").html("戻る");
            //$fun.selectValue();
            var total = $fun.handsontableCate.countRows();
            var janArr = $fun.handsontableCate.getDataAtCol(0);
            var dataArr = $fun.handsontableCate.getData();
            var num=0;
            for (var i = 0; i < total; i++) {
                if (janArr[i] != true) {
                    dataArr.splice(i-num,1);
                    num++;
                };
            };
            if (dataArr.length == 0) {
                dataArr = [{"available":"false","jan":"","janname":"","kikaku":"","meka":"","burando":""}];
                $fun.handsontableCate.loadData(dataArr);
                $("#jan-modal-content"+id+" .checkallCate").prop("disabled",true);
                $('#datatableCate'+id).find('input.htCheckboxRendererInput').addClass("noValue");
                $('#datatableCate'+id).find('input.htCheckboxRendererInput.noValue').prop("disabled",true);
            }else{
                $fun.handsontableCate.loadData(dataArr);
            };
            if ($fun.handsontableCate.getDataAtCol(0).join(",").indexOf("false") != -1 || $fun.handsontableCate.getDataAtCol(0).join(",").indexOf("true") == -1) {
                $("#jan-modal-content"+id+" .checkallCate").prop("checked",false);
            };
        },
        previewHideCate: function(){
            $("#lp"+id+" span").html("選択項目のみ表示");
            $fun.chartdata = $fun.handsontableDataCate.slice(0,$fun.handsontableDataCate.length);
            if ($fun.chartdata.length != 0) {
                $fun.handsontableLoadCate($fun.chartdata);
            };
            if ($fun.handsontableCate.getDataAtCol(0).join(",").indexOf("false") != -1 || $fun.handsontableCate.getDataAtCol(0).join(",").indexOf("true") == -1) {
                $("#jan-modal-content"+id+" .checkallCate").prop("checked",false);
            };
        },
        preview: function(e){
            if ($fun.handsontableCate.getDataAtCol(0).join(",").indexOf("false") != -1 || $fun.handsontableCate.getDataAtCol(0).join(",").indexOf("true") != -1) {
                $("#lp"+id+" span").html()!="選択項目のみ表示"?$fun.previewHideCate():$fun.previewShowCate();
            }else{
                $("#pre" + id +" label").button('toggle');
            };
        },
        favAdd: function(flg, tName){/*お気に入り追加、DBへ*/
            /*flg=0:重複判断が必須 flg=1:重複なし、既存をカバーする*/
            //var param = $fun.selectedParam.join(",");
            var total = $fun.handsontableCate.countRows();
            var janArr = $fun.handsontableCate.getDataAtCol(0);
            if (janArr.join("_").indexOf("true") == -1) {return;};
            var janValueArr = $fun.handsontableCate.getDataAtCol(1);
            var jans=[];
            for (var i = 0; i < total; i++) {
                if (janArr[i] == true) {
                    jans.push(janValueArr[i]);
                };
            };
            var param = jans.join(",");
            $.ajax({
                type:'POST',
                url: $favInsertUrl,
                data:'usercd=' + $fun.param.usercd + '&name=' + tName +"&flg=" + flg + "&param="+param+"&favTabType="+$fun.favTabType,
                success: function(response){
                    if(response.replace("\n", "") == "repeat"){
                        if(window.confirm("重複があるので、カバーしますか?")){
                            $fun.favAdd(1, tName);
                        }else{
                            return;
                        }
                    }else{
                        $("#txtFav" + id).val("");
                        $fun.favLoad();
                    }
                }
            });
        },
        favInsert: function(){/*お気に入り追加、画面入力判断*/
            var tName = $("#txtFav"+id).val();
            if (tName == ""){
                alert("お気に入り名が入力されていません");
                return;
            }
            //$fun.getJanvalueCate();
            var janArr = $fun.handsontableCate.getDataAtCol(0);
            if (janArr.join("_").indexOf("true") == -1) {
                alert("単品が選択されないので、保存できません");
                return;
            };
            /*if ($fun.janDataCate.length == 0) {
                alert("単品が選択されないので、保存できません");
                return;
            };*/
            //$fun.selectedParam = $fun.janDataCate.slice(0,$fun.janDataCate.length);
            $fun.favTabType = "cate";
            $fun.favAdd(0, tName);
        },
        isInExpandParam:function(tId){/*展開すべきのノードではないか*/
            var result = false;
            for(var i=0; i<$fun.expandParam.length; i++){
                if($fun.expandParam[i] == tId){
                    result = true;
                    break;
                }
            }
            return result;
        },
        isInCheckParam:function(tId){/*チェックを入れるべきのノードではないか*/
            var result = false;
            for(var i=0; i<$fun.checkParam.length; i++){
                if($fun.checkParam[i] == tId){
                    result = true;
                    break;
                }
            }
            return result;
        },
        getParent: function(node, pNum){/*ループにより、任意ノードからルートノードまでのパスを作成*/
            var sp = node.id.split('-');
            $fun.selectedParamArray[pNum][sp[1]] = sp[2];
            if(node.getParentNode() && (node.getParentNode().pId != null)){
                $fun.getParent(node.getParentNode(), pNum);
            }
        },
        selectValue : function(){/*現状のツリー上にチェックされた階層を取得する*/
            console.log("selectValue");
            var treeObj = $.fn.zTree.getZTreeObj("jan-tree-module"+id);
            nodes = treeObj.getCheckedNodes(true);
            var param="";
            $fun.selectedParamArray = [];
            $fun.selectedParam = [];
            var pNum = 0;
            for (var i = 0; i < nodes.length; i++){
            if(!nodes[i].getCheckStatus().half && nodes[i].getParentNode() && nodes[i].getParentNode().id == (id +"-0")){
                $fun.selectedParamArray[pNum] = {};
                $fun.getParent(nodes[i], pNum);
                pNum++;
            }else{
                if(!nodes[i].getCheckStatus().half && nodes[i].getParentNode() 
                 && nodes[i].getParentNode().getCheckStatus().half){
                    $fun.selectedParamArray[pNum] = {};
                    $fun.getParent(nodes[i], pNum);
                    pNum++;
                }
            }
            }
            for(var i = 0; i < $fun.selectedParamArray.length; i++){
                var pArray = $fun.selectedParamArray[i];
                var param = (pArray["d"]?pArray["d"]:"0") + "-"
                      + (pArray["l"]?pArray["l"]:"0") + "-"
                      + (pArray["b"]?pArray["b"]:"0") + "-"
                      + (pArray["c"]?pArray["c"]:"0") + "-"
                      + (pArray["sc"]?pArray["sc"]:"0") + "-"
                      + (pArray["sg"]?pArray["sg"]:"0") + "-"
                      + (pArray["ssg"]?pArray["ssg"]:"0");
                $fun.selectedParam.push(param);
            }
            if($fun.selectedParam.length > 0){
                $("#categorySel"+id).val('選択済み');
            }else{
                $("#categorySel"+id).val('商品選択');
            }
        },
        searchKeyCate: function(){
            var aKey = $("#inputKey" + id).val().toUpperCase();
            var aKey = $("#inputKey" + id).val().replace(/[\u3000]/g," ").replace(/(^\s*)|(\s*$)/g,"").replace(/_/g,"＿").replace(/[ ]/g,"_");
            var janlistVal = "";
            var janListTxt=$("#janArea"+id+"").val();
            var regex = /^[\d]+([\n\u3000\u0020]*$|((\n|,)[\d]+)*[\n\u3000\u0020]*$)/;
            if (regex.test(janListTxt.split("...\r\n")[0].replace(/[ ]/g,""))){
                var janlistVal = janListTxt.replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
            }
            if ($fun.selectedParam.length == 0 && aKey == "" && janlistVal == "" && $("#privProdCate"+id).prop("checked")==false) {
                alert("検索条件を入力してください");
                return false;
            };
            $fun.showOverlay();
            $fun.previewReturn();
            $.ajax({
                type:'POST',
                url: $searchKeyCateJanUrl,
                data:{
                    usercd:$fun.param.usercd ,
                    key:aKey,
                    jisya:$("#privProdCate"+id).prop("checked"),
                    cate:$fun.selectedParam.join("@"),
                    janlist:janlistVal
                },
                success: function(response){
                    $fun.hideOverlay();
                    $fun.handsontableLoadCate(response);
                    if (response[0].jan == "") {
                        $("#jan-modal-content"+id+" .checkallCate").prop("checked",false);
                        $("#jan-modal-content"+id+" .checkallCate").prop("disabled",true);
                        $('#datatableCate'+id).find('input.htCheckboxRendererInput').addClass("noValue");
                        $('#datatableCate'+id).find('input.htCheckboxRendererInput').prop("disabled",true)
                        $("#showMess"+id).hide();
                    }else{
                        $("#showMess"+id).show();
                    };
                    $fun.onInitialize();
                }
            });
        },
        setButton:function(){
            if($fun.janDataCate.length > 0){
                $("#"+$fun.param.elementId).val('選択済み');
            }else{
                $("#"+$fun.param.elementId).val('単品選択');
            }
        },
        destroy: function(){
            $("#jan-dialog-module" + id).remove();
        },
        showOverlay:function(){
            $("#overlay"+id).height($fun.pageHeight());
            $("#overlay"+id).width($fun.pageWidth());
            $fun.adjust();
            $("#overlay"+id).show();
        },
        hideOverlay:function(){
            $("#overlay"+id).hide();
        },
        pageHeight:function() {
            return $("#jan-modal-content"+id).css("height");
        },
        pageWidth:function(){
            return $("#jan-modal-content"+id).css("width");
        },
        adjust:function() {
            var X = $('#jan-modal-content'+id).offset().top; 
            var Y = $('#jan-modal-content'+id).offset().left; 
            //$("#overlay"+id).css({left: Y+'px', top: X+'px'});
        }
    };
    return $fun;
});
//(jQuery);