/**
 * センター自動発注_機能統合：発注案作成
 * @作成者: 10047496 zhangyunfeng
 * @作成日: 2019/04/01
 * 前提条件：Jquery,handsontableが必須
*/
var orderCaseDetail = (function ($) {
    var id=Math.floor(Math.random() * 1000);
    var $dialog = $(
    '<div id="ordercaseDetail-dialog-module'+id+'" class="modal fade" style="top: 40px;" tabindex="-1">' +
        '<div class="modal-dialog" style="width: 96%;" >' +
            '<div class="modal-content" id="ordercaseDetail-modal-content'+id+'">' +
                '<div class="modal-header" style="padding:10px 15px;">' +
                    '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '<p class="modal-title" style="display:inline-block;float:left;font-size: 16px;">案再作成</p>' +
                    '<p class="noteTitle" style="display:inline-block;">　※①混載区分を変更する場合、新混載区分だけ選択されても、旧混載区分も再推奨を行います。②20秒ごと進捗を自動更新します。</p>' +
                '</div>' +
                '<div class="modal-body" style="padding-top:5px;padding-bottom:5px;">' +
                    '<div class="row form-inline">' +
                        '<div class="row"  style="margin:0px 5px 5px 5px;">' +
                            '<div class="btn-group ordLblWidth"  role="group" style="margin-left: 8px;">' +
                                '<label>混載ID</label>' +
                            '</div>' +
                            '<div class="btn-group ordSelWidth"  role="group">' +
                                '<select id="mixcdMultiSel" size="5" multiple class="form-control"></select>' +
                            '</div>' +
                            '<div class="btn-group ordLblWidth"  role="group" style="margin-left: 8px;">' +
                                '<label>センター</label>' +
                            '</div>' +
                            '<div class="btn-group ordSelWidth"  role="group">' +
                                '<select id="centerMultiSel" size="5" multiple class="form-control"></select>' +
                            '</div>' +
                            '<div class="btn-group ordLblWidth"  role="group" style="margin-left: 8px;">' +
                                '<label>ベンダー</label>' +
                            '</div>' +
                            '<div class="btn-group ordSelWidth"  role="group">' +
                                '<select id="supplierMultiSel" size="5" multiple class="form-control"></select>' +
                            '</div>' +
                            '<div class="btn-group btnOrdWidth" style="margin-right:8px;">'+
                                '<input type="button" id="btnSearchCate'+id+'" class="btn btn-primary btn-block" value="検索"/>'+
                            '</div>'+
                            '<div class="btn-group btnOrdWidth">'+
                                '<input type="button" id="btnSaveCaluCate'+id+'" class="btn btn-primary btn-block" value="案再作成"/>'+
                            '</div>'+
                            '<div id="divCaluStatus" class="btn-group" style="background-color: #FFEFDB;margin-left:4px;display:none;">'+
                                '<label>推奨数再計算中</label>' +
                                '<img src="../Common/img/waiting.gif">'+
                            '</div>'+
                        '</div>' +
                    '</div>' +
                    '<div class="panel panel-default" style="margin-bottom:10px;">' +
                        '<div class="panel-body" style="padding: 0px; height: 432px; overflow: auto;">' +
                            '<div class="orderCaseTbhot" id="datatableCate'+id+'" style="height:430px;overflow:hidden;z-index:10;"></div>'+
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>' +
    '</div>'
    );
    
    var $fun = {
        handsontableCate : [],
        showOrderCaseList: [],
        allOrderCaseList: [],
        param : {},
        maxShowRowNum:0,
        isChkAll:false,
        isChkCol:false,
        savaFlg:false,
        interval:null,
        dialog: function(){
            return $dialog;
        },
        styleRender:function(instance, td, row, col, prop, value, cellProperties){
            var color;
            td.style.verticalAlign= 'middle';
            if("chkFlg"==prop){//checkbox
                Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
                td.style.textAlign = "center";
            }else{
                td.title = value;
        		Handsontable.renderers.TextRenderer.apply(this, arguments);
        	}
            if($fun.showOrderCaseList.length>0&&!isEmpty($fun.showOrderCaseList[row].calcuBeginTime)
                &&isEmpty($fun.showOrderCaseList[row].calcuEndTime)){
                if($fun.showOrderCaseList[row].calcuUserCD==RegistryGlobal.userCD||$fun.showOrderCaseList[row].calcuUserCD==RegistryGlobal.venderCD){
                    td.style.background = '#FFEFDB';
                    $("#divCaluStatus").show();
                }
            }
        },
        afterChg: function(changes, source){
            if (changes && changes[0].length) {
                for(var i=0; i<changes.length; i++){
                    rowidx = changes[i][0];
                    colName = changes[i][1];
                    preValue = changes[i][2];
                    newValue = changes[i][3];
                    var ordTableData = $fun.handsontableCate.getData();
                    var ordTableSourceData = $fun.handsontableCate.getSourceData();

                    if("chkFlg"==colName){
                        $fun.isChkCol =true;
                        $.each(ordTableData, function(j, data){
                            if(ordTableData[j][1] &&ordTableData[j][1]==ordTableData[rowidx][1] ){
                                if("0"==newValue||"1"==newValue){
                                    ordTableSourceData[j].chkFlg=newValue;
                                }else{
                                    $fun.handsontableCate.setDataAtRowProp(j,"chkFlg",preValue);
                                }
                            }
                        });
                        $fun.handsontableCate.render();
                    }
                }
            }
        },
        handsontableLoadCate: function(data){
            $fun.chartdata = data;
            var datatableCate=document.getElementById('datatableCate'+id);
            $fun.handsontableCate = new Handsontable(datatableCate,{
                data : data,
                colHeaders: function(col){
                    switch(col){
                    case 0:
                        var txt = '<label class="checkbox-inline"><input id="checkCate'+id+'" class="checkallCate" type="checkbox" ';
                            txt += $fun.isChecked() ? 'checked="checked"' : '';
                            txt += '/>全選</label>';
                        return txt;
                    case 1: return '混載区分';
                    case 2: return 'センター';
                    case 3: return 'ベンダー';
                    case 4: return '単品発注ロット';
                    case 5: return '混載発注ロット';
                    case 6: return '重量体積';
                    case 7: return '安全在庫日数';
					case 8: return 'LT';
                    case 9: return '継続区分';
                    case 10: return '発注区分';
                    case 11: return '参照JAN';
                    case 12: return '推奨方法';
                    case 13: return '基準消化日数';
                    case 14: return '最終修正者';
                    case 15: return '最終修正時間';
                    case 16: return '店舗配送設定';
                    case 17: return '売上参考期間';
                    case 18: return '進捗';
                    case 19: return '開始時間';
                    case 20: return '終了時間';
                    case 21: return 'ログID';
                    case 22: return '関連混載';
                    case 23: return '発注案ID';
                    case 24: return '発注ID';
                    case 25: return 'CaluUserCD';
                    }
                },
                colWidths: [52,75,120,120,70,70,65,70,60,60,65,60,60,70,110,72,72,72,60,65,65,0.01,0.01,0.01,0.01,0.01],
                minRows: 0,
                fillHandle:false,
                wordWrap:false,
                search: true ,
                columnSorting: false,
                manualColumnResize: true,
                currentRowClassName: 'currentRow',
                contextMenu: false,
                fixedColumnsLeft : 4,
                autoColumnSize:true,
                columns : [
                    {
                        data: 'chkFlg',
                        type: 'checkbox',
                        checkedTemplate: '1', uncheckedTemplate: '0'
                    },
                    {data: 'mixCD',type: 'text',      readOnly:true},
                    {data: 'storeHouse',type: 'text',      readOnly:true},
                    {data: 'supplier',type: 'text',      readOnly:true},
                    {data: 'orderUnit',type: 'text',      readOnly:true},
                    {data: 'lotUnit',type: 'text',  readOnly:true},
                    {data: 'caseWM',type: 'text',      readOnly:true},
                    {data: 'orderSaftyTime',	    type: 'text',     	  readOnly:true},
            	    {data: 'lT',		type: 'text',     readOnly:true},
                    {data: 'continueType',type: 'text',readOnly:true},
                    {data: 'orderStopType',type: 'text',   readOnly:true},
                    {data: 'consultJAN',type: 'text',      readOnly:true},
                    {data: 'orderType',type: 'text',      readOnly:true},
                    {data: 'standardSaleDays',type: 'text',      readOnly:true},
                    {data: 'maintainer',type: 'text',      readOnly:true},
                    {data: 'modified',type: 'text',      readOnly:true},
                    {data: 'centerBranch',type: 'text',      readOnly:true},
                    {data: 'salePeriod',type: 'text',      readOnly:true},
                    {data: 'calcuStatus',type: 'text',      readOnly:true},
                    {data: 'calcuBeginTime',type: 'text',      readOnly:true},
                    {data: 'calcuEndTime',type: 'text',      readOnly:true},
                    {data: 'logID',type: 'text',      readOnly:true},
                    {data: 'refMixCD',type: 'text',      readOnly:true},
                    {data: 'orderCaseID',type: 'text',      readOnly:true},
                    {data: 'orderID',type: 'text',      readOnly:true},
                    {data: 'calcuUserCD',type: 'text',      readOnly:true}
                ],
                cells: function (row, col, prop) {
                    var cellProperties = {};
                    cellProperties.renderer = $fun.styleRender;
                    return cellProperties;
                },
                afterChange : function(changes, source) {
                    $fun.afterChg(changes, source);
                },
                afterRender: function () {
                    for(var c in $('#datatableCate'+id).find('input')){
                        $($('#datatableCate'+id).find('input')[c].parentNode).addClass("htCenter");
                    };
                }
            });
            $("#datatableCate"+id).on('click', 'input.checkallCate',$fun.chkOrderCaseAll);
        },
        chkOrderCaseAll:function () {
            if($fun.isChkAll==true){
                $fun.isChkAll = false;
            }else{
                $fun.isChkAll = true;
            }
            var dataArr =$fun.handsontableCate.getSourceData();
            for (var i = 0, ilen = dataArr.length; i < ilen; i++) {
                dataArr[i].chkFlg = $fun.isChkAll?1:0;
            }
            $fun.handsontableCate.render();
        },
        isChecked: function(){
            var dataArr = [];
            if(!$fun.showOrderCaseList||$fun.showOrderCaseList.length==0){
                return false;
            }//init uncheck
            if($fun.isChkCol){
                dataArr = $fun.handsontableCate.getSourceData();
                for (var i = 0, ilen = dataArr.length; i < ilen; i++) {
                    if (!dataArr[i].chkFlg||dataArr[i].chkFlg=="0") {
                        $fun.isChkAll = false;
                        return false;
                    }
                }
                $fun.isChkAll = true;
                return true;
            }
            else{			
                return $fun.isChkAll;
            }
        },
        init: function(obj){/*モジュール初期入口*/
            $dialog.appendTo('body');
            $fun.param = obj;
            $fun.handsontableCate = [];
            $fun.chartdata = [];
            $fun.allOrderCaseList=[];
            $fun.showOrderCaseList=[];
            $("#btnSearchCate"+id).on("click",$fun.GetKeyCate);
            $("#btnSaveCaluCate"+id).on("click",$fun.SaveKeyCate);
            $fun.dialog().on("hidden.bs.modal",function(){
                $fun.destroy();
            });
            $fun.dialog().on("shown.bs.modal",function(){
                $(document).off('focusin.bs.modal');
                $fun.handsontableCate.render();

            });
            $fun.dialog().on("show.bs.modal",function(){
                $fun.handsontableCate.render();
            });
            $fun.dialog().on("hide.bs.modal",function(){
            });
           
            $("#ordercaseDetail-dialog-module"+id).scroll(function(){
                $fun.adjust();
            });
            $("#ordercaseDetail-dialog-module"+id).resize(function(){
                $fun.adjust();
            });
            $fun.handsontableLoadCate([]);
            $('#centerMultiSel').multiselect({
                buttonContainer: '<div class="dropdown"/>',
                buttonClass:'btn btn-default btn-block selover',
                maxHeight:200,
                enableFiltering: true,
                enableCaseInsensitiveFiltering: true,
                filterBehavior: 'both',
                filterPlaceholder: '番号、名前',
                includeSelectAllOption: true,
                selectAllText: '全て',
                allSelectedText: '全センター',
                nonSelectedText: '全て',
                nSelectedText: ' センター',
                numberDisplayed:1
            });
            //混載
            $('#mixcdMultiSel').multiselect({
                buttonContainer: '<div class="dropdown"/>',
                buttonClass:'btn btn-default btn-block selover',
                maxHeight:200,
                enableFiltering: true,
                enableCaseInsensitiveFiltering: true,
                filterBehavior: 'both',
                filterPlaceholder: '混載区分',
                includeSelectAllOption: true,
                selectAllText: '全て',
                allSelectedText: '全混載',
                nonSelectedText: '全て',
                nSelectedText: ' 混載',
                numberDisplayed:1
            });

            //ベンダー
            $('#supplierMultiSel').multiselect({
                buttonContainer: '<div class="dropdown"/>',
                buttonClass:'btn btn-default btn-block selover',
                maxHeight:200,
                enableFiltering: true,
                enableCaseInsensitiveFiltering: true,
                filterBehavior: 'both',
                filterPlaceholder: 'ベンダー',
                includeSelectAllOption: true,
                selectAllText: '全て',
                allSelectedText: '全ベンダー',
                nonSelectedText: '全て',
                nSelectedText: 'ベンダー',
                numberDisplayed:1
            });

            //混載
            bindData($fun.param.mixList,"mixCD","","mixcdMultiSel","");
            //ベンダー
            bindData($fun.param.centerList,"storeHouseCD","storeHouseName","centerMultiSel","");
            //ベンダー
            bindData(RegistryGlobal.supplierList,"supplierCD","supplierName","supplierMultiSel","");
        },
        show: function(){
            $dialog.modal({backdrop: 'static', keyboard: false});
            $fun.savaFlg=false;
            $fun.GetKeyCate();
            $("#datatableCate"+id).on('click', $fun.clearFresh);
        },
        hide: function(){
            $dialog.modal('hide');
        },
        GetKeyCate: function(obj){//初期化
            $("#loadMask").show();
            $fun.allOrderCaseList=[];
            $fun.showOrderCaseList=[];
            var centers = $("#centerMultiSel").val()==null?"":$("#centerMultiSel").val().join(",");
            var mixCDs = $("#mixcdMultiSel").val()==null?"":$("#mixcdMultiSel").val().join(",");
            var suppliers = $("#supplierMultiSel").val()==null?"":$("#supplierMultiSel").val().join(",");
            $("#divCaluStatus").hide();
            //案再作成の状態リスト取得
            $.ajax({
                url:SystemManage.address+'SupportPageControl/getOrderCaseCalcuList',
                type:'POST',
                data:{
                    userCD:RegistryGlobal.userCD,
                    suppliers:isEmpty(suppliers)?RegistryGlobal.venderCD:suppliers,
                    centers:centers,
                    mixCDs:mixCDs
                },
                success:function(response){
                    $("#loadMask").hide();
                    if("false"==response){
                        showMsgBox(Message.msg002,[],function(){});
                    }else{
                        $fun.showOrderCaseList = JSON.parse(response);
                        if($fun.showOrderCaseList.length>0){
                            $fun.handsontableCate.loadData($fun.showOrderCaseList);
                            $fun.handsontableCate.render();
                        }else{
                            $fun.handsontableCate.loadData([]);
                            $fun.handsontableCate.render();
                            showMsgBox(Message.msg003,[],function(){});
                        }

                    }
                },
                error:function(response, textStatus){
                    $("#loadMask").hide();
                    showMsgBox(Message.msg002,[],function(){});
                }
            });
        },
        SaveKeyCate: function(){//確定
            var tableData = $fun.handsontableCate.getSourceData();
            var mixsData = "";
            var cnt = 0;

            if(tableData.length==0){
                showMsgBox(Message.msg003,[],function(){});
                return;
            }
            for(var i=0;i<tableData.length;i++){
                if("1"==tableData[i].chkFlg){
                    cnt++;
                    if(!isEmpty(tableData[i].mixCD)){
                        mixsData += tableData[i].mixCD+"@";
                    }else{
                        mixsData += "@";
                    }
                    if(!isEmpty(tableData[i].orderCaseID)){
                        mixsData += tableData[i].orderCaseID+";";
                    }else{
                        mixsData += ";";
                    }
                }
            }
            //選択なし
            if(0==cnt){
                showMsgBox(Message.msg004,[],function(){});
                return;
            }
            $fun.clearFresh();
            $("#loadMask").show();
            $.ajax({
                url:SystemManage.address+'SupportPageControl/checkOrderCase',
                type:'POST',
                dataType:"TEXT",
                data:{
                    userCD:RegistryGlobal.userCD,
                    venderCD:RegistryGlobal.venderCD,
                    mixsData:mixsData
                },
                success:function(response){
                    $("#loadMask").hide();
                    if("false"==response){
                        showMsgBox(Message.msg043,["案再作成"],function(){});
                        return;
                    }else if(response =="order"){
                        showConfMsg(Message.msg060, [], function() {$fun.saveData(mixsData);}, function() {
                            return;
                        });
                    }else{
                        $fun.saveData(mixsData);
                    }

                },
                error:function(response, textStatus){
                    $("#loadMask").hide();
                    $fun.savaFlg=false;
                    showMsgBox(Message.msg043,["案再作成"],function(){});
                }
            });
        },
        saveData: function(mixsData){
            $("#loadMask").show();
            $("#divCaluStatus").show();
            $.ajax({
                url:SystemManage.address+'SupportPageControl/saveOrderCaseCalcu',
                type:'POST',
                dataType:"TEXT",
                data:{
                    userCD:RegistryGlobal.userCD,
                    venderCD:RegistryGlobal.venderCD,
                    mixsData:mixsData
                },
                beforeSend:function(){
                    $fun.savaFlg=true;
                    $fun.refreshOnTime();
                },
                success:function(response){
                    $("#loadMask").hide();
                    $fun.savaFlg=false;
                    if("false"==response){
                        showMsgBox(Message.msg043,["案再作成"],function(){$fun.GetKeyCate();});
                        return;
                    }else if(response =="wait"){
                        showMsgBox(Message.msg058,[],function(){});
                        return;
                    }
                    $fun.GetKeyCate();
                },
                error:function(response, textStatus){
                    $("#loadMask").hide();
                    $fun.savaFlg=false;
                    showMsgBox(Message.msg043,["案再作成"],function(){$fun.GetKeyCate();});
                }
            });
        },
        refreshOnTime:function(){
            if($fun.savaFlg){
                if( $fun.interval!=null){
                    clearInterval( $fun.interval);
                    $fun.interval=null;
                }
                setTimeout(function() {
                    $fun.GetKeyCate();
                }, 3000);//3秒WAIT
                $fun.interval=setInterval($fun.refreshOnTime, 17000);//15秒/次
            }else{
                $fun.clearFresh();
            }
        },
        clearFresh:function(){
            if( $fun.interval!=null){
                clearInterval( $fun.interval);
                $fun.interval=null;
            }
        },
        destroy: function(){
            $("#ordercaseDetail-dialog-module" + id).remove();
        },
        pageHeight:function() {
            return $("#ordercaseDetail-modal-content"+id).css("height");
        },
        pageWidth:function(){
            return $("#ordercaseDetail-modal-content"+id).css("width");
        },
        adjust:function() {
            if( $('#ordercaseDetail-modal-content'+id).size()){
                var X = $('#ordercaseDetail-modal-content'+id).offset().top;
                var Y = $('#ordercaseDetail-modal-content'+id).offset().left;
            }
        }
    };
    return $fun;
});