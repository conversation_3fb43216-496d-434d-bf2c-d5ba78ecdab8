.handsontable{
    font-size: 11px;
    overflow: hidden;
    text-overflow:ellipsis;
}
.handsontable td{
/*     height: 25px; */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
/*.handsontable td:hover{
    overflow:visible;
    text-overflow:inherit;
    color: #000;
    font-weight:bold;
}*/
.menuContent{
    -moz-box-shadow: 2px 2px 10px #909090;
    -webkit-box-shadow: 2px 2px 10px #909090;
    box-shadow:2px 2px 10px #909090;
    border-radius: 4px;
}
.overlay {
    background: #CCC;
    filter: alpha(opacity=50);
    opacity: 0.5; 
    display: none;
    /*position: absolute;*/
    position: fixed; 
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    z-index: 9999; 
    border-radius: 6px;
}
.spinner {
  margin: 250px auto;
  width: 60px;
  height: 60px;
  position: relative;
}
 
.container11 > div, .container22 > div, .container33 > div {
  width: 6px;
  height: 6px;
  background-color: #333;
 
  border-radius: 100%;
  position: absolute;
  -webkit-animation: bouncedelay 1.2s infinite ease-in-out;
  animation: bouncedelay 1.2s infinite ease-in-out;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
 
.spinner .spinner-container {
  position: absolute;
  width: 100%;
  height: 100%;
}
 
.container22 {
  -webkit-transform: rotateZ(45deg);
  transform: rotateZ(45deg);
}
 
.container33 {
  -webkit-transform: rotateZ(90deg);
  transform: rotateZ(90deg);
}
 
.circle11 { top: 0; left: 0; }
.circle22 { top: 0; right: 0; }
.circle33 { right: 0; bottom: 0; }
.circle44 { left: 0; bottom: 0; }
 
.container22 .circle11 {
  -webkit-animation-delay: -1.1s;
  animation-delay: -1.1s;
}
 
.container33 .circle11 {
  -webkit-animation-delay: -1.0s;
  animation-delay: -1.0s;
}
 
.container11 .circle22 {
  -webkit-animation-delay: -0.9s;
  animation-delay: -0.9s;
}
 
.container22 .circle22 {
  -webkit-animation-delay: -0.8s;
  animation-delay: -0.8s;
}
 
.container33 .circle22 {
  -webkit-animation-delay: -0.7s;
  animation-delay: -0.7s;
}
 
.container11 .circle33 {
  -webkit-animation-delay: -0.6s;
  animation-delay: -0.6s;
}
 
.container22 .circle33 {
  -webkit-animation-delay: -0.5s;
  animation-delay: -0.5s;
}
 
.container33 .circle33 {
  -webkit-animation-delay: -0.4s;
  animation-delay: -0.4s;
}
 
.container11 .circle44 {
  -webkit-animation-delay: -0.3s;
  animation-delay: -0.3s;
}
 
.container22 .circle44 {
  -webkit-animation-delay: -0.2s;
  animation-delay: -0.2s;
}
 
.container33 .circle44 {
  -webkit-animation-delay: -0.1s;
  animation-delay: -0.1s;
}
 
@-webkit-keyframes bouncedelay {
  0%, 80%, 100% { -webkit-transform: scale(0.0) }
  40% { -webkit-transform: scale(1.0) }
}
 
@keyframes bouncedelay {
  0%, 80%, 100% {
    transform: scale(0.0);
    -webkit-transform: scale(0.0);
  } 40% {
    transform: scale(1.0);
    -webkit-transform: scale(1.0);
  }
}