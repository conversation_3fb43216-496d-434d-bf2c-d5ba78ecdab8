var RegistryGlobal = {
	supplierList: []
	,appnoList:[]
	,authorityList: []
}

/** DataGrid Creat*/
var TemplateGrid = {
	tbhot:null,
	colWidths:[60, 200, 200, 100, 150, 100],
	columns: [
        {data: 'ChkFlg',    		type: 'checkbox',    checkedTemplate: '1', uncheckedTemplate: '0'},//全選 
        {data: 'SupplierCD',		editor: "chosen",
        	chosenOptions: {
//            	data: RegistryGlobal.supplierList
            }
        },
        {data: 'APPNO', 			editor: "chosen",
        	chosenOptions: {}
        },
        {data: 'SupAppAuthority', 	editor: "chosen",
        	chosenOptions: {}
        },
        {data: 'Modifier', 			type: 'text',        readOnly:true},
        {data: 'ModifiedTime', 		type: 'text',        readOnly:true},
	],
	isChecked: function(){
		if(!this.tbhot){
			return false;
		}
		if("afterChg"==chk){
			var sourceData = TemplateGrid.tbhot.getSourceData();
			for (var i=0; i<sourceData.length-1; i++) {
				if(sourceData[i].ChkFlg!="1"){						
					return false;
				}
			}
			return true;
		}else{
			return chk;
		}
	},
	//create grid
	CreatHotGrid: function(hotgrid, arrData){
		//まず対象内容クリア
		if(this.tbhot){
			this.tbhot.destroy();
			chk=false;
		}
		this.tbhot = new Handsontable(document.getElementById(hotgrid),{
			height:$(".page-content").height()-150,
			rowHeaders: false,
			colHeaders: function(col){
							switch(col){
								case 0:
									var txt = '<label class="checkbox-inline"><input id="chkAll" onclick="chkAll();" type="checkbox"';
										txt += TemplateGrid.isChecked() ? 'checked="checked"' : '';
										txt += '/>全選</label>';
									return txt;
								case 1: return 'ベンダー';
								case 2: return '画面区分';
								case 3: return '権限';
								case 4: return '最終更新者';
								case 5: return '最終更新日';
							}
						},
			colWidths: this.colWidths,
			columns: this.columns,
			overflow: scroll,
		    fillHandle:false,
		    wordWrap:false,
		    search: true,
		    minSpareRows:1,
		    columnSorting: true,
		    sortIndicator: true,
		    manualColumnResize: true,
			currentRowClassName: 'currentRow',
			cells: function (row, col, prop) {
				var cellProperties = {};
				cellProperties.renderer = styleRender;
				return cellProperties;					
	    	},
	    	beforeChange : function(changes, source){
	    	},
	    	afterChange: function(changes, source) {
	    		//自動選択
	    		dataChecked(changes, source);
 			},
 			afterSelectionByProp: function(r, p, r2, p2) {
 			}
		});	
		chk=false;
		this.tbhot.loadData(arrData);
	    this.tbhot.render();
	}
}

var chk;
var chkAll = function(){
	if(chk==true){
		chk = false;
	}else{
		chk = true;
	}
	
	var tableDataList = TemplateGrid.tbhot.getSourceData();
	if(tableDataList.length>0){		
		$.each(tableDataList, function(i, data){
			if(!data.ChkFlg)
			{
				return false;
			}
			data.ChkFlg = chk?"1":"0";
		});
	}
	TemplateGrid.tbhot.render();
}

var styleRender=function(instance, td, row, col, prop, value, cellProperties){
	Handsontable.renderers.TextRenderer.apply(this, arguments);
	var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', row);
	
	if("ChkFlg"==prop){//checkbox
		td.style.textAlign = "center";
		Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
	}
	else if("SupplierCD" == prop){
		td.style.backgroundColor = "#FFFF99";
		cellProperties.chosenOptions.data = RegistryGlobal.supplierList;
		singleDropdown(instance, td, row, col, prop, value, cellProperties);
	}
	else if("APPNO" == prop){
		td.style.backgroundColor = "#FFFF99";
		cellProperties.chosenOptions.data = RegistryGlobal.appnoList;
		singleDropdown(instance, td, row, col, prop, value, cellProperties);
	}
	else if("SupAppAuthority" == prop){
		td.style.backgroundColor = "#FFFF99";
		cellProperties.chosenOptions.data = RegistryGlobal.authorityList;
		singleDropdown(instance, td, row, col, prop, value, cellProperties);
	}
	else if("ModifiedTime"==prop){
		td.style.textAlign = "center";
	}
}

//自動選択
var dataChecked = function(changes, source){
	if (source === 'loadData'||source ==='external') {
        return; //don't save this change
    }
	if(changes&& changes!=null){
		changes.forEach(function(change, index){
			var rowIndex = change[0];//行号码
	        var colHead = change[1];//列
	        var preValue = change[2];
	        var newValue = change[3];
	        var sourceData = TemplateGrid.tbhot.getSourceData();
	        var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', rowIndex);
	        if(typeof(colHead)=="undefined"
	        	||((typeof(preValue)=="undefined"||preValue==""|| preValue==null)&& (typeof(newValue)=="undefined"||newValue==""|| newValue==null))
	            || newValue==preValue) {
	        }else{
		        if("ChkFlg"==colHead){
					chk = "afterChg";
					if("0"==newValue||"1"==newValue){
						sourceData[realRow].ChkFlg=newValue;
					}else{
						return false;
					}
				}else if(newValue==preValue) {
					return false;
				}else{
					sourceData[realRow].ChkFlg="1";
				};
	        }
	    });
		TemplateGrid.tbhot.render();
	}
}

var singleDropdown = function(instance, td, row, col, prop, value, cellProperties){
	if(value == null){
		Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
	}else{
		var selectedId;
	    var optionsList = cellProperties.chosenOptions.data;
	    if(typeof optionsList === "undefined" || typeof optionsList.length === "undefined" || !optionsList.length) {
	        Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
	        return td;
	    }
	    var values = (value + "").split(",");
	    var value = [];
	    for (var index = 0; index < optionsList.length; index++) {
	        if (values.indexOf(optionsList[index].id + "") > -1) {
	            selectedId = optionsList[index].id;
	            value.push(optionsList[index].label);
	        }
	    }
	    value = value.join(", ");
	    Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
	    if(TemplateGrid.tbhot!=null && value==""){
	    	TemplateGrid.tbhot.setDataAtCell(row, col, null);
		}
	}
	return td;
}