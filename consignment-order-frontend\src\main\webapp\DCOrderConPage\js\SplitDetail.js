/**
* 発注推奨案伝票分け結果一覧
* 前提条件：Jquery,handsontableが必須

 * @description:1）3種類改修、前者の数値変更したら、後者も合わせて、変わるうん
 * ①ロット数　（　ケース数　→　推奨数　一緒に変わる）
 * ②ケース数　（　推奨数　一緒に変わる　）
 * ③推奨数
 * 2）発注済み、再作成案判断追加
 * @修正者: 10047496 zhangyunfeng
 * @修正日: 2019/04/26
*/
var splitDetail = (function ($) {
    var id=Math.floor(Math.random() * 1000);
    var $dialog = $(
    '<div id="splitDetail-dialog-module'+id+'" class="modal fade" style="top: 40px;" tabindex="-1">' +
        '<div class="modal-dialog" style="width: 870px;" >' +
            '<div class="modal-content" id="splitDetail-modal-content'+id+'">' +
                '<div class="modal-header" style="padding:10px 15px;">' +
                    '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '<h4 class="modal-title" style="display:inline-block;">発注推奨案伝票分け結果一覧</h4>' +
                '</div>' +
                '<div class="modal-body" style="padding-top:5px;padding-bottom:5px;">' +
                    '<div class="row form-inline">' +
                        '<div class="row"  style="margin:0px 12px 0px 12px;">' +
                            '<div class="col-sm-offset-10" style="margin-right:35px;">'+
                                '<input type="button" id="btnSaveKeyCate'+id+'" class="btn btn-primary btn-block" value="確定"/>'+
                            '</div>'+
                        '</div>' +
                    '</div>' +
                    '<div class="panel panel-default" style="margin-bottom:10px;">' +
                        '<div class="panel-body" style="padding: 0px; height: 330px; overflow: auto;">' +
                            '<div id="datatableCate'+id+'" style="height:330px;overflow:hidden;z-index:10;"></div>'+
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>' +
    '</div>'
    );
    
    var $fun = {
        handsontableCate : [],
        handsontableDataCate : [],
        showSplitList: [],
        allSplitList: [],
        NotDeliveryList: [],
        showNewRow:[],
        param : {},
        maxShowRowNum:0,
        isChkAll:true,
        isChkCol:false,
        saveLotQty:0,
        saveOrderQty:0,
        saveDeliveryDate:"",
        dayWeeks : ['日','月','火','水','木','金','土'],
        isDateSame:false,
        dialog: function(){
            return $dialog;
        },
        approvalRender:function(instance, td, row, col, prop, value, cellProperties){
            if("ChkFlg"==prop){//checkbox
                Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
            }//else if("SplitNO"==prop||"OrderPlanQtyUpdate"==prop || "DeliveryPlanDateUpdate"==prop){//推奨数 || 納品予定日
            else if("SplitNO"==prop||"CSlot"==prop||prop=="CaseQty" || prop=="OrderPlanQtyUpdate"  || "DeliveryPlanDateUpdate"==prop){//ロット数 || 納品予定日 2018/08/17 zhangyunfeng
                Handsontable.renderers.NumericRenderer.apply(this, arguments);
                td.style.textAlign = "right";
            }
            else{
                Handsontable.renderers.TextRenderer.apply(this, arguments);
            }
            cellProperties.readOnly = true;
            td.style.backgroundColor = "#EEEEEE";
        },
        styleRender:function(instance, td, row, col, prop, value, cellProperties){
            var color;
            if("ChkFlg"==prop){//checkbox
                if("0"!=value && "1"!=value){
                    td.style.textAlign = "center";
                    return;
                }
                Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
                td.style.textAlign = "center";
            }else if("CSlot"==prop||"CaseQty"==prop ||"OrderPlanQtyUpdate"==prop || "DeliveryPlanDateUpdate"==prop){//ロット数 || 推奨数 || 納品予定日 2018/08/17 zhangyunfeng begin
                td.style.textAlign = "right";
                if("CSlot"==prop|| "CaseQty"==prop){//推奨数
                    Handsontable.renderers.NumericRenderer.apply(this, arguments);
                    td.style.backgroundColor = '#FFFF99';
                }else if("OrderPlanQtyUpdate"==prop){//推奨数
	                Handsontable.renderers.NumericRenderer.apply(this, arguments);
	                td.style.backgroundColor = '#FFFF99';
	                if(value&&parseFloat(value)>=100000){
	                     td.style.backgroundColor = "red";
	                     td.title = Message.msg027;
	                 }else if(value&&$fun.showSplitList[row].ProLotQtyCheck &&
	                      parseInt($fun.showSplitList[row].ProLotQtyCheck)!=0 &&
	                      parseFloat(value)% parseInt($fun.showSplitList[row].ProLotQtyCheck) != 0){
	                    td.style.backgroundColor = '#99FFFF';
	                    td.title = Message.msg008.replace("$1",parseInt($fun.showSplitList[row].ProLotQtyCheck));
	                }else{
	                    td.title = "";
	                }
                }else if("DeliveryPlanDateUpdate"==prop){
                    Handsontable.renderers.TextRenderer.apply(this, arguments);
                    if(!value||isDate(convertToCorrectDate(value))){				
                        $(arguments[1]).removeClass("htInvalid");
                    }
                    if(value && !isDate(value)){
        				value = "";
        			}else if(value && 
                        (
                       //10105066 start
//                        new Date(new Date(value).toLocaleDateString())<
//                           new Date(new Date(RegistryGlobal.OrderCaseCreateDate).toLocaleDateString()) ||
//                        new Date(new Date(value).toLocaleDateString())>=
//                           new Date(
//                               new Date(
//                                   Number(new Date(RegistryGlobal.OrderCaseCreateDate).getFullYear()+1)+"/"+
//                                   Number(new Date(RegistryGlobal.OrderCaseCreateDate).getMonth()+1)+"/"+
//                                   new Date(RegistryGlobal.OrderCaseCreateDate).getDate()
//                               ).toLocaleDateString()
//                           )
                		new Date(new Date(value))<
                 		   new Date(new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10))) ||  //new Date(str)方法，str格式影响其在IE浏览器上的解析
                 		new Date(new Date(value))>=
                 		   new Date(
                 			   new Date(
                 				   Number(new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10)).getFullYear()+1)+"/"+
                 				   Number(new Date(RegistryGlobal.OrderCaseCreateDate).getMonth()+1)+"/"+
                 				   new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10)).getDate()
                 		       )
                 		   )
                 		// 10105066 end
                        )
                    ){
                        color = "#EEEEEE";
                    }else if(0==new Date(value).getDay()){
                        color = "red";
                    }else{
                        for(var i=0; i<$fun.NotDeliveryList.length; i++){
                            if(
                                (value &&
                                 $fun.NotDeliveryList[i].NotDeliveryWeekDay &&
                                 //10105066 start
                                 //$fun.NotDeliveryList[i].NotDeliveryWeekDay.includes($fun.dayWeeks[new Date(value).getDay()])
                                 $fun.NotDeliveryList[i].NotDeliveryWeekDay.indexOf($fun.dayWeeks[new Date(value).getDay()]) > -1//includes()方法对IE浏览器不兼容，修改为indexOf()方法，该方法兼容IE9及其以上版本
                                 //10105066 end
                                )||
                                (
                                new Date(value)>=new Date($fun.NotDeliveryList[i].NotDeliveryBeginDate) &&
                                new Date(value)<=new Date($fun.NotDeliveryList[i].NotDeliveryEndDate)
                                )
                            ){
                                color = "red";
                                break;
                            }
                        }
                    }
                    
                    if(!color){        				
                        color = '#FFFF99';
                    }
                    
                    td.style.backgroundColor = color;
                }
            }else if("SplitNO"==prop)
            {
                Handsontable.renderers.NumericRenderer.apply(this, arguments);
                td.style.textAlign = "right";
                if($fun.showSplitList[row] && "1"==$fun.showSplitList[row].AutoSplitFlg ){//自動分け
                    cellProperties.readOnly = false;
                    td.style.backgroundColor = '#FFFF99';
                }
            }else{
        		Handsontable.renderers.TextRenderer.apply(this, arguments);
        	}
        },
        afterChg: function(changes, source){
            if (changes && changes.length) {
                for(var i=0; i<changes.length; i++){
                    rowidx = changes[i][0];
                    colName = changes[i][1];
                    preValue = changes[i][2];
                    newValue = changes[i][3];
                    if((typeof(preValue)=="undefined"||preValue==""|| preValue==null)&& (typeof(newValue)=="undefined"||newValue==""|| newValue==null)) {
                    }else {
                    	if("ChkFlg"==colName){
                            $fun.isChkCol =true;
                            if("0"!=newValue && "1"!=newValue){
                                $fun.handsontableCate.setDataAtRowProp(rowidx,"ChkFlg",preValue);
                            }
                            $fun.handsontableCate.render();
                        }
                        else if("SplitNO"==colName && preValue!=newValue){//伝票NO
                            var dataArr = $fun.handsontableCate.getSourceData();
                            if(dataArr.length-1==rowidx)
                            {
                                dataArr.push(jQuery.extend({}, $fun.showNewRow[0]));
                                $fun.handsontableCate.loadData(dataArr);
                                $fun.handsontableCate.render();
                            }
                        }
                        else if(("CSlot"==colName ||"CaseQty"==colName ||"OrderPlanQtyUpdate"==colName) && preValue!=newValue){//伝票NO
                            var dataArr = $fun.handsontableCate.getSourceData();
                            if(dataArr.length-1==rowidx&&dataArr[rowidx].SplitNO&&dataArr[rowidx].SplitNO!="")
                            {
                                dataArr.push(jQuery.extend({}, $fun.showNewRow[0]));
                                $fun.handsontableCate.loadData(dataArr);
                                $fun.handsontableCate.render();
                            }
       
                        }
                    	var InnerCaseQty=$fun.showSplitList[rowidx].InnerCaseQty?parseInt($fun.showSplitList[rowidx].InnerCaseQty):1;
                        var OrderUnitQty=$fun.showSplitList[rowidx].OrderUnitQty?parseInt($fun.showSplitList[rowidx].OrderUnitQty):1;
                        var CaseMeasurement=$fun.showSplitList[rowidx].CaseMeasurement?$fun.showSplitList[rowidx].CaseMeasurement:0;
                        var CaseWeight=$fun.showSplitList[rowidx].CaseWeight?$fun.showSplitList[rowidx].CaseWeight:0;
                        if("CSlot"==colName && preValue!=newValue && $fun.showSplitList[rowidx]){//推奖数
                            if($fun.showSplitList[rowidx].ProLotQtyCheck){
                            	$fun.showSplitList[rowidx].OrderPlanQtyUpdate = //推奖数
            						Math.round(
            								(newValue?parseFloat(Number(newValue)):0)* parseInt($fun.showSplitList[rowidx].ProLotQtyCheck)
            					    );//発注ロット単位=C/S:入力ロット値*発注ロット入数*内箱入数    発注ロット単位=個:入力ロット値*発注ロット入数
            				}
            				else{
            					$fun.showSplitList[rowidx].OrderPlanQtyUpdate = "0"//推奖数
            				}
                            var OrderPlanQtyUpdate= parseInt($fun.showSplitList[rowidx].OrderPlanQtyUpdate);
                            
                            //発注推奨案のロット計算
                            $fun.getLotQtyCalu(rowidx,OrderPlanQtyUpdate,InnerCaseQty,OrderUnitQty,CaseMeasurement,CaseWeight,newValue?parseFloat(Number(newValue)):0);

                            //ケース数更新
                            if(!$fun.showSplitList[rowidx].InnerCaseQty || "0"==$fun.showSplitList[rowidx].InnerCaseQty){
                            	$fun.showSplitList[rowidx].CaseQty = 0; //ケース数
            				}else{					
            					$fun.showSplitList[rowidx].CaseQty = //ケース数
            						//ケース数 = 推奖数/InnerCaseQty
            						OrderPlanQtyUpdate?Math.round(parseFloat(Number(OrderPlanQtyUpdate)) / parseInt($fun.showSplitList[rowidx].InnerCaseQty)):null;
            						if(isNaN($fun.showSplitList[rowidx].CaseQty)){
            							$fun.showSplitList[rowidx].CaseQty = null;
            						}
            				}
                        }else if("CaseQty"==colName && preValue!=newValue && $fun.showSplitList[rowidx]){//推奖数
                            if(InnerCaseQty){
                            	$fun.showSplitList[rowidx].OrderPlanQtyUpdate = //推奖数
            						Math.round(
            								(newValue?parseFloat(Number(newValue)):0)* parseInt($fun.showSplitList[rowidx].InnerCaseQty)
            					    );
            				}
            				else{
            					$fun.showSplitList[rowidx].OrderPlanQtyUpdate = "0"//推奖数
            				}
                            //ロット数クリア
                        	$fun.showSplitList[rowidx].CSlot = null;
                            var OrderPlanQtyUpdate= parseInt($fun.showSplitList[rowidx].OrderPlanQtyUpdate,0);
                            //発注推奨案のロット計算
                            $fun.getLotQtyCalu(rowidx,OrderPlanQtyUpdate,InnerCaseQty,OrderUnitQty,CaseMeasurement,CaseWeight,0);
                        }else if("OrderPlanQtyUpdate"==colName && preValue!=newValue && $fun.showSplitList[rowidx]){//推奖数
                        	//ロット数クリア
                        	$fun.showSplitList[rowidx].CSlot = null;
            				//ケース数 クリア
                        	$fun.showSplitList[rowidx].CaseQty = null;
                            //発注推奨案のロット計算
                        	$fun.getLotQtyCalu(rowidx,newValue,InnerCaseQty,OrderUnitQty,CaseMeasurement,CaseWeight,0);
                        }
                    }
                    $fun.handsontableCate.render();
                }
            }
        },
        getLotQtyCalu:function(rowidx,OrderPlanQtyUpdate,InnerCaseQty,OrderUnitQty,CaseMeasurement,CaseWeight,CSlot){
        	var tmpCSlot=0;
        	//発注推奨案のロット計算
            if($fun.showSplitList[rowidx].TermsCD!="1"&&
                    $fun.showSplitList[rowidx].TermsCD!="2"&&
                    $fun.showSplitList[rowidx].TermsCD!="3"&&
                    $fun.showSplitList[rowidx].TermsCD!="4"&&
                    $fun.showSplitList[rowidx].TermsCD!="99"){
            	tmpCSlot=0;
                $fun.showSplitList[rowidx].SplitMesure=(OrderPlanQtyUpdate?Math.round(parseFloat(Number(OrderPlanQtyUpdate)))/InnerCaseQty:0)*CaseMeasurement;
                $fun.showSplitList[rowidx].SplitWeight=(OrderPlanQtyUpdate?Math.round(parseFloat(Number(OrderPlanQtyUpdate)))/InnerCaseQty:0)*(CaseWeight/OrderUnitQty);
            }else if($fun.showSplitList[rowidx].OrderUnit=="C/S"){
                if($fun.showSplitList[rowidx].TermsCD=="1" || $fun.showSplitList[rowidx].TermsCD=="2"){
                	tmpCSlot=OrderPlanQtyUpdate?Math.round(parseFloat(Number(OrderPlanQtyUpdate)))/InnerCaseQty/OrderUnitQty:0;
                    $fun.showSplitList[rowidx].SplitMesure=0;
                    $fun.showSplitList[rowidx].SplitWeight=0;
                }else if($fun.showSplitList[rowidx].TermsCD=="3" || $fun.showSplitList[rowidx].TermsCD=="4"){
                	tmpCSlot=OrderPlanQtyUpdate?Math.round(parseFloat(Number(OrderPlanQtyUpdate)))/InnerCaseQty:0;
                    $fun.showSplitList[rowidx].SplitMesure=0;
                    $fun.showSplitList[rowidx].SplitWeight=0;
                }
            }else if($fun.showSplitList[rowidx].OrderUnit=="個")
            {	
                if($fun.showSplitList[rowidx].TermsCD=="1" || $fun.showSplitList[rowidx].TermsCD=="2"){
                	tmpCSlot=OrderPlanQtyUpdate?Math.round(parseFloat(Number(OrderPlanQtyUpdate)))/OrderUnitQty:0;
                    $fun.showSplitList[rowidx].SplitMesure=0;
                    $fun.showSplitList[rowidx].SplitWeight=0;
                }else if($fun.showSplitList[rowidx].TermsCD=="3" || $fun.showSplitList[rowidx].TermsCD=="4"){
                	tmpCSlot=OrderPlanQtyUpdate?OrderPlanQtyUpdate:0;
                    $fun.showSplitList[rowidx].SplitMesure=0;
                    $fun.showSplitList[rowidx].SplitWeight=0;
                }
            }
            else{
                $fun.showSplitList[rowidx].SplitMesure=0;
                $fun.showSplitList[rowidx].SplitWeight=0;
            }
            if(isNaN(tmpCSlot)){
            	tmpCSlot = 0;
            }
            if(isNaN($fun.showSplitList[rowidx].SplitMesure)){
                $fun.showSplitList[rowidx].SplitMesure = 0;
            }
            if(isNaN($fun.showSplitList[rowidx].SplitWeight)){
                $fun.showSplitList[rowidx].SplitWeight = 0;
            }
            //CSLotUnit更新
            if(!$fun.showSplitList[rowidx].MixLotUnit || "0"==$fun.showSplitList[rowidx].MixLotUnit){
                $fun.showSplitList[rowidx].CSLotUnit = CSlot==0?tmpCSlot:CSlot; 
            }else{					
                $fun.showSplitList[rowidx].CSLotUnit = parseFloat(CSlot==0?tmpCSlot:CSlot)/ parseFloat($fun.showSplitList[rowidx].MixLotUnit);
                if(isNaN($fun.showSplitList[rowidx].CSLotUnit)){
                    $fun.showSplitList[rowidx].CSLotUnit = 0;
                }
            }
        },
        handsontableLoadCate: function(data){
            $fun.chartdata = data;
            var datatableCate=document.getElementById('datatableCate'+id);
            $fun.handsontableCate = new Handsontable(datatableCate,{
                data : data,
                colHeaders: function(col){
                    switch(col){
                    case 0:
                        var txt = '<label class="checkbox-inline"><input id="checkCate'+id+'" class="checkallCate" type="checkbox" ';
                            txt += $fun.isChecked() ? 'checked="checked"' : '';
                            txt += '/>全選</label>';
                        return txt;
                    case 1: return '伝票分けNO';
                    case 2: return '混載区分';
                    case 3: return 'JAN';
                    case 4: return '商品名';
                    case 5: return '規格';
                    case 6: return 'ロット数';
					case 7: return 'ケース数';
                    case 8: return '推奨数';
                    case 9: return '納品予定日';
                    case 10: return 'OrderPlanQtyUpdate_Old';
                    case 11: return 'DeliveryPlanDateUpdate_Old';
                    case 12: return 'SplitNO_Old';
                    case 13: return 'TermsCD';
                    case 14: return 'InnerCaseQty';
                    case 15: return 'MixLotUnit';
                    case 16: return 'CSLotUnit';
                    case 17: return 'SlipSplitQty';
                    case 18: return 'DayMaxQty';
                    case 19: return 'ProLotQtyCheck';
                    case 20: return 'CaseMeasurement';
                    case 21: return 'CaseWeight';
                    case 22: return 'SplitMesure';
                    case 23: return 'SplitWeight';
                    case 24: return 'Measurement';
                    case 25: return 'MaxWeight';
                    case 26: return 'AutoSplitFlg';
                    case 27: return 'NotDeliveryCheck';
                    case 28: return 'Times';
                    case 29: return 'OrderCaseDetailID';
                    case 30: return 'OrderUnitQty';
                    case 31: return 'OrderUnit';
                    case 32: return 'LotUnit';
                    case 33: return 'Terms';
                    }
                },
                colWidths: [54,70, 80, 100, 165,60,70,70,80,85,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001,0.001],
                minRows: 1,
                minCols: 8,
                maxCols: 34,
                fillHandle:false,
                wordWrap:false,
                search: true ,
                columnSorting: false,
                manualColumnResize: true,
                contextMenu: false,
                copyPaste:true,
                copyRowsLimit: 5000,
                currentRowClassName: 'currentRow',
                columns : [
                    {
                        data: 'ChkFlg',
                        type: 'checkbox',
                        checkedTemplate: '1', uncheckedTemplate: '0'
                    },
                    {data: 'SplitNO',type: 'text',      readOnly:true},//,validator: /^\d{0,5}$/
                    {data: 'MixCD',type: 'text',      readOnly:true},
                    {data: 'JAN',type: 'text',      readOnly:true},
                    {data: 'ProductName',type: 'text',      readOnly:true},
                    {data: 'SpecName',type: 'text',      readOnly:true},
                    {data: 'CSlot',	    type: 'text',     	 format: '0,0',readOnly:false},//ロット数
            	    {data: 'CaseQty',		type: 'numeric',     format: '0,0',readOnly:false},//ケース数
                    {data: 'OrderPlanQtyUpdate',type: 'text',     format: '0,0',readOnly:false},//,validator: /^\d{0,7}$/
                    {data: 'DeliveryPlanDateUpdate',type: 'date',
                        dateFormat: 'YYYY/MM/DD',
                        allowEmpty: true,
                        datePickerConfig: {
                            firstDay: 1,
                            showWeekNumber: true,
                            numberOfMonths: 3,
                            disableDayFn: function(date) {
                            	// 10105066 start
//                                 return new Date(date.toLocaleDateString())<
//                                       new Date(new Date(RegistryGlobal.OrderCaseCreateDate).toLocaleDateString()) ||
//                                       new Date(date.toLocaleDateString())>=
//                                           new Date(
//                                               new Date(
//                                                   Number(new Date(RegistryGlobal.OrderCaseCreateDate).getFullYear()+1)+"/"+
//                                                   Number(new Date(RegistryGlobal.OrderCaseCreateDate).getMonth()+1)+"/"+
//                                                   new Date(RegistryGlobal.OrderCaseCreateDate).getDate()
//                                               ).toLocaleDateString()
//                                           );
                        	return new Date(date)<
	     	            		   new Date(new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10))) ||  //new Date(str)方法，str格式影响其在IE浏览器上的解析
	     	            	   new Date(date)>=
	     	            		   new Date(
	     	            			   new Date(
	     	            				   Number(new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10)).getFullYear()+1)+"/"+
	     	            				   Number(new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10)).getMonth()+1)+"/"+
	     	            				   new Date(RegistryGlobal.OrderCaseCreateDate.substring(0,10)).getDate()
	     	            		       )
	     	            		   );
	     	            	   //10105066 end 
                            },
                            i18n: {
                                previousMonth : 'Previous Month',
                                nextMonth     : 'Next Month',
                                months        : ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
                                weekdays      : ['日曜日','月曜日','火曜日','水曜日','木曜日','金曜日','土曜日'],
                                weekdaysShort : ['日','月','火','水','木','金','土']
                            }
                        }
                    },
                    {data: 'OrderPlanQtyUpdate_Old',type: 'text',      readOnly:true},
                    {data: 'DeliveryPlanDateUpdate_Old',type: 'text',      readOnly:true},
                    {data: 'SplitNO_Old',type: 'text',      readOnly:true},
                    {data: 'TermsCD',type: 'text',      readOnly:true},
                    {data: 'InnerCaseQty',type: 'text',      readOnly:true},
                    {data: 'MixLotUnit',type: 'text',      readOnly:true},
                    {data: 'CSLotUnit',type: 'text',      readOnly:true},
                    {data: 'SlipSplitQty',type: 'text',      readOnly:true},
                    {data: 'DayMaxQty',type: 'text',      readOnly:true},
                    {data: 'ProLotQtyCheck',type: 'text',      readOnly:true},
                    {data: 'CaseMeasurement',type: 'text',      readOnly:true},
                    {data: 'CaseWeight',type: 'text',      readOnly:true},
                    {data: 'SplitMesure',type: 'text',      readOnly:true},
                    {data: 'SplitWeight',type: 'text',      readOnly:true},
                    {data: 'Measurement',type: 'text',      readOnly:true},
                    {data: 'MaxWeight',type: 'text',      readOnly:true},
                    {data: 'AutoSplitFlg',type: 'text',      readOnly:true},
                    {data: 'NotDeliveryCheck',type: 'text',      readOnly:true},
                    {data: 'Times',type: 'numeric',      readOnly:true},
                    {data: 'OrderCaseDetailID',type: 'numeric',      readOnly:true},
                    {data: 'OrderUnitQty',type: 'text',      readOnly:true},
                    {data: 'OrderUnit',type: 'text',      readOnly:true},
                    {data: 'LotUnit',type: 'text',      readOnly:true},
                    {data: 'Terms',type: 'text',      readOnly:true}
                ],
                cells: function (row, col, prop) {
                    if("SplitNO"==prop||"CSlot"==prop||"CaseQty"==prop||"OrderPlanQtyUpdate"==prop){
                        this.maxLength = 5;
                    }
//                    if("OrderPlanQtyUpdate"==prop){
//                        this.maxLength = 7;
//                    }
//                    if("CSlot"==prop){
//                        this.maxLength = 5;
//                    }
                    var cellProperties = {};
                    if(!isEmpty($fun.param.OrderStatus)&&!isEmpty($fun.param.IsVerNODiff)&&$fun.param.IsVerNODiff=="0"){//$fun.param.OrderStatus&&$fun.param.OrderStatus!="") {
                        cellProperties.renderer = $fun.approvalRender;
                    }else{
                        if(prop=="ChkFlg" ||prop=="SplitNO" || prop=="CSlot" ||prop=="CaseQty" || prop=="OrderPlanQtyUpdate" || prop=="DeliveryPlanDateUpdate"){//checkbox
                            cellProperties.renderer = $fun.styleRender;
                        }
                    }
                    return cellProperties;
                },
                beforeChange : function(changes, source){
                	if (changes && changes[0].length) {
                        for(var i=0; i<changes.length; i++){
                            rowidx = changes[i][0];
                            colName = changes[i][1];
                            preValue = changes[i][2];
                            newValue = changes[i][3];
                            if(colName=="DeliveryPlanDateUpdate"){
//            	    		    var dt = convertToCorrectDate(changes[i][3]);
//            	    		    if(dt && dt!=changes[i][3]){
//            	    		    	$fun.handsontableCate.setDataAtRowProp(rowidx, colName, dt);
//            	    		    }
            	    		    if (newValue!=null&&typeof(newValue)!="undefined") {
            	    				var data = newValue?String(newValue.toString().replace(/\D/g, "")):String(newValue); 
            	    			    if(!data){
            	    			    	changes[i][3]= "";
            	    				}else{
            	    				    var dt = convertToCorrectDate(newValue);
            	    				    if(!dt||!isDate(dt)){
            	    				    	changes[i][3]=  "";
            	    					}else if(dt){
            	    						changes[i][3]=  dt;
            	    				    }
            	    				}
            	    			} else {
            	    				changes[i][3]= "";
            	    			}
            	    		}else if(colName=="SplitNO" || colName=="CSlot" ||colName=="CaseQty" || colName=="OrderPlanQtyUpdate"){
            	    			newValue = newValue.replace(/[^0-9]/ig, "");
            	    			changes[i][3]=newValue.substring(0, 5);
            	    		}
                        }
                	}
    	    	},
                afterChange : function(changes, source) {
                    $fun.afterChg(changes, source);
                },
                afterRender: function () {
                    for(var c in $('#datatableCate'+id).find('input')){
                        $($('#datatableCate'+id).find('input')[c].parentNode).addClass("htCenter");
                    };
                    if(!isEmpty($fun.param.OrderStatus)&&!isEmpty($fun.param.IsVerNODiff)&&$fun.param.IsVerNODiff=="0"){//$fun.param.OrderStatus&&$fun.param.OrderStatus!="")
                        $("#splitDetail-modal-content"+id+" .checkallCate").prop("disabled",true);
                    }
                }
            });
            $("#datatableCate"+id).on('click', 'input.checkallCate',$fun.chkSplitAll);
        },
        chkSplitAll:function () {
            if($fun.isChkAll==true){
                $fun.isChkAll = false;
            }else{
                $fun.isChkAll = true;
            }
            var dataArr =$fun.handsontableCate.getSourceData();
            for (var i = 0, ilen = dataArr.length; i < ilen; i++) {
                if(i==ilen-1&&(!dataArr[i].SplitNO||dataArr[i].SplitNO=="")){
                }else{
                    dataArr[i].ChkFlg = $fun.isChkAll?1:0;
                }
            }
            $fun.handsontableCate.render();
        },
        isChecked: function(){
            var dataArr = [];
            if(!$fun.showSplitList||$fun.showSplitList.length==0){
                return false;
            }//init uncheck
            if($fun.isChkCol){
                dataArr = $fun.handsontableCate.getSourceData();
                for (var i = 0, ilen = dataArr.length; i < ilen; i++) {
                    if(i==ilen-1&&(!dataArr[i].SplitNO||dataArr[i].SplitNO=="")){
                        $fun.isChkAll = true;
                        return true;
                    }else if (!dataArr[i].ChkFlg||dataArr[i].ChkFlg=="0") {
                        $fun.isChkAll = false;
                        return false;
                    }
                }
                $fun.isChkAll = true;
                return true;
            }
            else{			
                return $fun.isChkAll;
            }
        },
        init: function(obj){/*モジュール初期入口*/
            $dialog.appendTo('body');
            $fun.param = obj;
            $fun.handsontableCate = [];
            $fun.chartdata = [];
            $fun.allSplitList=[];
            $fun.showSplitList=[];
            $fun.NotDeliveryList=[];
            $fun.showNewRow=[];
            $fun.saveOrderQty=0;
            $fun.saveLotQty=0;
            $fun.saveDeliveryDate="";
            $fun.isDateSame=false;
            $("#btnSaveKeyCate"+id).bind("click",$fun.SaveKeyCate);
            $fun.dialog().on("hidden.bs.modal",function(){
                //if($fun.saveOrderQty!=0&&$fun.saveDeliveryDate!="")
            	if($fun.saveLotQty!=0||$fun.saveDeliveryDate!="")
                {
                	//主画面に戻る推奨数と混載ごと納品予定日の更新(最小納品日)
                    //TemplateGrid.tbhot.setDataAtRowProp($fun.param.row,"OrderPlanQty",$fun.saveOrderQty);
                    TemplateGrid.tbhot.setDataAtRowProp($fun.param.row,"LotQty",$fun.saveLotQty);
                    var preDeliveryPlanDate=TemplateGrid.tbhot.getDataAtRowProp($fun.param.row,"DeliveryPlanDate");
                    var currMixCD=TemplateGrid.tbhot.getDataAtRowProp($fun.param.row,"MixCD");
                    var currTimes=TemplateGrid.tbhot.getDataAtRowProp($fun.param.row,"Times");
                    var realRow;
                    //混載区分別納品予定日更新
                    if($fun.saveDeliveryDate!=preDeliveryPlanDate){
                        if($fun.saveDeliveryDate!=""){
                            TemplateGrid.tbhot.setDataAtRowProp($fun.param.row,"DeliveryPlanDate",$fun.saveDeliveryDate);
                        }
                    	RegistryGlobal.tableSourceData = TemplateGrid.tbhot.getSourceData();
                    	$.each(RegistryGlobal.tableSourceData, function(i, data){
                			if((RegistryGlobal.tableSourceData[i].ApprovalFlag&&"1"==RegistryGlobal.tableSourceData[i].ApprovalFlag)
                                || !RegistryGlobal.tableSourceData[i].OrderCaseID){
                			}else if(i != $fun.param.row && currMixCD == RegistryGlobal.tableSourceData[i].MixCD
                                &&currTimes==RegistryGlobal.tableSourceData[i].Times
                				&&!isEmpty(RegistryGlobal.tableSourceData[i].DeliveryPlanDate)){
                				realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow',i);
                				RegistryGlobal.tableSourceData[realRow].DeliveryPlanDate=$fun.saveDeliveryDate;
                			}
                		});
                    }
                    TemplateGrid.tbhot.render();
                }
                $fun.destroy();
            });
            $fun.dialog().on("shown.bs.modal",function(){
                $(document).off('focusin.bs.modal');
                $fun.handsontableCate.render();
                if(!isEmpty($fun.param.OrderStatus)&&!isEmpty($fun.param.IsVerNODiff)&&$fun.param.IsVerNODiff=="0"){//if($fun.param.OrderStatus&&$fun.param.OrderStatus!="") {
                    $("#splitDetail-modal-content"+id+" .checkallCate").prop("disabled",true);
                }
                
            });
            $fun.dialog().on("show.bs.modal",function(){
                $fun.handsontableCate.render();
                if(!isEmpty($fun.param.OrderStatus)&&!isEmpty($fun.param.IsVerNODiff)&&$fun.param.IsVerNODiff=="0"){//if($fun.param.OrderStatus&&$fun.param.OrderStatus!="") {
                    $("#splitDetail-modal-content"+id+" .checkallCate").prop("disabled",true);
                }
            });
            $fun.dialog().on("hide.bs.modal",function(){
            });
           
            $("#splitDetail-dialog-module"+id).scroll(function(){
                $fun.adjust();
            });
            $("#splitDetail-dialog-module"+id).resize(function(){
                $fun.adjust();
            });
            $fun.handsontableLoadCate([]);
        },
        show: function(){
            $dialog.modal({backdrop: 'static', keyboard: false});
            $fun.GetKeyCate();
            $fun.handsontableCate.selectCellByProp(0,"SplitNO");
            if(!isEmpty($fun.param.OrderStatus)&&!isEmpty($fun.param.IsVerNODiff)&&$fun.param.IsVerNODiff=="0"){//if($fun.param.OrderStatus&&$fun.param.OrderStatus!=""){
                $("#btnSaveKeyCate"+id).attr("disabled", true); 
            }
        },
        hide: function(){
            $dialog.modal('hide');
        },
        GetKeyCate: function(obj){//初期化
            $fun.allSplitList=[];
            $fun.showSplitList=[];
            $fun.NotDeliveryList=[];
            $fun.showNewRow=[];
            $fun.isDialog=true;
            $.ajax({
                url:'../Page/split_GetsplitDetailInfo.action',
                type:'POST',
                data:{orderCaseID:$fun.param.orderCaseID},
                success:function(response){
                	if("false"==response){
                        showMsgBox(Message.msg001,[],function(){});
                    }else{
	                    var result = JSON.parse(response);
	                    $fun.allSplitList = result[0].concat(); 
	                    $fun.NotDeliveryList = result[1];
	
	                    for(var i=0; i<result[0].length; i++){
	                        var JAN = result[0][i].JAN;
	                        if(JAN==$fun.param.strJAN)
	                        {
	                            $fun.showSplitList.push(result[0][i]);//表示情報
	                            if($fun.showNewRow.length==0)
	                            {
	                                var newJson = jQuery.extend({}, result[0][i]);
	                                $fun.showNewRow.push(newJson);
	                                $fun.showNewRow[0].ChkFlg="0";
	                                $fun.showNewRow[0].SplitNO="";
	                                $fun.showNewRow[0].CSlot="";
	                                $fun.showNewRow[0].CaseQty="";
	                                $fun.showNewRow[0].OrderPlanQtyUpdate="";
	                                $fun.showNewRow[0].DeliveryPlanDateUpdate="";
	                                $fun.showNewRow[0].OrderPlanQtyUpdate_Old="";
	                                $fun.showNewRow[0].DeliveryPlanDateUpdate_Old="";
	                                $fun.showNewRow[0].SplitNO_Old="";
	                                $fun.showNewRow[0].AutoSplitFlg="1";
	                                $fun.showNewRow[0].NotDeliveryCheck="0";
	                            }
	                        }
	                    }
                        if(!isEmpty($fun.param.OrderStatus)&&!isEmpty($fun.param.IsVerNODiff)&&$fun.param.IsVerNODiff=="0"){//if($fun.param.OrderStatus&&$fun.param.OrderStatus!=""){//OrderStatus
	                    }else{
	                        if($fun.showSplitList.length>0&&$fun.showSplitList[0].OrderPlanQtyUpdate&&$fun.showSplitList[0].OrderPlanQtyUpdate!="")
	                        {
	                            $fun.showSplitList.push(jQuery.extend({}, $fun.showNewRow[0]));
	                        }
	                    }
	                    $fun.maxShowRowNum=$fun.showSplitList.length-1;
	                    $fun.handsontableCate.loadData($fun.showSplitList);
	                    $fun.handsontableCate.render();
                        if(!isEmpty($fun.param.OrderStatus)&&!isEmpty($fun.param.IsVerNODiff)&&$fun.param.IsVerNODiff=="0"){//if($fun.param.OrderStatus&&$fun.param.OrderStatus!="") {
	                        $("#splitDetail-modal-content"+id+" .checkallCate").prop("disabled",true);
	                    }
                    }
                },
                error:function(response, textStatus){
                    showMsgBox(Message.msg001,[],function(){});
                }
            });
        },
        SaveKeyCate: function(){//確定
            var tableData = $fun.handsontableCate.getSourceData();
            var sendData = "";
            var cnt = 0;
            var pass = true;
            var chkData = [];
            //var sumOrderPlanQtyUpdate=0;
            var sumLotQty=0;
            var isChange=false;
            var dateSameRow;
            $fun.isDateSame=false;

            if(tableData.length==0){
                showMsgBox(Message.msg003,[],function(){});
                return;
            }
            for(var i=0;i<tableData.length;i++){
                if("1"==tableData[i].ChkFlg){
                    cnt++;
                    if (!tableData[i].SplitNO||tableData[i].SplitNO == ""||tableData[i].SplitNO == "0")
                    {
                        showMsgBox(Message.msg013,["伝票分けNO"],function(){
                            $fun.handsontableCate.selectCellByProp(i,"SplitNO");
                        });
                        return;
                    }
                    else{
                        if (isNaN(tableData[i].SplitNO)||parseInt(tableData[i].SplitNO)>99999)
                        {
                            showMsgBox(Message.msg012,["伝票分けNO",5],function(){
                                $fun.handsontableCate.selectCellByProp(i,"SplitNO");
                            });
                            return;
                        }
                        var isExisted = false;
                        for (var j=0; j<tableData.length && j<i; j++)
                        {
                            if (j != i && tableData[i].SplitNO == tableData[j].SplitNO)
                            {
                                isExisted = true;
                                break;
                            }
                        }
                        if (isExisted)
                        {
                            showMsgBox(Message.msg014,[],function(){
                                $fun.handsontableCate.selectCellByProp(i,"SplitNO");
                            });
                            return;
                        }
                    }
                    if (!tableData[i].OrderPlanQtyUpdate || tableData[i].OrderPlanQtyUpdate == "")
                    {
                        showMsgBox(Message.msg026,[],function(){
                            $fun.handsontableCate.selectCellByProp(i,"CSlot");
                        });
                        
                        return;
                    }else{
                        if (isNaN(tableData[i].OrderPlanQtyUpdate))
                        {
                            showMsgBox(Message.msg012,["推奨数",5],function(){
                                $fun.handsontableCate.selectCellByProp(i,"OrderPlanQtyUpdate");
                            });
                            return;
                        }
                        if (parseInt(tableData[i].OrderPlanQtyUpdate)>=100000)
                        {
                        	showMsgBox(Message.msg027,[],function(){
                                $fun.handsontableCate.selectCellByProp(i,"OrderPlanQtyUpdate");
                            });
                            return;
                        }
                        //単品別倍数チェック
                        var planQty = parseInt(tableData[i].OrderPlanQtyUpdate);
                        
                        //単品別ケース倍数チェック
                        var intInnerCaseQty = parseInt(tableData[i].InnerCaseQty);
                        if("C/S"==tableData[i].OrderUnit){
                            if (intInnerCaseQty != 0 && (planQty % intInnerCaseQty) != 0)
                            {
                                showMsgBox(Message.msg007,[intInnerCaseQty],function(){
                                    $fun.handsontableCate.selectCellByProp(i,"CSlot");
                                });
                                return;
                            }
                        }
                        var strTermsCD = tableData[i].TermsCD;
                        //①と伝票分け数チェック　
                        var drSplit=[];
                        for(var k=0; k<$fun.allSplitList.length; k++){
                            if($fun.allSplitList[k].SplitNO== tableData[i].SplitNO&& $fun.allSplitList[k].JAN!=$fun.param.strJAN)
                            {
                                drSplit.push($fun.allSplitList[k]);//伝票チェック情報
                            }
                        }
                        if (strTermsCD == "1" || strTermsCD == "2" || strTermsCD == "3" || strTermsCD == "4")
                        {
                            var SlipCSLotUnit = 0;
                            var slipSplitQty = 0;
                            
                            if (tableData[i].SlipSplitQty!="")
                            {
                                slipSplitQty=parseInt(tableData[i].SlipSplitQty);
                            }
                            if (drSplit.length > 0)
                            {
                                for (var m = 0; m < drSplit.length; m++)
                                {
                                    SlipCSLotUnit = SlipCSLotUnit + parseFloat(drSplit[m].CSLotUnit);
                                }
                            }
                            for (var n = 0; n < tableData.length; n++)
                            {
                                if (tableData[i].SplitNO == tableData[n].SplitNO )
                                {
                                    SlipCSLotUnit = SlipCSLotUnit + parseFloat(tableData[n].CSLotUnit);
                                }
                            }
//                            SlipCSLotUnit = Math.round(SlipCSLotUnit, 2);
                            SlipCSLotUnit = Math.ceil(SlipCSLotUnit);
                            if (slipSplitQty != 0 && slipSplitQty < SlipCSLotUnit) 
                            {
                                showMsgBox(Message.msg015,[],function(){
                                    $fun.handsontableCate.selectCellByProp(i,"CSlot");
                                });
                                return;
                            }
                        }
                        else if (strTermsCD != "99")
                        {
                            var sumSplitMesure = 0;
                            var sumSplitWeight = 0;
                            var measurement =parseFloat(tableData[i].Measurement);
                            var maxWeight = parseFloat(tableData[i].MaxWeight);
                            //混載区分別体積と重量合計
                            if (drSplit.length > 0)
                            {
                                for (var m = 0; m < drSplit.length; m++)
                                {
                                    sumSplitMesure = sumSplitMesure + parseFloat(drSplit[m].SplitMesure);
                                    sumSplitWeight = sumSplitWeight + parseFloat(drSplit[m].SplitWeight);
                                }
                            }
                            for (var n = 0; n < tableData.length; n++)
                            {
                                if (tableData[i].SplitNO == tableData[n].SplitNO )
                                {
                                    sumSplitMesure = sumSplitMesure + parseFloat(tableData[n].SplitMesure);
                                    sumSplitWeight = sumSplitWeight + parseFloat(tableData[n].SplitWeight);
                                }
                            }
                            if (measurement < sumSplitMesure)
                            {
                                showMsgBox(Message.msg016,[],function(){
                                    $fun.handsontableCate.selectCellByProp(i,"CSlot");
                                });
                                return;
                            }
                            else if (maxWeight < sumSplitWeight)
                            { //コンテナ：積載重量チェック
                                showMsgBox(Message.msg017,[],function(){
                                    $fun.handsontableCate.selectCellByProp(i,"CSlot");
                                });
                                return;
                            }
                        }
                    }
                    if (!tableData[i].DeliveryPlanDateUpdate||tableData[i].DeliveryPlanDateUpdate == "")
                    {
                        showMsgBox(Message.msg026,[],function(){
                            $fun.handsontableCate.selectCellByProp(i,"DeliveryPlanDateUpdate");
                        });
                        return;
                    }
                    else
                    {
                        if (!isDate(tableData[i].DeliveryPlanDateUpdate))
                        {
                            showMsgBox(Message.msg018,["納品予定日"],function(){
                                $fun.handsontableCate.selectCellByProp(i,"DeliveryPlanDateUpdate");
                            });
                            return;
                        }      				
                        if(new Date(new Date(tableData[i].DeliveryPlanDateUpdate).toLocaleDateString())<new Date(new Date($fun.param.OrderCaseCreateDate).toLocaleDateString())){
                            showMsgBox(Message.msg009,[],function(){
                                $fun.handsontableCate.selectCellByProp(i,"DeliveryPlanDateUpdate");
                            });
                            return;
                        }
                        var year = new Date($fun.param.OrderCaseCreateDate).getFullYear();
                        var month = new Date($fun.param.OrderCaseCreateDate).getMonth();
                        var date = new Date($fun.param.OrderCaseCreateDate).getDate();
                        if(new Date(tableData[i].DeliveryPlanDateUpdate)>=new Date(Number(year+1)+"/"+Number(month+1)+"/"+date)){
                            showMsgBox(Message.msg010,[],function(){
                                $fun.handsontableCate.selectCellByProp(i,"DeliveryPlanDateUpdate");
                            });
                            return;
                        }
                        //同じ回数の伝票分けNOの納品予定日チェック
                        var drPlanDate=[];
                        for(var m=0; m<$fun.allSplitList.length; m++){
                            if($fun.allSplitList[m].SplitNO== tableData[i].SplitNO&& $fun.allSplitList[m].JAN!=$fun.param.strJAN&& $fun.allSplitList[m].DeliveryPlanDateUpdate)
                            {
                                drPlanDate.push($fun.allSplitList[m]);//伝票チェック情報
                            }
                        }
                        if (drPlanDate.length > 0 && drPlanDate[0].DeliveryPlanDateUpdate!="")
                        {
                            var dtCurrDate1 = drPlanDate[0].DeliveryPlanDateUpdate;
                            var dtCurrDate2 =tableData[i].DeliveryPlanDateUpdate;
                            if (dtCurrDate1 != dtCurrDate2)
                            {
                                if (tableData[i].AutoSplitFlg == "1")
                                {
                                    showMsgBox(Message.msg024,[tableData[i].SplitNO],function(){
                                        $fun.handsontableCate.selectCellByProp(i,"DeliveryPlanDateUpdate");
                                    });
                                    return ;
                                }
                                else{
                                    dateSameRow=i;
                                    $fun.isDateSame=true;
                                }
                            }
                        }
                        //1日最大数チェック　
                        var strTermsCD = tableData[i].TermsCD;
                        if (strTermsCD == "1" || strTermsCD == "2" || strTermsCD == "3" || strTermsCD == "4")
                        {
                            //②と1日最大数量チェック
                            var DayCSLotUnit = 0;
                            var dayMaxQty=0;
                            if (tableData[i].DayMaxQty)
                            {
                                dayMaxQty = parseInt(tableData[i].DayMaxQty);
                            }
                            if (tableData[i].DeliveryPlanDateUpdate)
                            {
                                var deliveryDate = tableData[i].DeliveryPlanDateUpdate;
                                var drDayPlanDate = [];
                                for(var p=0; p<$fun.allSplitList.length; p++){
                                    if($fun.allSplitList[p].DeliveryPlanDateUpdate== tableData[i].DeliveryPlanDateUpdate&& $fun.allSplitList[p].JAN!=$fun.param.strJAN)
                                    {
                                        drDayPlanDate.push($fun.allSplitList[p]);//伝票チェック情報
                                    }
                                }
                                if (drDayPlanDate.length > 0)
                                {
                                    for (var x = 0; x < drPlanDate.length; x++)
                                    {
                                        DayCSLotUnit = DayCSLotUnit + parseFloat(drPlanDate[x].CSLotUnit);
                                    }
                                }
                                for (var y = 0; y < tableData.length; y++)
                                {
                                    if (tableData[i].DeliveryPlanDateUpdate == tableData[y].DeliveryPlanDateUpdate)
                                    {
                                        DayCSLotUnit = DayCSLotUnit + parseFloat(tableData[y].CSLotUnit);
                                    }
                                }
//                                DayCSLotUnit = Math.round(DayCSLotUnit, 2);
                                DayCSLotUnit = Math.ceil(DayCSLotUnit);
                                if (dayMaxQty!=0 && dayMaxQty < DayCSLotUnit)
                                {
                                    showMsgBox(Message.msg025,[tableData[i].SplitNO],function(){
                                        $fun.handsontableCate.selectCellByProp(i,"DeliveryPlanDateUpdate");
                                    });
                                    return ;
                                }
                            }
                        }
                    }
                    //変更チェック
                    if (tableData[i].SplitNO != tableData[i].OldSplitNO || 
                        tableData[i].OrderPlanQtyUpdate != tableData[i].OrderPlanQtyUpdate_Old ||
                        tableData[i].DeliveryPlanDateUpdate != tableData[i].DeliveryPlanDateUpdate_Old)
                    {
                        //データ変更
                        isChange = true;
                    }
                    //sumOrderPlanQtyUpdate+=parseInt(tableData[i].OrderPlanQtyUpdate);
                    sendData += $fun.param.orderCaseID+",";
                    sendData += tableData[i].Times+",";
                    sendData += tableData[i].OrderCaseDetailID+",";
                    sendData += tableData[i].SplitNO+",";
                    sendData += tableData[i].MixCD+",";
                    sendData += tableData[i].JAN+",";
                    sendData += tableData[i].OrderPlanQtyUpdate+",";
                    sendData += tableData[i].DeliveryPlanDateUpdate+",";
                    //if(typeof( tableData[i].SlipSplitQty)!="undefined"){
                    if(!isEmpty(tableData[i].SlipSplitQty)&&tableData[i].SlipSplitQty!="0"){
                        sendData += tableData[i].SlipSplitQty+",";
                    }
                    else{
                        sendData += ",";
                    }
                    //if(typeof( tableData[i].DayMaxQty)!="undefined"){
                    if(!isEmpty(tableData[i].DayMaxQty)&&tableData[i].DayMaxQty!="0"){
                        sendData += tableData[i].DayMaxQty+",";
                    }
                    else{
                        sendData += ",";
                    }
                    //if(typeof( tableData[i].CSlot)!="undefined"){
                    if(!isEmpty(tableData[i].CSlot)&&tableData[i].CSlot!="0"){
                        sendData += tableData[i].CSlot+",";
                        sumLotQty+=parseInt(tableData[i].CSlot);
                    }
                    else{
                        var ProLotQtyCheck=isEmpty(tableData[i].ProLotQtyCheck)?1:parseInt(tableData[i].ProLotQtyCheck);
                        if(!isEmpty(ProLotQtyCheck)){
                            var tmpCSlot=Math.round(tableData[i].OrderPlanQtyUpdate/ ProLotQtyCheck);
                            sendData += tmpCSlot+",";
                            sumLotQty+=tmpCSlot;
                        }else{
                            sendData += ",";
                        }
                    }

                    //if(typeof( tableData[i].MixLotUnit)!="undefined"){
                    if(!isEmpty(tableData[i].MixLotUnit)&&tableData[i].MixLotUnit!="0"){
                        sendData += tableData[i].MixLotUnit+",";
                    }
                    else{
                        sendData += ",";
                    }
                    //if(typeof( tableData[i].CSLotUnit)!="undefined"){
                    if(!isEmpty(tableData[i].CSLotUnit)&&tableData[i].CSLotUnit!="0"){
                        sendData += tableData[i].CSLotUnit+",";
                    }
                    else{
                        sendData += ",";
                    }
                    //if(typeof( tableData[i].SumMesure)!="undefined"){
                    if(!isEmpty(tableData[i].SumMesure)&&tableData[i].SumMesure!="0"){
                        sendData += tableData[i].SumMesure+",";
                    }
                    else{
                        sendData += ",";
                    }
                    //if(typeof( tableData[i].SumWeight)!="undefined"){
                    if(!isEmpty(tableData[i].SumWeight)&&tableData[i].SumWeight!="0"){
                        sendData += tableData[i].SumWeight+",";
                    }
                    else{
                        sendData += ",";
                    }
                    //if(typeof( tableData[i].Measurement)!="undefined"){
                    if(!isEmpty(tableData[i].Measurement)&&tableData[i].Measurement!="0"){
                        sendData += tableData[i].Measurement+",";
                    }
                    else{
                        sendData += ",";
                    }
                    //if(typeof( tableData[i].MaxWeight)!="undefined"){
                    if(!isEmpty(tableData[i].MaxWeight)&&tableData[i].MaxWeight!="0"){
                        sendData += tableData[i].MaxWeight+",";
                    }
                    else{
                        sendData += ",";
                    }
                    //if(typeof( tableData[i].AutoSplitFlg)!="undefined"){
                    if(!isEmpty(tableData[i].AutoSplitFlg)){
                        sendData += tableData[i].AutoSplitFlg+";";
                    }
                    else{
                        sendData += ";";
                    }
                }
                else if (i < $fun.maxShowRowNum &&"0"==tableData[i].ChkFlg)
                {
                    //選択変更
                    isChange = true;
                }
            }
            //変更なし
            if (!isChange)
            {
                return;
            }
            if(0==cnt){
                showMsgBox(Message.msg004,[],function(){$fun.handsontableCate.selectCellByProp(0,"SplitNO");});
                return;
            }
            if($fun.isDateSame){
                showMsgConfirm(Message.msg023,[tableData[dateSameRow].SplitNO],function(){
                    $.ajax({
                        url:'../Page/split_SaveSplitDetailInfo.action',
                        type:'POST',
                        data:{sendData: sendData, userCD: $fun.param.usercd},
                        success:function(response){
                            if("false"==response){
                                showMsgBox(Message.msg011,[],function(){});
                            }else{
                                $fun.saveLotQty=sumLotQty;//ロット数
                                //$fun.saveOrderQty=sumOrderPlanQtyUpdate;
                                $fun.saveDeliveryDate=response;
                                $fun.GetKeyCate();
                            }
                        },
                        error:function(response, textStatus){
                            showMsgBox(Message.msg011,[],function(){});
                        }
                    });
                },function(){
                    $fun.handsontableCate.selectCellByProp(dateSameRow,"DeliveryPlanDateUpdate");
                    return;
                });
            }else{
                $.ajax({
                    url:'../Page/split_SaveSplitDetailInfo.action',
                    type:'POST',
                    data:{sendData: sendData, userCD: $fun.param.usercd},
                    success:function(response){
                        if("false"==response){
                            showMsgBox(Message.msg011,[],function(){});
                        }else{
                            $fun.saveLotQty=sumLotQty;//ロット数
                            //$fun.saveOrderQty=sumOrderPlanQtyUpdate;
                            $fun.saveDeliveryDate=response;
                            $fun.GetKeyCate();
                        }
                    },
                    error:function(response, textStatus){
                        showMsgBox(Message.msg011,[],function(){});
                    }
                });
            }
        },
        destroy: function(){
            $("#splitDetail-dialog-module" + id).remove();
        },
        pageHeight:function() {
            return $("#splitDetail-modal-content"+id).css("height");
        },
        pageWidth:function(){
            return $("#splitDetail-modal-content"+id).css("width");
        },
        adjust:function() {
            if( $('#splitDetail-modal-content'+id).size()){
                var X = $('#splitDetail-modal-content'+id).offset().top; 
                var Y = $('#splitDetail-modal-content'+id).offset().left; 
            }
        }
    };
    return $fun;
});