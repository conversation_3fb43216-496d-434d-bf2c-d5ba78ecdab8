var TemplateGrid ;
var getTemplateMainGrid=function(arrCenter, arrSupplier, arrDivision){
	var cArr = [];
	var vArr = [];
	var dArr = [];
	for (var i=0;i<arrCenter.length;i++){
		var obj={};
		obj.id=arrCenter[i].centerCD;
		obj.label=arrCenter[i].centerCD+"-"+arrCenter[i].centerName;
		cArr.push(obj);
	}
	for (var i=0;i<arrSupplier.length;i++){
		var obj={};
		obj.id=arrSupplier[i].supplierCD;
		obj.label=arrSupplier[i].supplierCD+"-"+arrSupplier[i].supplierName;
		vArr.push(obj);
	}
	for (var i=0;i<arrDivision.length;i++){
		var obj={};
		obj.id=arrDivision[i].divisionCD;
		obj.label=arrDivision[i].divisionCD+"-"+arrDivision[i].divisionName;
		dArr.push(obj);
	}
	TemplateGrid = {
			tbhot:null,
			colWidths:[60,320,250,250,0.001],
			columns: [
	            {data: 'chkFlg',    		type: 'checkbox',    checkedTemplate: '1', uncheckedTemplate: '0'},//全
	            {
	            	data: 'supplier',	    	
	                editor: "chosen",
	                chosenOptions: {
	                    data: vArr
	                }
	            },//センター
	            {
	            	data: 'center',			
	                editor: "chosen",
	                chosenOptions: {
	                    data: cArr
	                }
	            },//ベンダー
	            {
	            	data: 'division',	    	
	                editor: "chosen",
	                chosenOptions: {
	                	multiple: true,
	                    data: dArr
	                }
	            },//DIV
	            {data: 'id',				type: 'text',     	 readOnly:true}//id
			],
			showSplitList: [],
			isChecked: function(){
				if(!this.tbhot){
					return false;
				}
				if("afterChg"==chk){
					RegistryGlobal.tableSourceData = TemplateGrid.tbhot.getSourceData();
					for (var i=0; i<RegistryGlobal.tableSourceData.length; i++) {
						if(RegistryGlobal.tableSourceData[i].chkFlg!="1"){						
							return false;
						}
					}
					return true;
				}else{
					return chk;
				}
			},
			//create grid
			CreatHotGrid: function(hotgrid,arrData){
				//まず対象内容クリア
				if(this.tbhot){
					this.tbhot.destroy();
					chk=false;
				}
				this.tbhot = new Handsontable(document.getElementById(hotgrid),{
					height:$(".page-content").height()-150,
					rowHeaders: false,
					colHeaders: function(col){
									switch(col){
									case 0:
										var txt = '<label class="checkbox-inline"><input id="chkAll" onclick="chkAll();" type="checkbox"';
										txt += TemplateGrid.isChecked() ? 'checked="checked"' : '';
										txt += '/>全</label>';
										return txt;
									case 1: return 'ベンダー';
									case 2: return 'センター';
									case 3: return 'DIV';
									}
								},
					colWidths: this.colWidths,
					columns: this.columns,
					overflow: scroll,
				    fillHandle:false,
				    wordWrap:false,
				    search: true,
				    minSpareRows:1,
				    columnSorting: true,
				    sortIndicator: true,
				    manualColumnResize: true,
					currentRowClassName: 'currentRow',
					cells: function (row, col, prop) {
						var cellProperties = {};
						cellProperties.renderer = styleRender;
						return cellProperties;					
			    	},
			    	beforeChange : function(changes, source){
			    		if(changes!=null){  
							for(var i=0;i<changes.length;i++){
								var rowidx = changes[i][0];
								var colName = changes[i][1];
								var preValue = changes[i][2];
								var newValue = changes[i][3];
			                    if(typeof(preValue)=="undefined"&&(newValue==""||newValue==null)){  	
			                    }else{
			                    	if(typeof(RegistryGlobal.showList[rowidx].chkFlg)=="undefined"){
				                    	RegistryGlobal.showList[rowidx].chkFlg = "0";
				                    }
			                    }
							}
		                }
			    	},
			    	afterChange: function(changes, source) {
			    		if (source === 'loadData'||source ==='external') {
			                return; //don't save this change
			            }
			    		if (changes && changes[0].length) {
				            for(var i=0; i<changes.length; i++){
				                var rowidx = changes[i][0];
				                var colName = changes[i][1];
				                var preValue = changes[i][2];
				                var newValue = changes[i][3];
	    	                    if(typeof(colName)=="undefined"
	    	                    	||((typeof(preValue)=="undefined"||preValue==""|| preValue==null)&& (typeof(newValue)=="undefined"||newValue==""|| newValue==null))
	    	                        || newValue==preValue) {
	    	                    }else{
					    			RegistryGlobal.tableSourceData = TemplateGrid.tbhot.getSourceData();
					    			var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', rowidx);
					    			if("chkFlg"==colName){
					    				chk = "afterChg";
					    				if("0"==newValue||"1"==newValue){
					    					RegistryGlobal.tableSourceData[realRow].chkFlg=newValue;
					    				}else{
					    					TemplateGrid.tbhot.setDataAtRowProp(rowidx,"chkFlg",preValue);
					    				}
					    			}else if (preValue!=newValue) {
					    				RegistryGlobal.tableSourceData[realRow].chkFlg = "1";
					    			}
	    	                    }
				            }
				            TemplateGrid.tbhot.render();
			    		}
		 			},
		 			afterSelectionByProp: function(r, p, r2, p2) {
		 			}
				});		
				this.tbhot.loadData(arrData);
			    this.tbhot.render();
			}		
	}
	//Grid　初期データロード
	var arrData = [[]];
	//var result = eval(arrData);
	if(RegistryGlobal.showList.length==0){
		RegistryGlobal.showList =arrData[0];
	}
	TemplateGrid.CreatHotGrid('MainGrid',RegistryGlobal.showList);
}
var chk;
var chkAll = function(){
	if(chk==true){
		chk = false;
	}else{
		chk = true;
	}
	if(TemplateGrid.tbhot.getSourceData().length>0){		
		$.each(RegistryGlobal.showList, function(i, data){ 
			data.chkFlg = chk?"1":"0";
		});
	}
	TemplateGrid.tbhot.render();
}

var styleRender=function(instance, td, row, col, prop, value, cellProperties){
	Handsontable.renderers.TextRenderer.apply(this, arguments);
	var color;
	var realRow = Handsontable.hooks.run(TemplateGrid.tbhot, 'modifyRow', row);
	if("chkFlg"==prop){//checkbox
		Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
		td.style.textAlign = "center";
	}else if("supplier"==prop||"center"==prop||"division"==prop){
		if(value == null){
			Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
			td.style.textAlign = "left";
			td.style.backgroundColor = "#FFFF99";
		}else{
			var selectedId;
		    var optionsList = cellProperties.chosenOptions.data;
		    if(typeof optionsList === "undefined" || typeof optionsList.length === "undefined" || !optionsList.length) {
		        Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
		        return td;
		    }
		    var values = (value + "").split(",");
		    var value = [];
		    for (var index = 0; index < optionsList.length; index++) {
		        if (values.indexOf(optionsList[index].id + "") > -1) {
		            selectedId = optionsList[index].id;
		            value.push(optionsList[index].label);
		        }
		    }
		    value = value.join(", ");
		    Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
		    td.style.textAlign = "left";
			td.style.backgroundColor = "#FFFF99";
		    if(TemplateGrid.tbhot!=null && value==""){
		    	TemplateGrid.tbhot.setDataAtCell(row, col, null);
			}
		}
	}
	return td;
}