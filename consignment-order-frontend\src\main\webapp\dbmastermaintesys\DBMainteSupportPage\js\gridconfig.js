var windowWidth = window.screen.availWidth;
var windowHeight = window.screen.availHeight;
var vtbConfig = {};
var hotGridMeisai;
var oldCellValue;
var oldChlFlg;
//保存の行番号
var saveArr = [];

// 列番号設定
var listCol = {
	'chkFlg' : 0,// Checkbox
	'deliveryPlanStartDate' : 1,// 適用開始日
	'storeHouse' : 2,// センターCD
	'dIV' : 3,// Div
	'line' : 4,// Line
	'department' : 5,// 部門
	'jAN' : 6,// JAN
	'productName' : 7,// 商品
	'specName' : 8,// 規格
	'shipptype' : 9,// 物流タイプ
	'orderCenter' : 10,// 発注センター
	'nohiSaki' : 11,// 納品先
	'orderFlg' : 12,// 発注フラグ
	'supplierCD' : 13,// ベンダー
	'mixID' : 14,// 混載ID
	'titleName' : 15,// 代表商品名
	'qput' : 16,// 入数
	'qunt' : 17,// 単位
	'qty' : 18,// 数量
	'qflg' : 19,// 条件
	'jyuRru' : 20,// 重量
	'taiSeki' : 21,// 体積
	'stockQty' : 22,// 安全在庫日数
	'syokaQty' : 23,// 基準消化日数
	'orderKikan' : 24,// 発注間隔
	'nichi' : 25,// 日
	'getu' : 26,// 月
	'kayo' : 27,// 火
	'siu' : 28,// 水
	'moku' : 29,// 木
	'kin' : 30,// 金
	'doyo' : 31,// 土
	'kiJun' : 32,// 基準
	'jiXuKubun' : 33,// 継続区分
	'orderKubun' : 34,// 発注区分
	'sanSyoKubun' : 35,// 参照区分
	'sanSyoKubunJAN' : 36,// 参照JAN
	'dBFlg' : 37,// DB対象フラグ
	'systemType' : 38,// システム区分
	'orderType' : 39,// 推奨方法
	'autoOrderType' : 40,// 自動区分
	'texts' : 41,
	'isDelFlg' : 42//add zhangyunfeng 削除FLG 20190611
// 備考
}
var TemplateMainGrid;

/*
 * セルのセレクトオプションの初期化 DataGridの初期化方法設定 DataGridの初期化 @method getTemplateMainGrid
 * @for null @param{array[]} p1 センター p2 発注センター p3 継続区分 p4 発注区分 p5 条件 p6 単位 p7
 * DB対象フラグ p8 推奨方法 p9 システム区分 p10 自動区分 @return null
 */
var getTemplateMainGrid = function(arrCenter,arrContinue,arrOrderFlag,arrOrder,arrTerms,arrOrderunit,arrDBFlag,arrSystemType,arrOrderType,arrAutoOrderType){
	
	// センター
	var cArr = [];
	// 発注センター
	var oArr = [];
	// 継続区分
	var conArr = [];
	// 発注区分
	var ofArr = [];
	// 条件
	var tArr = [];
	// 単位
	var ouArr = [];
	// DB対象フラグ
	var dbfArr = [];
	// 推奨方法
	var ordertypeArr = [];
	// システム区分
	var systemtypeArr = [];
	// 自動区分
	var autoordertypeArr = [];
	
	for (var i=0;i<arrCenter.length;i++){
		cArr.push({
			id : arrCenter[i].storeHouseCD,
			label : arrCenter[i].storeHouseCD+"-"+arrCenter[i].storeHouseName
		});
	}
	for (var i=0;i<arrOrder.length;i++){
		oArr.push({
			id : arrOrder[i].orderCenterCD,
			label : arrOrder[i].orderCenterCD+"-"+arrOrder[i].orderCenterName
		});
	}
	for (var i=0;i<arrContinue.length;i++){
		conArr.push({
			id : arrContinue[i].continuCD,
			label : arrContinue[i].continuCD+"-"+arrContinue[i].continuName
		});
	}
	for (var i=0;i<arrOrderFlag.length;i++){
		ofArr.push({
			id : arrOrderFlag[i].orderFlgCD,
			label : arrOrderFlag[i].orderFlgCD+"-"+arrOrderFlag[i].orderFlgName
		});
	}
	for (var i=0;i<arrTerms.length;i++){
		tArr.push({
			id : arrTerms[i].termsCD,
			label : arrTerms[i].termsCD+"-"+arrTerms[i].terms
		});
	}
	for (var i=0;i<arrOrderunit.length;i++){
		ouArr.push({
			id : arrOrderunit[i].orderUnitName,
			label : arrOrderunit[i].orderUnitName
		});
	}
	for (var i=0;i<arrDBFlag.length;i++){
		dbfArr.push({
			id : arrDBFlag[i].dBFlagCD,
			label : arrDBFlag[i].dBFlagCD+"-"+arrDBFlag[i].dBFlagName
		});
	}
	for (var i=0;i<arrOrderType.length;i++){
		ordertypeArr.push({
			id : arrOrderType[i].orderType,
			label : arrOrderType[i].orderType+"-"+arrOrderType[i].orderTypeName
		});
	}
	for (var i=0;i<arrSystemType.length;i++){
		systemtypeArr.push({
			id : arrSystemType[i].systemType,
			label : arrSystemType[i].systemType+"-"+arrSystemType[i].systemTypeName
		});
	}
	for (var i=0;i<arrAutoOrderType.length;i++){
		autoordertypeArr.push({
			id : arrAutoOrderType[i].autoOrderTypeCD,
			label : arrAutoOrderType[i].autoOrderTypeCD+"-"+arrAutoOrderType[i].autoOrderTypeName
		});
	}
	var colWidths;
	var fixedColumnsLeftNum = 8;
	var columnsArr = [{
			        	data: 'chkFlg',    	
			        	type: 'checkbox'
			        },{// CheckBox
						data : 'deliveryPlanStartDate',
						type : 'date',
						dateFormat : 'YYYY/MM/DD',
						allowEmpty : true,
						datePickerConfig : {
							firstDay : 1,
							disableDayFn: function(date) {
				        	  return new Date(date)<
						       		   new Date(new Date(RegistryGlobal.CreateDate.substring(0,10)));
					        },
							i18n : {
								previousMonth : 'Previous Month',
								nextMonth : 'Next Month',
								months : [ '一月', '二月', '三月', '四月', '五月', '六月', '七月',
										'八月', '九月', '十月', '十一月', '十二月' ],
								weekdays : [ '日曜日', '月曜日', '火曜日', '水曜日', '木曜日', '金曜日',
										'土曜日' ],
								weekdaysShort : [ '日', '月', '火', '水', '木', '金', '土' ]
							}
						}
					},// 適用開始日
					{
						data : 'storeHouse',
		                editor: "chosen",
		                chosenOptions: {
		                    data: cArr
		                }
		            },// センターCD
					{data : 'dIV', type : 'text', readOnly : true},// Div
					{data : 'line',type : 'text',readOnly : true},// Line
					{data : 'department',type : 'text',readOnly : true},// department
					{data : 'jAN',type : 'text',readOnly : false},// JAN
					{data : 'productName',type : 'text',readOnly : true},// 商品名
					{data : 'specName',type : 'text',readOnly : true},// 規格
					{data : 'shipptype',type : 'text',readOnly : true},// 物流タイプ
					{data : 'orderCenter',editor: "chosen"
						,chosenOptions: {
		                    data: oArr
		                }
					},// 発注センター
					{data : 'nohiSaki',type : 'text',readOnly : true},// 納品先
					{data : 'orderFlg',type : 'text',readOnly : true},// 発注フラグ
					{data : 'supplierCD',type : 'text',readOnly : true},// ベンダー
					{data : 'mixID',type : 'text',readOnly : false},// 混載ID
					{data : 'titleName',type : 'text',readOnly : false},// 代表商品名
					{data : 'qput',type : 'text',readOnly : false},// 入数
					{data : 'qunt',editor: "chosen",
		                chosenOptions: {
		                    data: ouArr
		                }
					},// 単位
					{data : 'qty',type : 'text',readOnly : false},// 数量
					{data : 'qflg',editor: "chosen"
						,chosenOptions: {
		                    data: tArr
		                }
					},// 条件
					{data : 'jyuRru',type : 'text',readOnly : false},// 重量
					{data : 'taiSeki',type : 'text',readOnly : false},// 体積
					{data : 'stockQty',type : 'text',readOnly : false},// 安全在庫日数
					{data : 'syokaQty',type : 'text',readOnly : false},// 基準消化日数
					{data : 'orderKikan',type : 'text',readOnly : false},// 発注間隔
					{data : 'nichi',type : 'text',readOnly : false},// 日
					{data : 'getu',type : 'text',readOnly : false},// 月
					{data : 'kayo',type : 'text',readOnly : false},// 火
					{data : 'siu',type : 'text',readOnly : false},// 水
					{data : 'moku',type : 'text',readOnly : false},// 木
					{data : 'kin',type : 'text',readOnly : false},// 金
					{data : 'doyo',type : 'text',readOnly : false},// 土
					{data : 'kiJun',type : 'text',readOnly : false},// 基準
					{data : 'jiXuKubun',editor: "chosen",
						chosenOptions: {
		                    data: conArr
		                }
					},// 継続区分
					{data : 'orderKubun',editor: "chosen",
		                chosenOptions: {
		                    data: ofArr
		                }
					},// 発注区分
					{data : 'sanSyoKubun',editor: "chosen",
		                chosenOptions: {
		                    data: [
		                           {
		                               id: "1",
		                               label: "1-新規"
		                           }, {
		                               id: "2",
		                               label: "2-改廃"
		                           }
		                       ]
		                }
					},// 参照区分
					{data : 'sanSyoKubunJAN',type : 'text',readOnly : false},// 参照JAN
					{data : 'dBFlg',editor: "chosen",
						chosenOptions: {
		                    data: dbfArr
		                }
					}, // DB対象フラグ
					{data : 'systemType',
		                editor: "chosen",
		                chosenOptions: {
		                    data: systemtypeArr
		                }
					}, // システム区分
					{
						data : 'orderType',
		                editor: "chosen",
		                chosenOptions: {
		                    data: ordertypeArr
		                }
					}, // 推奨方法
					{
						data : 'autoOrderType',
		                editor: "chosen",
		                chosenOptions: {
		                    data: autoordertypeArr
		                }
					}, // 自動区分
					{
						data : 'texts',
						type : 'text',
						readOnly : false
					}, // 備考
					{data : 'isDelFlg',type : 'text',readOnly : true} //add zhangyunfeng 削除FLG 20190611
			];
	var colmerg = [ {
					row : 0,
					col : 0,
					rowspan : 2,
					colspan : 1
				},  {
					row : 0,
					col : 1,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 2,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 3,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 4,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 5,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 6,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 8,
					rowspan : 2,
					colspan : 1
				},{
					row : 0,
					col : 8,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 9,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 10,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 11,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 12,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 13,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 14,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 15,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 16,
					rowspan : 1,
					colspan : 2
				}, {
					row : 0,
					col : 18,
					rowspan : 1,
					colspan : 2
				}, {
					row : 0,
					col : 20,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 21,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 22,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 23,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 24,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 25,
					rowspan : 1,
					colspan : 8
				}, {
					row : 0,
					col : 33,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 34,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 35,
					rowspan : 1,
					colspan : 2
				}, {
					row : 0,
					col : 37,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 38,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 39,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 40,
					rowspan : 2,
					colspan : 1
				}, {
					row : 0,
					col : 41,
					rowspan : 2,
					colspan : 1
				} , {
					row : 0,
					col : 42,
					rowspan : 2,
					colspan : 1
				} ];
	if (RegistryGlobal.venderCD != "") {
		colWidths = [ 40, 90, 90, 90, 110, 110, 60, 60, 110, 110, 80,
						110, 80, 110, 60, 60, 60, 60, 80, 80, 80, 80, 80, 40, 40, 40,
						40, 40, 40, 40, 40, 80, 80, 110, 110, 80, 80, 80, 80, 80 ,0.01];
		columnsArr = [{
			        	data: 'chkFlg',    	
			        	type: 'checkbox'
			        },{// CheckBox
						data : 'deliveryPlanStartDate',
						type : 'date',
						dateFormat : 'YYYY/MM/DD',
						allowEmpty : true,
						datePickerConfig : {
							firstDay : 1,
							disableDayFn: function(date) {
				        	  return new Date(date)<
						       		   new Date(new Date(RegistryGlobal.CreateDate.substring(0,10)));
					        },
							i18n : {
								previousMonth : 'Previous Month',
								nextMonth : 'Next Month',
								months : [ '一月', '二月', '三月', '四月', '五月', '六月', '七月',
										'八月', '九月', '十月', '十一月', '十二月' ],
								weekdays : [ '日曜日', '月曜日', '火曜日', '水曜日', '木曜日', '金曜日',
										'土曜日' ],
								weekdaysShort : [ '日', '月', '火', '水', '木', '金', '土' ]
							}
						}
					},// 適用開始日
					{data : 'storeHouse',editor: "chosen",
						chosenOptions: {
			                data: cArr
			            }
			        },// センターCD
					{data : 'dIV',type : 'text',readOnly : true},// Div
					{data : 'jAN',type : 'text',readOnly : false},// JAN
					{data : 'productName',type : 'text',readOnly : true},// 商品名
					{data : 'specName',type : 'text',readOnly : true},// 規格
					{data : 'shipptype',type : 'text',readOnly : true},// 物流タイプ
					{data : 'orderCenter',editor: "chosen",
			            chosenOptions: {
			                data: oArr
			            }
					},// 発注センター
					{data : 'nohiSaki',type : 'text',readOnly : true},// 納品先
					{data : 'orderFlg',type : 'text',readOnly : true},// 発注フラグ
					{data : 'supplierCD',type : 'text',readOnly : true},// ベンダー
					{data : 'mixID',type : 'text',readOnly : false},// 混載ID
					{data : 'titleName',type : 'text',readOnly : false},// 代表商品名
					{data : 'qput',type : 'text',readOnly : false},// 入数
					{data : 'qunt',editor: "chosen",
			            chosenOptions: {
			                data: ouArr
			            }
					},// 単位
					{data : 'qty',type : 'text',readOnly : false},// 数量
					{data : 'qflg',editor: "chosen",
			            chosenOptions: {
			                data: tArr
			            }
					},// 条件
					{data : 'jyuRru',type : 'text',readOnly : false},// 重量
					{data : 'taiSeki',type : 'text',readOnly : false},// 体積
					{data : 'stockQty',type : 'text',readOnly : false},// 安全在庫日数
					{data : 'syokaQty',type : 'text',readOnly : false},// 基準消化日数
					{data : 'orderKikan',type : 'text',readOnly : false},// 発注間隔
					{data : 'nichi',type : 'text',readOnly : false},// 日
					{data : 'getu',type : 'text',readOnly : false},// 月
					{data : 'kayo',type : 'text',readOnly : false},// 火
					{data : 'siu',type : 'text',readOnly : false},// 水
					{data : 'moku',type : 'text',readOnly : false},// 木
					{data : 'kin',type : 'text',readOnly : false},// 金
					{data : 'doyo',type : 'text',readOnly : false},// 土
					{data : 'kiJun',type : 'text',readOnly : false},// 基準
					{data : 'jiXuKubun',editor: "chosen",
			            chosenOptions: {
			                data: conArr
			            }
					},// 継続区分
					{data : 'orderKubun',editor: "chosen",
			            chosenOptions: {
			                data: ofArr
			            }
					},// 発注区分
					{data : 'sanSyoKubun',
			            editor: "chosen",
			            chosenOptions: {
			                data: [{id: "1",label: "1-新規"}, 
			                       {id: "2",label: "2-改廃"}]
			            }
					},// 参照区分
					{data : 'sanSyoKubunJAN',type : 'text',readOnly : false},// 参照JAN
					{data : 'dBFlg',editor: "chosen",
			            chosenOptions: {
			                data: dbfArr
			            }
					}, // DB対象フラグ
					{data : 'systemType',editor: "chosen",
			            chosenOptions: {
			                data: systemtypeArr
			            }
					}, // システム区分
					{data : 'orderType',editor: "chosen",
			            chosenOptions: {
			                data: ordertypeArr
			            }
					}, // 推奨方法
					{data : 'autoOrderType',editor: "chosen",
			            chosenOptions: {
			                data: autoordertypeArr
			            }
					}, // 自動区分
					{data : 'texts',type : 'text',readOnly : false}, // 備考
					{data : 'isDelFlg',type : 'text',readOnly : true} //削除FLG
			];
		fixedColumnsLeftNum = 6;
		listCol = {
				'chkFlg' : 0,// Checkbox
				'deliveryPlanStartDate' : 1,// 適用開始日
				'storeHouse' : 2,// センターCD
				'dIV' : 3,// Div
				'jAN' : 4,// JAN
				'productName' : 5,// 商品
				'specName' : 6,// 規格
				'shipptype' : 7,// 物流タイプ
				'orderCenter' : 8,// 発注センター
				'nohiSaki' : 9,// 納品先
				'orderFlg' : 10,// 発注フラグ
				'supplierCD' : 11,// ベンダー
				'mixID' : 12,// 混載ID
				'titleName' : 13,// 代表商品名
				'qput' : 14,// 入数
				'qunt' : 15,// 単位
				'qty' : 16,// 数量
				'qflg' : 17,// 条件
				'jyuRru' : 18,// 重量
				'taiSeki' : 19,// 体積
				'stockQty' : 20,// 安全在庫日数
				'syokaQty' : 21,// 基準消化日数
				'orderKikan' : 22,// 発注間隔
				'nichi' : 23,// 日
				'getu' : 24,// 月
				'kayo' : 25,// 火
				'siu' : 26,// 水
				'moku' : 27,// 木
				'kin' : 28,// 金
				'doyo' : 29,// 土
				'kiJun' : 30,// 基準
				'jiXuKubun' : 31,// 継続区分
				'orderKubun' : 32,// 発注区分
				'sanSyoKubun' : 33,// 参照区分
				'sanSyoKubunJAN' : 34,// 参照JAN
				'dBFlg' : 35,// DB対象フラグ
				'systemType' : 36,// システム区分
				'orderType' : 37,// 推奨方法
				'autoOrderType' : 38,// 自動区分
				'texts' : 39,
				'isDelFlg' : 40//伸び率
			// 備考
			};
		colmerg = [ {
						row : 0,
						col : 0,
						rowspan : 2,
						colspan : 1
					},  {
						row : 0,
						col : 1,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 2,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 3,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 4,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 6,
						rowspan : 2,
						colspan : 1
					},{
						row : 0,
						col : 6,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 7,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 8,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 9,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 10,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 11,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 12,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 13,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 14,
						rowspan : 1,
						colspan : 2
					}, {
						row : 0,
						col : 16,
						rowspan : 1,
						colspan : 2
					}, {
						row : 0,
						col : 18,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 19,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 20,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 21,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 22,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 23,
						rowspan : 1,
						colspan : 8
					}, {
						row : 0,
						col : 31,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 32,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 33,
						rowspan : 1,
						colspan : 2
					}, {
						row : 0,
						col : 35,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 36,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 37,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 38,
						rowspan : 2,
						colspan : 1
					}, {
						row : 0,
						col : 39,
						rowspan : 2,
						colspan : 1
					}, {//削除FLG追加
						row : 0,
						col : 40,
						rowspan : 2,
						colspan : 1
					} ];
		
	} else {
		colWidths = [ 40, 90, 90, 90, 90, 90, 110, 110, 60, 60, 110, 110, 80,
				110, 80, 110, 60, 60, 60, 60, 80, 80, 80, 80, 80, 40, 40, 40,
				40, 40, 40, 40, 40, 80, 80, 110, 110, 80, 80, 80, 80, 80,0.01];
	}
	TemplateMainGrid = {
		tbhot : null,
		colWidths : colWidths,
		columns : columnsArr,
		// create grid
		CreatHotMainGrid : function(hotgrid, arrData) {
			// まず対象内容クリア
			if (this.tbhot) {
				this.tbhot.destroy();
			}
			this.tbhot = new Handsontable(document.getElementById(hotgrid), {
				height : $(".page-content").height() - 265,
				colWidths : this.colWidths,
				columns : this.columns,
				manualColumnResize : true,
				overflow : scroll,
				fillHandle:false,
			    wordWrap:false,
				search : true,
				minSpareRows : 1,
				columnSorting : true,
				sortIndicator : true,
				rowHeaders : false,
				mergeCells : colmerg,
				fixedColumnsLeft : fixedColumnsLeftNum,
				autoColumnSize:true,
				fixedRowsTop : 2,
				currentRowClassName: 'currentRow',
				cells : function(row, col, prop) {
					var cellProperties = {};
					if (row == 0 || row == 1) {
						if(col != listCol.chkFlg)
						cellProperties.readOnly = true;
						cellProperties.renderer = HeaderRender;
					} else {
						cellProperties.renderer = styleRender;
					}
					return cellProperties;
				},
				beforeChange : function(changes, source) {
					if(changes!=null){  
						for(var ii=0;ii<changes.length;ii++){
							var index = changes[ii][0];//行
		                    var colHead = changes[ii][1];//列
		                    var oldvalue = changes[ii][2];
		                    var newvalue = changes[ii][3];
		                    if(typeof(oldvalue)=="undefined"&&(newvalue==""||newvalue==null)){  	
		                    }else{
		                    	if(index>1&&RegistryGlobal.showList.length>index&&typeof(RegistryGlobal.showList[index].chkFlg)=="undefined"){
			                    	RegistryGlobal.showList[index].chkFlg = false;
			                    }
		                    }
						}
	                }
				},
				afterChange : function(changes, source) {
					if (source === 'loadData'||source ==='external') {
		                return; //don't save this change
		            }
                    if(changes!=null){  
                    	for(var ii=0;ii<changes.length && changes[ii].length==4;ii++){
                    		var index = changes[ii][0];//行
    	                    var colHead = changes[ii][1];//列
    	                    var oldvalue = changes[ii][2];
    	                    var newvalue = changes[ii][3];
    	                    if(typeof(colHead)=="undefined"
                    	        ||!listCol.hasOwnProperty(colHead)
    	                    	||((typeof(oldvalue)=="undefined"||oldvalue==""|| oldvalue==null)&& (typeof(newvalue)=="undefined"||newvalue==""|| newvalue==null))
    	                        || newvalue==oldvalue) {
    	                    }else{
    	                    	
	    	                    newvalue = studioFilter(newvalue,colHead);

    	                    	RegistryGlobal.showList[index][colHead]=newvalue;
    	                    	TemplateMainGrid.tbhot.setDataAtRowProp(index,colHead,newvalue);
    							removeRangeCellClass(index, listCol[colHead], "tdBgColor");

	    	                    if(colHead == "chkFlg"){
	    	                    	if(newvalue==false) removeByValue(saveArr, index);
	    	                    	if(index == 0){
	    	                    		saveArr = [];
	    	                    		for(var i=2;i<RegistryGlobal.showList.length;i++){
	    	                    			if((RegistryGlobal.showList[i].chkFlg == false || RegistryGlobal.showList[i].chkFlg == true)
												&&"1"!=RegistryGlobal.showList[i].isDelFlg){
	    	                    				RegistryGlobal.showList[i].chkFlg = RegistryGlobal.showList[0].chkFlg;
	    	                    				saveArr.push(i);
	    	                    			}
	    	                    		}
	    	                    		TemplateMainGrid.tbhot.render();
	    	                    		if(RegistryGlobal.showList[0].chkFlg == false) saveArr = [];
	    	                    	}else{
	    	                    		if(RegistryGlobal.showList[index].chkFlg == false){
	    	                    			RegistryGlobal.showList[0].chkFlg = false;
	    	                    		}else{
	    	                    			var chk = true;
	    	                    			for(var i=2;i<RegistryGlobal.showList.length;i++){
	    		                    			if(RegistryGlobal.showList[i].chkFlg == false&&"1"!=RegistryGlobal.showList[i].isDelFlg){
	    		                    				chk = false;
	    		                    				break;
	    		                    			}
	    		                    		}
	    	                    			RegistryGlobal.showList[0].chkFlg = chk;
	    	                    			if(saveArr.indexOf(index) == -1){
	    				                    	saveArr.push(index);
	    				                    }
	    	                    		}
	    	                    	}
	    	                    }else{
	
	    	                    	if(saveArr.length == 0){
	    		                    	saveArr.push(index);
	    		                    	RegistryGlobal.showList[index].chkFlg = true;
	    		                    }else{
	    		                    	if(saveArr.indexOf(index) == -1){
	    			                    	saveArr.push(index);
	    			                    	RegistryGlobal.showList[index].chkFlg = true;
	    			                    }
	    		                    }
	    	                    	if(colHead == "deliveryPlanStartDate" || colHead == "storeHouse" || colHead == "jAN"){
	    					    		getInputData(index);
	    		                    }
	    	                    }
	    	                
                    	    }
    	                    TemplateMainGrid.tbhot.render();
                    	}
                    }
				},
				afterSelectionByProp : function(r, p, r2, p2) {
				}
			});
			this.tbhot.loadData(arrData);
			this.tbhot.render();
		}
	}
	arrDataInit();
	//var result = eval(arrData);
	RegistryGlobal.showList = arrData[0];
	TemplateMainGrid.CreatHotMainGrid('MainGrid',RegistryGlobal.showList);
}

var studioFilter = function(data, colHead) {
	if(colHead != "deliveryPlanStartDate"){
		data = strRegFilter(data);//[,|'|\"|%|!|;|<|>|?]
	}
	switch (listCol[colHead]) {
		case listCol.deliveryPlanStartDate:// 適用開始日
			data = dateDeal(data);
			break;
		case listCol.jAN:// JAN
			data = numLen(data, 13);
			break;
		case listCol.mixID:// 混載ID
			data = numLen(data, 9);
			break;
		case listCol.titleName:// 代表商品名
			data = strLen(data, 50);
			break;
		case listCol.qput:// 入数
		case listCol.qty:// 数量
			data = numLen(data, 8);
			break;
		case listCol.jyuRru:// 重量
		case listCol.taiSeki:// 体積
			data = toDecimal(data, 4);
			break;
		case listCol.stockQty:// 安全在庫日数
		case listCol.syokaQty:// 基準消化日数
		case listCol.orderKikan:// 発注間隔
			data = numLen(data, 8);
			break;
		case listCol.nichi:// 日
		case listCol.getu:// 月
		case listCol.kayo:// 火
		case listCol.siu:// 水
		case listCol.moku:// 木
		case listCol.kin:// 金
		case listCol.doyo:// 土
			data = numLen(data, 8);
			break;
		case listCol.kiJun:// 基準
			data = numLen(data, 8);
			break;
		case listCol.sanSyoKubunJAN:// 参照JAN
			data = numLen(data, 13);
			break;
		case listCol.texts:// 備考
			data = strLen(data, 128);
			break;
	}
	return data;
}

var compareDate = function(val) {
	return ((new Date()) < (new Date(val)));
}
var toDecimal = function(val, decLen) {
	if (val.indexOf(".") != -1 && val.indexOf(".") > 6) {
		return "";
	}
	if (val.indexOf(".") == -1 && val.length > 6) {
		return "";
	}
	var f = parseFloat(val);
	if (isNaN(f)) {
		return "";
	}
	var s = val.toString();
	var rs = s.indexOf('.');
	if (rs < 0) {
		rs = s.length;
		s += '.';
	}
	while (s.length <= rs + decLen) {
		s += '0';
	}
	s = s.substring(0, rs + decLen + 1);
	s = s.substring(0, 11);

	return s;
}
var strLen = function(val, len) {
	val = val.substring(0, len);

	return val;
}
var numLen = function(val, len) {
	val = val.replace(/[^0-9]/ig, "");
	val = val.substring(0, len);

	return val;
}
// 配列の削除
var removeByValue = function(arr, val) {
	for (var i = 0; i < arr.length; i++) {
		if (arr[i] == val) {
			arr.splice(i, 1);
			break;
		}
	}
}
// 適用開始日とセンターとJANで情報を獲得
var getInputData = function(row){
	
	//適用開始日
	var val0 = RegistryGlobal.showList[row].deliveryPlanStartDate;
	if(val0==null||val0=="null"){val0="";}
	//センター
	var val1 = RegistryGlobal.showList[row].storeHouse;
	//JAN
	var val5 = RegistryGlobal.showList[row].jAN;
	
	if(val1!=null&&val5!=null&&val1!=""&&val5!=""&&val1!="null"&&val5!="null"){
		$("#loadMask").show();
		$.ajax({
			url:SystemManage.address+'SupportPageControl/getinputdata',
			type:'POST',
			data:{
				userCD:RegistryGlobal.userCD,
				venderCD:RegistryGlobal.venderCD,
				indata:val0+","+val1.split("-")[0]+","+val5
			},
			success:function(response){
				$("#loadMask").hide();
				if(response =="false"){
					showMsgBox(Message.msg043,['データ取得'],function(){});
		    	}else{
		    		if(response == "[[]]"){
		    			//showMsgBox(Message.msg053,[],function(){});
		    		}else{
		    			var result = JSON.parse(response);	
			    		var rstJson = result[0][0];

			    		RegistryGlobal.showList[row] = rstJson;
			    		RegistryGlobal.showList[row].chkFlg = true;
			    		removeAllCellClass(row,"tdBgColor");
			    		TemplateMainGrid.tbhot.render();
		    		}
		    	}
			},
			error:function(){
				$("#loadMask").hide();
				showMsgBox(Message.msg043,['データ取得'],function(){});
			}
		});
	}
}
var HeaderRender = function(instance, td, row, col, prop, value, cellProperties) {
	if (col != listCol.chkFlg) {
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		// Header設定
		if (row == 0 || row == 1) {
			td.style.backgroundColor = 'lightcyan';
			td.style.textAlign = "center";
			td.style.textOverflow = 'ellipsis';
			td.style.whiteSpace = 'nowrap';
			td.style.overflow = 'hidden';
			td.title = value;
		}
	} else {
		Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
		// Header設定
		if (row == 0 || row == 1) {
			td.style.backgroundColor = 'lightcyan';
			td.style.textAlign = "center";
			td.style.textOverflow = 'ellipsis';
			td.style.whiteSpace = 'nowrap';
			td.style.overflow = 'hidden';
		}
	}
	if (col == listCol.productName && row == 0) {
		td.style.borderBottom= "1px solid lightcyan";
	}

	return td;
}
var styleRender = function(instance, td, row, col, prop, value, cellProperties) {
	//CheckBox
	if (col == listCol.chkFlg) {
		Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
		td.style.textAlign = "center";
		//センター、発注センター、単位、条件、基準、発注区分、参照区分、DB対象フラグ、システム区分、推奨方法、自動区分
	} else if (col == listCol.storeHouse || col == listCol.orderCenter
			|| col == listCol.qunt || col == listCol.qflg
			|| col == listCol.jiXuKubun || col == listCol.orderKubun
			|| col == listCol.dBFlg || col == listCol.systemType
			|| col == listCol.orderType || col == listCol.autoOrderType
			|| col == listCol.sanSyoKubun) {
		
		td.title = value;
		
		if(value == null) {
			Handsontable.TextCell.renderer(instance, td, row, col, prop, value,
					cellProperties);
		}else{
			var selectedId;
			var optionsList = cellProperties.chosenOptions.data;

			if (typeof optionsList === "undefined"
					|| typeof optionsList.length === "undefined"
					|| !optionsList.length) {
				Handsontable.TextCell.renderer(instance, td, row, col, prop, value,
						cellProperties);
				return td;
			}

			var values = (value + "").split(",");
			value = [];
			for (var index = 0; index < optionsList.length; index++) {

				if (values.indexOf(optionsList[index].id + "") > -1) {
					selectedId = optionsList[index].id;
					value.push(optionsList[index].label);
				}
			}
			value = value.join(", ");
			
			Handsontable.TextCell.renderer(instance, td, row, col, prop, value,
					cellProperties);
			
			if(TemplateMainGrid.tbhot!=null && value==""){
				TemplateMainGrid.tbhot.setDataAtCell(row, col, null);
			}
		}
		
	}else if(col == listCol.deliveryPlanStartDate){
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		if(!value||isDate(convertToCorrectDate(value))){				
			$(arguments[1]).removeClass("htInvalid");
		}
		if(value && !isDate(value)){
			value = "";
		}
	} else {
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		td.title = value;
	}
	if (col == listCol.qput || col == listCol.qty
			|| col == listCol.jyuRru || col == listCol.taiSeki
			|| col == listCol.stockQty || col == listCol.syokaQty
			|| col == listCol.orderKikan || col == listCol.nichi
			|| col == listCol.getu || col == listCol.kayo
			|| col == listCol.siu || col == listCol.moku
			|| col == listCol.kin || col == listCol.doyo
			|| col == listCol.kiJun ) {
		
		td.style.textAlign = "right";
	}
	var dataSource=[];
	if(TemplateMainGrid.tbhot!=null){
		dataSource=TemplateMainGrid.tbhot.getSourceData();
	}
	if(dataSource.length>0 && dataSource[row]&&!isEmpty(dataSource[row].isDelFlg)&&"1"==dataSource[row].isDelFlg ){//削除FLG
		cellProperties.readOnly = true;
		td.style.backgroundColor = "#EEEEEE";
	}else{
		//表示列は背景色を黄色に設定。
		switch (col) {
			case listCol.dIV:
				td.style.background = '#FFF';
				break;
			case listCol.line:
				td.style.background = '#FFF';
				break;
			case listCol.department:
				td.style.background = '#FFF';
				break;
			case listCol.productName:
				td.style.background = '#FFF';
				break;
			case listCol.specName:
				td.style.background = '#FFF';
				break;
			case listCol.shipptype:
				td.style.background = '#FFF';
				break;
			case listCol.nohiSaki:
				td.style.background = '#FFF';
				break;
			case listCol.orderFlg:
				td.style.background = '#FFF';
				break;
			case listCol.supplierCD:
				td.style.background = '#FFF';
				break;
			default:
				td.style.background = '#FFFF99';
				break;
		}
	}
	td.style.verticalAlign= 'middle';
	return td;
}
