<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>イベント設定画面</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width, initial-scale=1" name="viewport"/>
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Cache-Control" content="no-cache">
	<meta http-equiv="Expires" content="0">
	<link rel="icon" type="image/png/ico" href="../Common/favicon.ico">
	<!-- basic styles -->
	<link href="../Common/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <link href="../Common/common.css" rel="stylesheet">
    <link href="../Common/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/bootstrap/css/bootstrap.min.css" rel="stylesheet">
	<link href="../Common/css/layout.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/css/themes/light2.css" rel="stylesheet" type="text/css" id="style_color"/>
	<link href="../Common/bootstrap-multiselect/css/bootstrap-multiselect.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/bootstrap-datepicker/css/bootstrap-datepicker3.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/handsontable/handsontable.full.min.css" rel="stylesheet"/>
	<link href="../Common/handsontable/chosen.css" rel="stylesheet"/>
	<link href="../Common/jan/jan.css" rel="stylesheet"/>
	<link href="../Common/prettify/prettify.css" rel="stylesheet"/>
	<link href="../Common/zTree/css/zTreeStyle.css" rel="stylesheet"/>
	<link href="css/style.css" rel="stylesheet" type="text/css" />
	
    <script src="../Common/jquery/jquery.min.js" type="text/javascript"></script>
	<script src="../Common/bootstrap-multiselect/js/bootstrap-multiselect.js" type="text/javascript" ></script>
	<script src="../Common/handsontable/handsontable.full.min.js" type="text/javascript" ></script>
	<script src="../Common/handsontable/chosen.jquery.js" type="text/javascript" ></script>
    <script src="../Common/handsontable/handsontable-chosen-editor.js" type="text/javascript"></script>
    <script src="../Common/prettify/prettify.js" type="text/javascript" ></script>
    <script src="../Common/zTree/js/jquery.ztree.core-3.5.min.js" type="text/javascript" ></script>
    <script src="../Common/zTree/js/jquery.ztree.excheck-3.5.min.js" type="text/javascript" ></script>
	<script>document.write("<s"+"cript src='../Common/version.js?ver="+Math.random()+"'></scr"+"ipt>"); </script>
	<script>document.write("<s"+"cript src='../Common/common.js?ver="+sysversion+"'></scr"+"ipt>");</script>
	<link rel="shortcut icon" href="../Common/favicon.ico"/>	
</head>
<body class="page-container-bg-solid">
<script>
	var href=window.document.location.href;
	var rankNum=getRank(href);
</script>

<!-- BEGIN CONTAINER -->
<div class="page-container">
	<!-- BEGIN CONTENT -->
	<div class="page-content-wrapper">
		<div class="page-content">
			<!--#検索条件 form-->
			<div class="form-horizontal">
				<form class="form-group" style="margin-top: 15px;">
					<div class="btn-group" role="group">
						<label class="lblEventWidth">イベント名</label>
					</div>
					<div class="btn-group selWidth" role="group">
						<select id="divEventNameMulti" class="form-control" multiple="multiple"></select>
					</div>
				</form>
				<form class="form-group">
					<div class="btn-group" role="group">
						<label class="lblEventWidth">イベント開始日</label>
					</div>
					<div class="btn-group selWidth" role="group">
						<input type='text' class="form-control" id="StartDate" />
					</div>
					<div class="btn-group" role="group">
						<label class="lblEventWidth">イベント終了日</label>
					</div>
					<div class="btn-group selWidth" role="group">
						<input type='text' class="form-control" id="EndDate" />
					</div>
					<div class="btn-group btnWidth" role="group">
						<input id="btn_search" type="button"
							class="btn btn-primary btn-block" value="検索">
					</div>
					<div class="btn-group btnWidth" role="group">
						<input id="btn_Save" type="button"
							class="btn btn-primary btn-block" value="登録">
					</div>
					<div class="btn-group btnWidth" role="group">
						<input id="btn_Delete" type="button"
							class="btn btn-primary btn-block" value="削除">
					</div>
				</form>
			</div>
			<!--/検索条件 form-->
			<div class="col-md-12 col-sm-12 col-xs-12" id="grid" style="padding: 0px 10px 10px 10px;">
				<div id="MainGrid" style="width: 100%"></div>
			</div>
		</div>
	</div>
	<!-- END CONTENT -->
</div>
<!-- END CONTAINER -->

<!-- BEGIN FOOTER -->
<div class="page-footer" style="display: none; height: 4px; padding: 0px; margin: 0px;">
</div>
<!-- END FOOTER -->

<!--メッセージ表示-->
<div class="modal fade" id="branchDivDialog" tabindex="-2" role="dialog" aria-labelledby="" style="z-index: 2000;">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-header">
	        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			</button>
	        <h4 class="modal-title" id="branchDialogTitle">店舗選択</h4>
	      </div>
	      <div class="modal-body" >
	      	  <div class="zTreeDemoBackground left">
	      	  	<ul id="branchDialog" class="ztree"></ul>
	      	  </div>
	      </div>
	      <div class="modal-footer">
	          <button type="button" id="branchDialogYes" class="btn btn-default" data-dismiss="">OK</button>
	          <button type="button" id="branchDialogNo" class="btn btn-default" data-dismiss="modal">キャンセル</button>
	      </div>
	    </div>
	  </div>
</div>

<!--メッセージ表示-->
<div class="modal fade" id="myMessage" tabindex="-2" role="dialog" aria-labelledby="" style="z-index: 2000;margin-top:100px;">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-header">
	        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span>
			</button>
	        <h4 class="modal-title" id="myMessageTitle">コンサイメントイベント設定画面</h4>
	      </div>
	      <div class="modal-body" >
	      	  <p id="myMessageBody"></p>
	      </div>
	      <div class="modal-footer">
	          <button type="button" id="msgSysBtnYes" class="btn btn-default" data-dismiss="">OK</button>
	          <button type="button" id="msgSysBtnNo" class="btn btn-default" data-dismiss="modal">キャンセル</button>
	      </div>
	    </div>
	  </div>
</div>

<script src="../Common/jquery/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
<script src="../Common/bootstrap/js/bootstrap.min.js"></script>
<script src="../Common/bootstrap-datepicker/js/bootstrap-datepicker.js" type="text/javascript" ></script>
<script src="../Common/bootstrap-datepicker/locales/bootstrap-datepicker.ja.min.js" type="text/javascript" ></script>
<script src="../Common/message/message.js"></script>
<script src="../Common/common.js"></script>
<script src="../Common/js/metronic.js" type="text/javascript"></script>
<script src="../Common/js/layout.js" type="text/javascript"></script>
<script>
	jQuery(document).ready(function() {    
	   Metronic.init(); // init metronic core componets
	   Layout.init(); // init layout
	});
	makeProcessing(rankNum);
</script>
<script>document.write("<s"+"cript src='js/gridconfig.js?ver="+sysversion+"'></scr"+"ipt>");</script>
<script>document.write("<s"+"cript src='js/controller.js?ver="+sysversion+"'></scr"+"ipt>");</script>
</body>
<!-- END BODY -->
</html>