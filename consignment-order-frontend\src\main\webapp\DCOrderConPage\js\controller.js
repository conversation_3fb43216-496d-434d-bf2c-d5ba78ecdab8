/**
 * @description: コンサイメント発注専用システム-ロット数追加
 * @修正者: 10047496 zhangyunfeng
 * @修正日: 2018/9/8

 * @修正内容:1）3種類改修、前者の数値変更したら、後者も合わせて、変わるうん
 * ①ロット数　（　ケース数　→　推奨数　一緒に変わる）
 * ②ケース数　（　推奨数　一緒に変わる　）
 * ③推奨数
 * 2）発注済み、再作成案判断追加
 * @修正者: 10047496 zhangyunfeng
 * @修正日: 2019/04/26
 */
var RegistryGlobal = {
	userCD: sessionStorage.getItem("userCD"),
	employeeCD:sessionStorage.getItem("employeeCD"),//20180815追加社員CD　10047496　
	//10105066
	//OrderCaseCreateDate: new Date(),
	OrderCaseCreateDate: new Date().toISOString(),//日付転換
	//10105066 end
	divList: [],
	storeHouseList: [],
	fourWeekModel: {},
	showList: [],
	confirmList: [],
	showDataLength: 0,
	tableData: [],
	tableSourceData: [],
	supplierList:[],//add ベンダー条件追加 zhangyunfeng 2019/04/08
	init: null,
	leftpos:0,
    toppos:0
}
//app start
$(document).ready(function () {
	$("#loadMask").show();
	$(document).ajaxStart(function(){
		$("#loadMask").show();
    }).ajaxStop(function(){
    	$("#loadMask").hide();
    });
	//社員設定
	if(!isEmpty(sessionStorage.getItem("employeeCD"))){
		$("#divExceptPro").attr("style","display:inline;");
	}
	//INIT
	divAndStoreHouseInit();
		
	//div,センター,OrderCaseCreateDate
	getInitDataList();
	
	//検索
	$("#btn_search").on("click",function(){
		tableData_search();
	});
	
	//自動検索
	if("search"==getQueryString("search")){
		tableData_search();
	}
	
	//確定
	$("#btn_confirm").on("click",function(){
		tableData_confirm();
	});
	
	//預託CSV
	$("#btn_csv").on("click",function(){
		tableData_csv();
	});
	
	$("#messageTitle").html(Message.sysTitle);
	
	//JAN条件入力
	$("#janArea").keypress(function(event){
		$("#janArea").val(this.value.replace(/[^\d-.\n]/g,''));
		return ((event.charCode >= 48 && event.charCode <= 57)||(event.charCode == 13));
	});
	$('ul.dropdown-menu').on('click', function(event){
        event.stopPropagation();
    });
	 $("#jan_reload").bind("click", function(){
		 var janlistVal = "";
         var janListTxt=$("#janArea").val();
         if (janlistCheck(janListTxt)){
             var janlistVal = janListTxt.replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
			 var l = janlistVal.split(",").length;
			 if(l>5000){
				 showMsgBox(Message.msg020,[],function(){});//JANは5000件まで入力してください。
			 }else{
				 $("#parse_button").dropdown('toggle');}
	         }else{
	        	 if(janListTxt!=""){
	        		 showMsgBox(Message.msg021,[],function(){});//正しいJANを入力してください。
		         }else{
		        	 $("#parse_button").dropdown('toggle');
		         }
		    }
	 });
});
//JANチェック
janlistCheck=function(janlist){
	var regex = /^\d+([\s,]*\d+)*[\s,]*$/;
    var r = regex.test(janlist.split("...\r\n")[0].replace(/[ ]/g,""));
	return r;
}

//div,センター,OrderCaseCreateDate
var getInitDataList = function(){
	$.ajax({
		url: `${SystemManage.shiniseAddress}Recommend/getInitDataList`,
		type:'POST',
		data:{userCD: RegistryGlobal.employeeCD, venderCD:RegistryGlobal.userCD},
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg001,[],function(){});//初期化失敗しました。
			}else{				
				var result = JSON.parse(response);
				const itemResult = result?.[0] || {}; 
				RegistryGlobal.divList = itemResult.DivisionList;
				RegistryGlobal.storeHouseList = itemResult.CenterList;
				// RegistryGlobal.fourWeekModel = result[2];
				RegistryGlobal.supplierList = itemResult.SupplierList;
				bindData(RegistryGlobal.divList,"divisionCD","divisionName","divMulti","");
				bindData(RegistryGlobal.storeHouseList,"centerCD","centerName","storeHouseMulti","");
				bindData(RegistryGlobal.supplierList,"supplierCD","supplierName","divSupplierMulti","");
				//Grid　初期データロード
				TemplateGrid.CreatHotGrid('dgJiseki',[]);
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg001,[],function(){});//初期化失敗しました。
		}
	});
}

//検索
var tableData_search = function(){
	RegistryGlobal.init = "search";
	chk = "";
	var divStr;//DIV
	var storeHouseStr;//センター
	if(!$("#divMulti").val()){
		divStr = $("#divMulti").val();
	}else{
		divStr = $("#divMulti").val().join(",");
	}
	if(!$("#storeHouseMulti").val()){
		storeHouseStr = $("#storeHouseMulti").val();
	}else{
		storeHouseStr = $("#storeHouseMulti").val().join(",");
	}
	//JAN
	var janStr = $("#janArea").val().replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
	var dataFlg;//推奨数ある商品のみ表示
	if($("#inline1").is(":checked")){
		dataFlg = "1";
	}else{
		dataFlg = "0";
	}
	var venderFlg;//add zhangyunfeng コンサイメント発注商品除外2019/06/05
	if(!isEmpty(sessionStorage.getItem("employeeCD"))&&$("#chkExceptPro").is(":checked")){
		venderFlg = "1";
	}else{
		venderFlg = "0";
	}
    var chkTimesFlg;//add zhangyunfeng 二回発注2019/06/06
    if($("#chkTimesFlg").is(":checked")){
        chkTimesFlg = "1";
    }else{
        chkTimesFlg = "0";
    }
	var vendorStr=$("#divSupplierMulti").val()==null?"":$("#divSupplierMulti").val().join(",");
	$.ajax({
		url: `${SystemManage.shiniseAddress}Recommend/tableDataSearch`,
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		type:'POST',
		data:{divStr: divStr, storeHouseStr: storeHouseStr, janStr: janStr
			, userCD:isEmpty(vendorStr)? RegistryGlobal.userCD:vendorStr
			, dataFlg: dataFlg,employeeCD:RegistryGlobal.employeeCD
			, venderFlg:venderFlg,chkTimesFlg:chkTimesFlg},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
			}else{				
				var result = JSON.parse(response);
				RegistryGlobal.showList = result.Recommendations;
				RegistryGlobal.confirmList = result.NonDeliveryDate;
				RegistryGlobal.showDataLength = RegistryGlobal.showList.length;
				TemplateGrid.CreatHotGrid('dgJiseki',RegistryGlobal.showList);
				if(RegistryGlobal.showList.length>0){				
					RegistryGlobal.OrderCaseCreateDate = RegistryGlobal.showList[0].OrderCaseCreateDate;
				}
			}
		},
		error:function(response, textStatus){
			var mockData = [
    {
        ChkFlg: '0',
        OrderStatus: '未発注',
        Times: '1',
        StoreHouse: 'CTR001 センター1',
        MixCD: 'MX01',
        DIV: 'DIV001',
        JAN: '4901234567890',
        ProductName: '商品A サンプル商品',
        SpecName: '500ml',
        WareHouseQty: 150,
        week1: 25,
        week2: 30,
        week3: 28,
        week4: 32,
        Tweek: 35,
        OrderPlanDate: '2024/11/25',
        DeliveryPlanQty: 100,
        SalesDay: 5,
        CenterSalesDay: 8,
        OrderSalesDay: 12,
        OrderPlanQtyAfter: 80,
        LotQty: '10',
        CaseQty: 20,
        AdjustRate: '105',
        OrderPlanQty: 84,
        DeliveryPlanDate: '2024/11/30',
        FlgConsultJAN: 'A/4901234567890',
        comment: '通常発注',
        OrderCaseID: 'OC001',
        OrderCaseDetailID: 'OCD001',
        VerNO: '1',
        StoreHouseCD: 'CTR001',
        InnerCaseQty: '12',
        ApprovalFlag: '0',
        SplitSetFlg: '0',
        ProLotQtyCheck: '12',
        NotDeliveryCheck: '0',
        AvgDigestQty: '7',
        OrderUnit: 'CS',
        ShippingTypeCD: '1',
        OrderUnitQty: '12',
        LotUnit: 'CS',
        Terms: '30',
        IsVerNODiff: '0',
        CurrentOrderFlg: '0'
    },
    {
        ChkFlg: '1',
        OrderStatus: '発注済',
        Times: '1',
        StoreHouse: 'CTR002 センター2',
        MixCD: 'MX02',
        DIV: 'DIV002',
        JAN: '4901234567891',
        ProductName: '商品B テスト商品',
        SpecName: '1L',
        WareHouseQty: 200,
        week1: 40,
        week2: 45,
        week3: 42,
        week4: 48,
        Tweek: 50,
        OrderPlanDate: '2024/11/26',
        DeliveryPlanQty: 120,
        SalesDay: 4,
        CenterSalesDay: 7,
        OrderSalesDay: 10,
        OrderPlanQtyAfter: 96,
        LotQty: '8',
        CaseQty: 24,
        AdjustRate: '110',
        OrderPlanQty: 106,
        DeliveryPlanDate: '2024/12/01',
        FlgConsultJAN: 'B/4901234567891',
        comment: '急ぎ発注',
        OrderCaseID: 'OC002',
        OrderCaseDetailID: 'OCD002',
        VerNO: '1',
        StoreHouseCD: 'CTR002',
        InnerCaseQty: '24',
        ApprovalFlag: '1',
        SplitSetFlg: '0',
        ProLotQtyCheck: '24',
        NotDeliveryCheck: '0',
        AvgDigestQty: '12',
        OrderUnit: 'CS',
        ShippingTypeCD: '1',
        OrderUnitQty: '24',
        LotUnit: 'CS',
        Terms: '30',
        IsVerNODiff: '0',
        CurrentOrderFlg: '1'
    },
    {
        ChkFlg: '0',
        OrderStatus: '',
        Times: '2',
        StoreHouse: 'CTR001 センター1',
        MixCD: 'MX01',
        DIV: 'DIV003',
        JAN: '4901234567892',
        ProductName: '商品C デモ商品',
        SpecName: '250ml',
        WareHouseQty: 75,
        week1: 15,
        week2: 18,
        week3: 20,
        week4: 22,
        Tweek: 25,
        OrderPlanDate: '2024/11/27',
        DeliveryPlanQty: 60,
        SalesDay: 3,
        CenterSalesDay: 6,
        OrderSalesDay: 9,
        OrderPlanQtyAfter: 48,
        LotQty: '6',
        CaseQty: 12,
        AdjustRate: '95',
        OrderPlanQty: 46,
        DeliveryPlanDate: '2024/12/02',
        FlgConsultJAN: 'C/4901234567892',
        comment: '在庫調整',
        OrderCaseID: 'OC003',
        OrderCaseDetailID: 'OCD003',
        VerNO: '1',
        StoreHouseCD: 'CTR001',
        InnerCaseQty: '6',
        ApprovalFlag: '0',
        SplitSetFlg: '1',
        ProLotQtyCheck: '6',
        NotDeliveryCheck: '0',
        AvgDigestQty: '5',
        OrderUnit: 'CS',
        ShippingTypeCD: '2',
        OrderUnitQty: '6',
        LotUnit: 'CS',
        Terms: '45',
        IsVerNODiff: '1',
        CurrentOrderFlg: '0'
    },
    {
        ChkFlg: '0',
        OrderStatus: '承認待ち',
        Times: '1',
        StoreHouse: 'CTR003 センター3',
        MixCD: 'MX03',
        DIV: 'DIV004',
        JAN: '4901234567893',
        ProductName: '商品D 新商品',
        SpecName: '2L',
        WareHouseQty: 300,
        week1: 60,
        week2: 65,
        week3: 58,
        week4: 70,
        Tweek: 75,
        OrderPlanDate: '2024/11/28',
        DeliveryPlanQty: 150,
        SalesDay: 4,
        CenterSalesDay: 6,
        OrderSalesDay: 8,
        OrderPlanQtyAfter: 120,
        LotQty: '15',
        CaseQty: 30,
        AdjustRate: '100',
        OrderPlanQty: 120,
        DeliveryPlanDate: '2024/12/03',
        FlgConsultJAN: 'D/4901234567893',
        comment: '新商品導入',
        OrderCaseID: 'OC004',
        OrderCaseDetailID: 'OCD004',
        VerNO: '1',
        StoreHouseCD: 'CTR003',
        InnerCaseQty: '8',
        ApprovalFlag: '0',
        SplitSetFlg: '0',
        ProLotQtyCheck: '8',
        NotDeliveryCheck: '0',
        AvgDigestQty: '15',
        OrderUnit: 'CS',
        ShippingTypeCD: '1',
        OrderUnitQty: '8',
        LotUnit: 'CS',
        Terms: '30',
        IsVerNODiff: '0',
        CurrentOrderFlg: '0'
    },
    {
        ChkFlg: '1',
        OrderStatus: '未発注',
        Times: '1',
        StoreHouse: 'CTR004 センター4',
        MixCD: 'MX04',
        DIV: 'DIV005',
        JAN: '4901234567894',
        ProductName: '商品E 限定商品',
        SpecName: '300ml',
        WareHouseQty: 80,
        week1: 12,
        week2: 15,
        week3: 18,
        week4: 20,
        Tweek: 22,
        OrderPlanDate: '2024/11/29',
        DeliveryPlanQty: 50,
        SalesDay: 4,
        CenterSalesDay: 7,
        OrderSalesDay: 10,
        OrderPlanQtyAfter: 40,
        LotQty: '5',
        CaseQty: 10,
        AdjustRate: '120',
        OrderPlanQty: 48,
        DeliveryPlanDate: '2024/12/04',
        FlgConsultJAN: 'E/4901234567894',
        comment: '限定販売',
        OrderCaseID: 'OC005',
        OrderCaseDetailID: 'OCD005',
        VerNO: '1',
        StoreHouseCD: 'CTR004',
        InnerCaseQty: '10',
        ApprovalFlag: '0',
        SplitSetFlg: '0',
        ProLotQtyCheck: '10',
        NotDeliveryCheck: '0',
        AvgDigestQty: '4',
        OrderUnit: 'CS',
        ShippingTypeCD: '1',
        OrderUnitQty: '10',
        LotUnit: 'CS',
        Terms: '60',
        IsVerNODiff: '0',
        CurrentOrderFlg: '0'
    },
    {
        ChkFlg: '0',
        OrderStatus: '発注済',
        Times: '2',
        StoreHouse: 'CTR002 センター2',
        MixCD: 'MX02',
        DIV: 'DIV006',
        JAN: '4901234567895',
        ProductName: '商品F 季節商品',
        SpecName: '750ml',
        WareHouseQty: 120,
        week1: 20,
        week2: 25,
        week3: 30,
        week4: 35,
        Tweek: 40,
        OrderPlanDate: '2024/11/30',
        DeliveryPlanQty: 80,
        SalesDay: 3,
        CenterSalesDay: 5,
        OrderSalesDay: 7,
        OrderPlanQtyAfter: 64,
        LotQty: '8',
        CaseQty: 16,
        AdjustRate: '90',
        OrderPlanQty: 58,
        DeliveryPlanDate: '2024/12/05',
        FlgConsultJAN: 'F/4901234567895',
        comment: '季節限定',
        OrderCaseID: 'OC006',
        OrderCaseDetailID: 'OCD006',
        VerNO: '2',
        StoreHouseCD: 'CTR002',
        InnerCaseQty: '4',
        ApprovalFlag: '1',
        SplitSetFlg: '1',
        ProLotQtyCheck: '4',
        NotDeliveryCheck: '1',
        AvgDigestQty: '8',
        OrderUnit: 'CS',
        ShippingTypeCD: '3',
        OrderUnitQty: '4',
        LotUnit: 'CS',
        Terms: '30',
        IsVerNODiff: '0',
        CurrentOrderFlg: '1'
    },
    {
        ChkFlg: '1',
        OrderStatus: '',
        Times: '1',
        StoreHouse: 'CTR005 センター5',
        MixCD: 'MX05',
        DIV: 'DIV007',
        JAN: '4901234567896',
        ProductName: '商品G プレミアム商品',
        SpecName: '1.5L',
        WareHouseQty: 180,
        week1: 35,
        week2: 40,
        week3: 38,
        week4: 42,
        Tweek: 45,
        OrderPlanDate: '2024/12/01',
        DeliveryPlanQty: 90,
        SalesDay: 4,
        CenterSalesDay: 6,
        OrderSalesDay: 9,
        OrderPlanQtyAfter: 72,
        LotQty: '9',
        CaseQty: 18,
        AdjustRate: '115',
        OrderPlanQty: 83,
        DeliveryPlanDate: '2024/12/06',
        FlgConsultJAN: 'G/4901234567896',
        comment: 'プレミアム',
        OrderCaseID: 'OC007',
        OrderCaseDetailID: 'OCD007',
        VerNO: '1',
        StoreHouseCD: 'CTR005',
        InnerCaseQty: '6',
        ApprovalFlag: '0',
        SplitSetFlg: '0',
        ProLotQtyCheck: '6',
        NotDeliveryCheck: '0',
        AvgDigestQty: '10',
        OrderUnit: 'CS',
        ShippingTypeCD: '1',
        OrderUnitQty: '6',
        LotUnit: 'CS',
        Terms: '45',
        IsVerNODiff: '0',
        CurrentOrderFlg: '0'
    },
    {
        ChkFlg: '0',
        OrderStatus: '承認待ち',
        Times: '1',
        StoreHouse: 'CTR001 センター1',
        MixCD: 'MX01',
        DIV: 'DIV008',
        JAN: '4901234567897',
        ProductName: '商品H エコ商品',
        SpecName: '400ml',
        WareHouseQty: 90,
        week1: 18,
        week2: 20,
        week3: 22,
        week4: 25,
        Tweek: 28,
        OrderPlanDate: '2024/12/02',
        DeliveryPlanQty: 70,
        SalesDay: 3,
        CenterSalesDay: 6,
        OrderSalesDay: 8,
        OrderPlanQtyAfter: 56,
        LotQty: '7',
        CaseQty: 14,
        AdjustRate: '108',
        OrderPlanQty: 60,
        DeliveryPlanDate: '2024/12/07',
        FlgConsultJAN: 'H/4901234567897',
        comment: 'エコ対応',
        OrderCaseID: 'OC008',
        OrderCaseDetailID: 'OCD008',
        VerNO: '1',
        StoreHouseCD: 'CTR001',
        InnerCaseQty: '8',
        ApprovalFlag: '0',
        SplitSetFlg: '0',
        ProLotQtyCheck: '8',
        NotDeliveryCheck: '0',
        AvgDigestQty: '6',
        OrderUnit: 'CS',
        ShippingTypeCD: '2',
        OrderUnitQty: '8',
        LotUnit: 'CS',
        Terms: '30',
        IsVerNODiff: '0',
        CurrentOrderFlg: '0'
    },
    {
        ChkFlg: '1',
        OrderStatus: '未発注',
        Times: '2',
        StoreHouse: 'CTR003 センター3',
        MixCD: 'MX03',
        DIV: 'DIV009',
        JAN: '4901234567898',
        ProductName: '商品I 健康商品',
        SpecName: '200ml',
        WareHouseQty: 60,
        week1: 10,
        week2: 12,
        week3: 15,
        week4: 18,
        Tweek: 20,
        OrderPlanDate: '2024/12/03',
        DeliveryPlanQty: 40,
        SalesDay: 3,
        CenterSalesDay: 5,
        OrderSalesDay: 7,
        OrderPlanQtyAfter: 32,
        LotQty: '4',
        CaseQty: 8,
        AdjustRate: '125',
        OrderPlanQty: 40,
        DeliveryPlanDate: '2024/12/08',
        FlgConsultJAN: 'I/4901234567898',
        comment: '健康志向',
        OrderCaseID: 'OC009',
        OrderCaseDetailID: 'OCD009',
        VerNO: '1',
        StoreHouseCD: 'CTR003',
        InnerCaseQty: '20',
        ApprovalFlag: '0',
        SplitSetFlg: '1',
        ProLotQtyCheck: '20',
        NotDeliveryCheck: '0',
        AvgDigestQty: '3',
        OrderUnit: 'CS',
        ShippingTypeCD: '1',
        OrderUnitQty: '20',
        LotUnit: 'CS',
        Terms: '60',
        IsVerNODiff: '1',
        CurrentOrderFlg: '0'
    },
    {
        ChkFlg: '0',
        OrderStatus: '発注済',
        Times: '1',
        StoreHouse: 'CTR004 センター4',
        MixCD: 'MX04',
        DIV: 'DIV010',
        JAN: '4901234567899',
        ProductName: '商品J スペシャル商品',
        SpecName: '600ml',
        WareHouseQty: 110,
        week1: 22,
        week2: 25,
        week3: 28,
        week4: 30,
        Tweek: 33,
        OrderPlanDate: '2024/12/04',
        DeliveryPlanQty: 75,
        SalesDay: 3,
        CenterSalesDay: 5,
        OrderSalesDay: 7,
        OrderPlanQtyAfter: 60,
        LotQty: '10',
        CaseQty: 15,
        AdjustRate: '102',
        OrderPlanQty: 61,
        DeliveryPlanDate: '2024/12/09',
        FlgConsultJAN: 'J/4901234567899',
        comment: 'スペシャル',
        OrderCaseID: 'OC010',
        OrderCaseDetailID: 'OCD010',
        VerNO: '1',
        StoreHouseCD: 'CTR004',
        InnerCaseQty: '5',
        ApprovalFlag: '1',
        SplitSetFlg: '0',
        ProLotQtyCheck: '5',
        NotDeliveryCheck: '0',
        AvgDigestQty: '9',
        OrderUnit: 'CS',
        ShippingTypeCD: '1',
        OrderUnitQty: '5',
        LotUnit: 'CS',
        Terms: '30',
        IsVerNODiff: '0',
        CurrentOrderFlg: '1'
    },
    {
        ChkFlg: '1',
        OrderStatus: '',
        Times: '1',
        StoreHouse: 'CTR005 センター5',
        MixCD: 'MX05',
        DIV: 'DIV011',
        JAN: '4901234567800',
        ProductName: '商品K 定番商品',
        SpecName: '350ml',
        WareHouseQty: 250,
        week1: 50,
        week2: 55,
        week3: 52,
        week4: 58,
        Tweek: 60,
        OrderPlanDate: '2024/12/05',
        DeliveryPlanQty: 140,
        SalesDay: 4,
        CenterSalesDay: 7,
        OrderSalesDay: 10,
        OrderPlanQtyAfter: 112,
        LotQty: '14',
        CaseQty: 28,
        AdjustRate: '98',
        OrderPlanQty: 110,
        DeliveryPlanDate: '2024/12/10',
        FlgConsultJAN: 'K/4901234567800',
        comment: '定番商品',
        OrderCaseID: 'OC011',
        OrderCaseDetailID: 'OCD011',
        VerNO: '1',
        StoreHouseCD: 'CTR005',
        InnerCaseQty: '4',
        ApprovalFlag: '0',
        SplitSetFlg: '0',
        ProLotQtyCheck: '4',
        NotDeliveryCheck: '0',
        AvgDigestQty: '14',
        OrderUnit: 'CS',
        ShippingTypeCD: '1',
        OrderUnitQty: '4',
        LotUnit: 'CS',
        Terms: '30',
        IsVerNODiff: '0',
        CurrentOrderFlg: '0'
    },
    {
        ChkFlg: '0',
        OrderStatus: '承認待ち',
        Times: '2',
        StoreHouse: 'CTR001 センター1',
        MixCD: 'MX01',
        DIV: 'DIV012',
        JAN: '4901234567801',
        ProductName: '商品L 最終商品',
        SpecName: '800ml',
        WareHouseQty: 130,
        week1: 26,
        week2: 30,
        week3: 28,
        week4: 32,
        Tweek: 35,
        OrderPlanDate: '2024/12/06',
        DeliveryPlanQty: 85,
        SalesDay: 4,
        CenterSalesDay: 6,
        OrderSalesDay: 8,
        OrderPlanQtyAfter: 68,
        LotQty: '8.5',
        CaseQty: 17,
        AdjustRate: '112',
        OrderPlanQty: 76,
        DeliveryPlanDate: '2024/12/11',
        FlgConsultJAN: 'L/4901234567801',
        comment: '最終発注',
        OrderCaseID: 'OC012',
        OrderCaseDetailID: 'OCD012',
        VerNO: '2',
        StoreHouseCD: 'CTR001',
        InnerCaseQty: '8',
        ApprovalFlag: '0',
        SplitSetFlg: '1',
        ProLotQtyCheck: '8',
        NotDeliveryCheck: '0',
        AvgDigestQty: '8',
        OrderUnit: 'CS',
        ShippingTypeCD: '2',
        OrderUnitQty: '8',
        LotUnit: 'CS',
        Terms: '45',
        IsVerNODiff: '1',
        CurrentOrderFlg: '0'
    }
];
			RegistryGlobal.showList = mockData;
			TemplateGrid.CreatHotGrid('dgJiseki',RegistryGlobal.showList);
			showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
		}
	});
}

//csv出力
var tableData_csv = function(){
	var divStr;//DIV
	var storeHouseStr;//センター
	if(!$("#divMulti").val()){
		divStr = $("#divMulti").val();
	}else{
		divStr = $("#divMulti").val().join(",");
	}
	if(!$("#storeHouseMulti").val()){
		storeHouseStr = $("#storeHouseMulti").val();
	}else{
		storeHouseStr = $("#storeHouseMulti").val().join(",");
	}
	//JAN
	var janStr = $("#janArea").val().replace(/[\n\u3000\u0020]*$/g,"").replace(/[ ]/g,"").replace(/\n+$/g,"").replace(/\r\n+$/g,"").replace(/\,+$/g,"").replace(/\n/g,",").replace(/\r\n/g,",");
	var dataFlg;//推奨数ある商品のみ表示
	if($("#inline1").is(":checked")){
		dataFlg = "1";
	}else{
		dataFlg = "0";
	}
	var venderFlg;//add zhangyunfeng コンサイメント発注商品除外2019/06/05
	if($("#chkExceptPro").is(":checked")){
		venderFlg = "1";
	}else{
		venderFlg = "0";
	}
    var chkTimesFlg;//add zhangyunfeng 二回発注2019/06/06
    if($("#chkTimesFlg").is(":checked")){
        chkTimesFlg = "1";
    }else{
        chkTimesFlg = "0";
    }
	var vendorStr=$("#divSupplierMulti").val()==null?"":$("#divSupplierMulti").val().join(",");
	$.ajax({
		url: `${SystemManage.shiniseAddress}Recommend/tableDataSearch`,
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		type:'POST',
		data:{divStr: divStr, storeHouseStr: storeHouseStr, janStr: janStr
			, userCD: isEmpty(vendorStr)? RegistryGlobal.userCD:vendorStr
			, dataFlg: dataFlg,employeeCD:RegistryGlobal.employeeCD
			, venderFlg:venderFlg,chkTimesFlg:chkTimesFlg},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
			}else{				
				var result = JSON.parse(response);
				const resultRecommendations = result.Recommendations;
				if(resultRecommendations.length == 0){
					showMsgBox(Message.msg003,[],function(){});//対象データがありません。
					return;
				}
			    var csvList = $.grep(resultRecommendations, function (item) {
			        return item.ShippingTypeCD === "4";
			    })	
			    var fields = ['Times','StoreHouse', 'MixCD', 'DIV', 'JAN', 'ProductName', 'SpecName', 'WareHouseQty', 'week1', 'week2','week3','week4','Tweek','OrderPlanDate','DeliveryPlanQty',
			                  'SalesDay','CenterSalesDay','OrderSalesDay','OrderPlanQtyAfter','LotQty','CaseQty','AdjustRate','OrderPlanQty','DeliveryPlanDate','FlgConsultJAN','Comment'];
			    var headNames =['回数','センター', '混載区分', 'DIV', 'JAN', '商品名', '規格', 'センター在庫', '4週前週売数', '3週前週売数',
			              '2週前週売数', '1週前週売数', '今週','発注予定日', '納品予定数', '現在庫消化', 
			              '在庫＋納品予定分消化日数', '発注後消化日数','推奨数', 'ロット数', 'ケース数', '伸び率','発注数', '納品予定日', '区分/JAN', '備考'];
			    
			    var csv = json2csv({data:csvList, fields:fields, fieldNames:headNames});
			    var filename = "預託商品推奨" + "-" + getNowTime() + ".csv";
			    funDownload(csv.replaceAll("\"", ""), filename);
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
		}
	});
}

//download csv
var funDownload = function (content, filename) {
	if (window.navigator.msSaveOrOpenBlob) {
		// if browser is IE
	  	var blob = new Blob(['\uFEFF', decodeURIComponent(encodeURI(content))], {
	    	type: "text/csv;charset=utf-8;"
	  	});
	  	navigator.msSaveBlob(blob, filename);
	}else{
    	var eleLink = document.createElement('a');
        eleLink.download = filename;
        eleLink.style.display = 'none';
        var sjis_array = UTF8_ShiftJIS(content);
        var uint8_array = new Uint8Array(sjis_array);
        var blob = new Blob([uint8_array],{type: "text/csv"}); 
        eleLink.href = URL.createObjectURL(blob);   
        document.body.appendChild(eleLink);
        eleLink.click();
        document.body.removeChild(eleLink);
    }
};
//20190426 発注済み、再作成案判断追加
var confirmDataSave=function(sendData){
	$.ajax({
		url: `${SystemManage.shiniseAddress}Recommend/tableData_confirm`,
		headers: {
        	"user-code": sessionStorage.getItem("userCode") || ""
    	},
		type:'POST',
		data:{sendData: sendData, userCD: RegistryGlobal.userCD,employeeCD:RegistryGlobal.employeeCD},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg011,[],function(){tableData_search();});//確定失敗しました。
			}else if("confirm"==response){
				showMsgBox(Message.msg029,[],function(){tableData_search();});//確定成功、自動承認失敗。
			}else if("stop"==response){
				showMsgBox(Message.msg030,[],function(){tableData_search();});//発注更新時間帯。
			}else{
				tableData_search();
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg011,[],function(){});//確定失敗しました。
		}
	});
}
//確定
var tableData_confirm = function(){
	//var tableData = TemplateGrid.tbhot.getData();
	var tableData = TemplateGrid.tbhot.getSourceData();
	var sendData = "";
	var cnt = 0;
	var sumCnt = 0;
	var pass = true;
	var chkData = [];
	var hasVerChk=false;
	chk = "";
	if(tableData.length==0){
		showMsgBox(Message.msg005,[],function(){});//対象データがありません。
		return;
	}
	for(var i=0;i<tableData.length;i++){
		if("1"==tableData[i].ChkFlg){
			cnt++;
			if(tableData[i].OrderPlanQty && 0!=tableData[i].OrderPlanQty && tableData[i].DeliveryPlanDate){
				sumCnt++;
			}
			if(
				(!tableData[i].OrderPlanQty || "0"==tableData[i].OrderPlanQty) && tableData[i].DeliveryPlanDate
			){
				showMsgBox(Message.msg005,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"OrderPlanQty");});//推奨数あるいは納品予定日は空白です。ご確認してください。
				return;
			}
			if(
				tableData[i].OrderPlanQty && "0"!=tableData[i].OrderPlanQty && !tableData[i].DeliveryPlanDate
			){
				showMsgBox(Message.msg005,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"DeliveryPlanDate");});//推奨数あるいは納品予定日は空白です。ご確認してください。
				return;
			}
			if (tableData[i].OrderPlanQty&&parseInt(tableData[i].OrderPlanQty)>=100000)
            {
                showMsgBox(Message.msg027,[],function(){
                	TemplateGrid.tbhot.selectCellByProp(i,"OrderPlanQty");
                });
                return;
            }
			if( "C/S"==tableData[i].OrderUnit &&tableData[i].InnerCaseQty&&
				(parseFloat(tableData[i].OrderPlanQty?Number(tableData[i].OrderPlanQty):0) %
					parseInt(tableData[i].InnerCaseQty) != 0)
            ){
				showMsgBox(Message.msg007,[parseInt(tableData[i].InnerCaseQty)],function(){TemplateGrid.tbhot.selectCellByProp(i,"OrderPlanQty");});//ケース入数は$1です、発注予定数はケース入数の倍数を入力してください！
				return;
			}
			if(tableData[i].DeliveryPlanDate && !isDate(tableData[i].DeliveryPlanDate)){
				showMsgBox(Message.msg018,["納品予定日"],function(){TemplateGrid.tbhot.selectCellByProp(i,"DeliveryPlanDate");});//$1の日付入力が不正です。
				return;
			}
			if(tableData[i].DeliveryPlanDate){
				if(-1==chkData.indexOf(tableData[i].OrderCaseID+"_"+tableData[i].Times)){
					chkData.push(tableData[i].OrderCaseID+"_"+tableData[i].Times);
					chkData.push(tableData[i].OrderCaseID+"_"+tableData[i].Times+"_"+tableData[i].DeliveryPlanDate);
				}else{
					if(-1==chkData.indexOf(tableData[i].OrderCaseID+"_"+tableData[i].Times+"_"+tableData[i].DeliveryPlanDate)){
						showMsgBox(Message.msg006,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"DeliveryPlanDate");});//同じ混載区分内、納品予定日は同じてす。
						return;
					}
				}
			}
			//
			if(tableData[i].IsVerNODiff&&(tableData[i].IsVerNODiff===1||tableData[i].IsVerNODiff==="1")&&tableData[i].DeliveryPlanDate && tableData[i].OrderPlanQty){
				hasVerChk=true;
			}
			
			var year = new Date(RegistryGlobal.OrderCaseCreateDate).getFullYear();
			var month = new Date(RegistryGlobal.OrderCaseCreateDate).getMonth();
			var date = new Date(RegistryGlobal.OrderCaseCreateDate).getDate();
			if(tableData[i].DeliveryPlanDate && new Date(tableData[i].DeliveryPlanDate)<new Date(year+"/"+Number(month+1)+"/"+date)){
				showMsgBox(Message.msg009,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"DeliveryPlanDate");});//当日より過去の納品日は入力できません。
				return;
			}
			if(tableData[i].DeliveryPlanDate && new Date(tableData[i].DeliveryPlanDate)>=new Date(Number(year+1)+"/"+Number(month+1)+"/"+date)){
				showMsgBox(Message.msg010,[],function(){TemplateGrid.tbhot.selectCellByProp(i,"DeliveryPlanDate");});//1年以内の納品予定日を入力してください。
				return;
			}

			//重複発注チェック add zyf 20191106
			var orderArr = tableData.filter(function (item) {
				return item.ChkFlg==='1'&&item.JAN === tableData[i].JAN && item.StoreHouseCD === tableData[i].StoreHouseCD && item.DeliveryPlanDate === tableData[i].DeliveryPlanDate;
			});
			if (orderArr.length > 1) {
				showMsgBox(Message.msg032,[tableData[i].StoreHouseCD,tableData[i].JAN],function(){TemplateGrid.tbhot.selectCellByProp(i,"DeliveryPlanDate");});//当日より過去の納品日は入力できません。
				return;
			}
			if(tableData[i].DeliveryPlanDate && tableData[i].OrderPlanQty){
				sendData += tableData[i].OrderCaseID+",";
				sendData += tableData[i].OrderCaseDetailID+",";
				sendData += tableData[i].JAN+",";
				sendData += tableData[i].StoreHouseCD+",";
				sendData += tableData[i].DeliveryPlanDate+",";
				sendData += tableData[i].OrderPlanQty+",";
				sendData += tableData[i].Times+",";
				sendData += tableData[i].VerNO+",";
				if(tableData[i].SplitSetFlg=="1"){
					sendData += ";";//add zhangyunfeng 伝票分けの場合、伸び率保存しない 20190611
				}else{
					sendData += (isEmpty(tableData[i].AdjustRate)?"":tableData[i].AdjustRate)+";";//add zhangyunfeng 伸び率追加 20190611
				}

			}
		}
	}
	if(0==cnt){
		showMsgBox(Message.msg004,[],function(){});//チェックされたデータがありません。
		return;
	}
	if(0==sumCnt){
		showMsgBox(Message.msg019,[],function(){});//案推奨データがないので、確定できません。
		return;
	}
	//20190426 zhangyunfeng 発注済み、再作成案判断追加
	if(hasVerChk){
		showMsgConfirm(Message.msg031,[], function(){confirmDataSave(sendData);},function(){});
	}else{
		confirmDataSave(sendData);
	}
}
var divAndStoreHouseInit = function(){	
	$('#divMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て  ',
		allSelectedText: '全DIV',
		nonSelectedText: '全て ',
		nSelectedText: 'DIV',
		numberDisplayed:1
	});
	
	$('#storeHouseMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全センター',
		nonSelectedText: '全て',
		nSelectedText: ' センター',
		numberDisplayed:1
	});
	//ベンダー
	$('#divSupplierMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: 'ベンダー',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全ベンダー',
		nonSelectedText: '全て',
		nSelectedText: 'ベンダー',
		numberDisplayed:1
	});
}

function getQueryString(name){
     var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
     var r = window.location.search.substr(1).match(reg);
     if(r!=null){    	 
    	 return unescape(r[2]);
     }else{    	 
    	 return null;
     }
}

/////////////////////////////////////////////////////////
//bindDataとonchange事件
//data     Json
//valuecol 項目CDString
//labelcol 項目名 String
//id       String
//multiple  "all":全選(複数選択のみ有効) 
//          "single":初期に選択項目
//          Array:選択したデータ  []:すべて選択しない
////////////////////////////////////////////////////////
function bindData(filtdata ,valuecol, labelcol, id, multiple){ 
   var data = filtdata;
   var options=[];
   for(var i=0;i<data.length;i++){
	   options.push({
		   label:data[i][valuecol]+"-"+data[i][labelcol],
		   title:data[i][labelcol],
		   value:data[i][valuecol]
	   });
   }
   $('#'+id).multiselect('dataprovider', options);
   if(multiple=="all"){
  	 	//全選  及び　onChange trigger
  	 	$('#'+id).multiselect('selectAll',false,true);
  	 	$('#'+id).multiselect('updateButtonText');				   
   } else if(multiple=="single"){
  	 	//第一の項目選択　及び　onChange trigger
  	 	$('#'+id).multiselect('select',options[0].value,true);			     
   } else {
	   	//選択設定   及び　onChange trigger
		$('#'+id).multiselect('select',multiple,true);
   }
}
/*

//計算マウスの位値
var Mouse = function(e){
    mouse = new MouseEvent(e);
    leftpos = mouse.x + divoffset;
    toppos = mouse.y + divoffset;
}
//計算マウスの位値
var MouseEvent = function(e) {
    this.x = e.pageX
    this.y = e.pageY
}*/
