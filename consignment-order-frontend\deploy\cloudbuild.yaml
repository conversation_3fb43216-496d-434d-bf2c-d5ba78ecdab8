steps:
  # Build the container image
  - id: build_image
    name: "gcr.io/cloud-builders/docker"
    args:
      [
        "build",
        "consignment-order-frontend",
        "-f",
        "consignment-order-frontend/deploy/Dockerfile",
        "-t",
        "$LOCATION-docker.pkg.dev/$PROJECT_ID/$_REPOSITORY/$_IMAGE:$SHORT_SHA",
      ]

  # Scan container image
  - id: scan_artifact
    name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        echo '--- Creating an scan artifact for image'
        if [[ $_SCAN_ARTIFACT_SECURITY == 1 ]]; then
          echo '--- Run Security scan'
          gcloud artifacts docker images scan $LOCATION-docker.pkg.dev/$PROJECT_ID/$_REPOSITORY/$_IMAGE:$SHORT_SHA \
          --location=asia --format='value(response.scan)' > /workspace/scan_id.txt
        else
          echo '---Not Run Security scan. Skipping image scan.'
        fi

  # Check severity of scan image
  - id: check_artifact_vulnerability_severity
    name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    allowFailure: true
    entrypoint: /bin/bash
    args:
      - "-c"
      - |
        echo '--- Check severity of image'
        if [[ $_SCAN_ARTIFACT_SECURITY == 1 ]]; then
          echo '--- Run check severity'
          severities=$(gcloud artifacts docker images list-vulnerabilities $(cat /workspace/scan_id.txt) \
          --format='value(vulnerability.effectiveSeverity)')
          if echo "$severities" | grep -Eq "$_SCAN_ARTIFACT_SEVERITY"; then
            echo "Failed vulnerability check for ${_SCAN_ARTIFACT_SEVERITY} level"
            gcloud artifacts docker images list-vulnerabilities $(cat /workspace/scan_id.txt) \
            --format='json' > /workspace/scanned.json
            gsutil cp /workspace/scanned.json gs://$_CACHE_BUCKET/build-scan-log/$_IMAGE-$SHORT_SHA.json
            exit 1
          else
            echo '"No ${_SCAN_ARTIFACT_SEVERITY} vulnerabilities found"' && exit 0
          fi
        else
          echo '---Not Run Security scan. Skipping vulnerability check.'
        fi

  # Push the container image to Artifact Registry
  - id: push_image
    name: "gcr.io/cloud-builders/docker"
    args:
      [
        "push",
        "$LOCATION-docker.pkg.dev/$PROJECT_ID/$_REPOSITORY/$_IMAGE:$SHORT_SHA",
      ]

  # Update helm charts image tag
  - id: update_helm_image_tag
    name: "gcr.io/cloud-builders/gcloud"
    entrypoint: sh
    args:
      - "-c"
      - |
        set -x
        if gsutil -q stat gs://$_CACHE_BUCKET/yq.tar.gz; then
          gsutil cp gs://$_CACHE_BUCKET/yq.tar.gz yq.tar.gz
        else
          curl -L -o yq.tar.gz -O https://github.com/mikefarah/yq/releases/download/v4.2.0/yq_linux_amd64.tar.gz && \
          gsutil cp yq.tar.gz gs://$_CACHE_BUCKET/yq.tar.gz
        fi
        tar -xzf yq.tar.gz && mv yq_linux_amd64 /usr/bin/yq && chmod +x /usr/bin/yq && \
        mkdir -p /root/.ssh && \
        gcloud secrets versions access latest --secret=SHINISE_HELM_SSH_KEY > /root/.ssh/id_github && \
        chmod 600 /root/.ssh/id_github
        cat <<EOF >/root/.ssh/config
        Hostname github.com
        IdentityFile /root/.ssh/id_github
        EOF
        ssh-keyscan -t rsa github.com > /root/.ssh/known_hosts && \
        <NAME_EMAIL>:retail-ai-inc/shinise-infra.git && \
        git config --global user.email $(gcloud auth list --filter=status:ACTIVE --format='value(account)') && \
        git config --global user.name "Cloud Build"
        cd shinise-infra && \
        git checkout $_HELM_BRANCH && \
        yq eval -i '.deployment.image.tag="$SHORT_SHA"' frontends/consignment-order-fe/$_HELM_VALUES_FILE && \
        git commit -a -m "CI: Update $_IMAGE:$SHORT_SHA image tag in $_HELM_VALUES_FILE && \
        Built by trigger https://console.cloud.google.com/cloud-build/builds;region=$LOCATION/$BUILD_ID?project=$PROJECT_ID"
        git push origin $_HELM_BRANCH

options:
  logging: CLOUD_LOGGING_ONLY
substitutions:
  _CACHE_BUCKET:
  _REPOSITORY: shinise-app
  _IMAGE: consignment-order-frontend
  _HELM_BRANCH:
  _HELM_VALUES_FILE:
  _SCAN_ARTIFACT_SECURITY: "0"
  _SCAN_ARTIFACT_SEVERITY: "CRITICAL|HIGH"
images:
  - "$LOCATION-docker.pkg.dev/$PROJECT_ID/$_REPOSITORY/$_IMAGE:$SHORT_SHA"
