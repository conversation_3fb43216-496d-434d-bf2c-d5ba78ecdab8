<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>展開センター設定画面</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta content="width=device-width, initial-scale=1" name="viewport"/>
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Cache-Control" content="no-cache">
	<meta http-equiv="Expires" content="0">
	<link rel="icon" type="image/png/ico" href="../Common/favicon.ico">
	<!-- basic styles -->
	<link href="../Common/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <link href="../Common/common.css" rel="stylesheet">
    <link href="../Common/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/bootstrap/css/bootstrap.min.css" rel="stylesheet">
	<link href="../Common/css/layout.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/css/themes/light2.css" rel="stylesheet" type="text/css" id="style_color"/>
	<link href="../Common/bootstrap-multiselect/css/bootstrap-multiselect.css" rel="stylesheet" type="text/css"/>
	<link href="../Common/handsontable/handsontable.full.min.css" rel="stylesheet"/>
	<link href="../Common/handsontable/chosen.css" rel="stylesheet"/>
	<link href="../Common/jan/jan.css" rel="stylesheet"/>
	<link href="../Common/prettify/prettify.css" rel="stylesheet"/>
    <script src="../Common/jquery/jquery.min.js" type="text/javascript"></script>
	<script src="../Common/bootstrap-multiselect/js/bootstrap-multiselect.js" type="text/javascript" ></script>
	<script src="../Common/handsontable/handsontable.full.min.js" type="text/javascript" ></script>
	<script src="../Common/handsontable/chosen.jquery.js" type="text/javascript" ></script>
	<script src="../Common/handsontable/handsontable-chosen-editor.js" type="text/javascript" ></script>
    <script src="../Common/prettify/prettify.js" type="text/javascript" ></script>
	<script>document.write("<s"+"cript src='../Common/version.js?ver="+Math.random()+"'></scr"+"ipt>"); </script>
	<script>document.write("<s"+"cript src='../Common/common.js?ver="+sysversion+"'></scr"+"ipt>");</script>
	<link rel="shortcut icon" href="../Common/favicon.ico"/>
	<link href="css/style.css" rel="stylesheet" type="text/css" />
</head>
<body class="page-container-bg-solid">
<script>
	var href=window.document.location.href;
	var rankNum=getRank(href);
</script>
<!-- BEGIN CONTAINER -->
<div class="page-container">
	<!-- BEGIN CONTENT -->
	<div class="page-content-wrapper">
		<div class="page-content">	
			<!--#検索条件 form-->  		
			<div class="form-horizontal" style="margin-top: 15px;">	
			    <div class="form-group">	
				    <div class="btn-group"  role="group">
					    <label class="lblWidth">ベンダー</label>
					</div>
					<div class="btn-group selWidth"  role="group">					
					    <select id="divSupplierMulti" size="5" multiple class="form-control"></select>
					</div>
				　　　<div class="btn-group"  role="group">
					    <label class="lblWidth">センター</label>
					</div>
					<div class="btn-group selWidth"  role="group">					
					    <select id="divCenterMulti" size="5" multiple class="form-control"></select>
					</div>										
                </div>
			    <div class="form-group" style="margin-left: 30%;">
					<div class="btn-group btnWidth" role="group">
						<input id="btn_search" type="button" class="btn btn-primary btn-block" value="検索">
					</div>
					<div class="btn-group btnWidth" role="group">
						<input id="btn_Delete" type="button" class="btn btn-primary btn-block" value="削除">
					</div>
					<div class="btn-group btnWidth" role="group">
						<input id="btn_save" type="button" class="btn btn-primary btn-block" value="登録">
					</div>
				</div>																																											
			</div>
			<!--/検索条件 form-->
            <div class="col-md-12 col-sm-12 col-xs-12" id="grid" style="padding:20px 10px 5px 10px;">
                <div id="MainGrid" style="width:100%;"></div>
            </div>
		</div>
	</div>
	<!-- END CONTENT -->
</div>
<!-- END CONTAINER -->

<!-- BEGIN FOOTER -->
<div class="page-footer">
	<div class="scroll-to-top">
		<i class="icon-arrow-up"></i>
	</div>
</div>
<!-- END FOOTER -->

<!--メッセージ表示-->
<div class="modal fade" id="myMessage" tabindex="-2" role="dialog" aria-labelledby="" style="z-index: 2000;margin-top:100px;">
	  <div class="modal-dialog" role="document">
	    <div class="modal-content">
	      <div class="modal-header">
	        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span></button>
	        <h4 class="modal-title" id="myMessageTitle">展開センター設定</h4>
	      </div>
	      <div class="modal-body" >
	      	  <p id="myMessageBody"></p>
	      </div>
	      <div class="modal-footer">
	          <button type="button" id="msgSysBtnYes" class="btn btn-default" data-dismiss="">OK</button>
	          <button type="button" id="msgSysBtnNo" class="btn btn-default" data-dismiss="modal">キャンセル</button>
	      </div>
	    </div>
	  </div>
</div>

<script src="../Common/jquery/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
<script src="../Common/bootstrap/js/bootstrap.min.js"></script>
<script src="../Common/message/message.js"></script>
<!-- <script src="../Common/message/jquery-ui.my.js" type="text/javascript"></script> -->
<script src="../Common/common.js"></script>
<script src="../Common/js/metronic.js" type="text/javascript"></script>
<script src="../Common/js/layout.js" type="text/javascript"></script>
<!-- <div id='messageBox' style='display:none;'><span id='spanmessage'></span></div> -->
<script>
	jQuery(document).ready(function() {    
	   Metronic.init(); // init metronic core componets
	   Layout.init(); // init layout
	});
	makeProcessing(rankNum);
</script>
<script>document.write("<s"+"cript src='js/gridconfig.js?ver="+sysversion+"'></scr"+"ipt>");</script>
<script>document.write("<s"+"cript src='js/controller.js?ver="+sysversion+"'></scr"+"ipt>");</script>
</body>
<!-- END BODY -->
</html>