package jp.trial.DCOrderConsignment.dao;

import jp.trial.DCOrderConsignment.model.InOutPlanModel;

import java.util.List;

public interface RecommendScreenDao {
	/** DIVとセンターとFOUR WEEKSを取得する */
	public List<Object> getInitDataList(String userCD,String employeeCD) throws Exception;
	/** 検索 */
	public List<Object> tableData_search(String divStr, String storeHouseStr, String janStr, String userCD,
										 String dataFlg,String employeeCD,String venderFlg,String chkTimesFlg) throws Exception;
	/** 確定 */
	public String tableData_confirm(String sendData, String userCD,String employeeCD) throws Exception;
	/**センター入出荷予定結果一覧*/
	public List<InOutPlanModel> GetInOutPlanInfo(String jan, String storeHouseCD) throws Exception;
}
