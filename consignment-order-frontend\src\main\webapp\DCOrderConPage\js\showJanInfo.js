/**
* 発注推奨案伝票分け結果一覧
* 前提条件：Jquery,handsontableが必須
*/
var janInfo = (function ($) {
    var id=Math.floor(Math.random() * 1000);
    var $dialog = $(
    '<div id="janInfo-dialog-module'+id+'" class="modal fade" style="top:10px;" tabindex="-1">' +
        '<div id="janInfo-modal'+id+'" class="modal-dialog" style="width: 200px;" >' +
            '<div class="modal-content" id="janInfo-modal-content'+id+'">' +
                '<div class="modal-header" style="padding:10px 15px;">' +
                    '<button type="button" class="close" data-dismiss="modal" aria-label="Close">' +
                        '<span aria-hidden="true">&times;</span>' +
                    '</button>' +
                    '<h4 class="modal-title" style="display:inline-block;">ロット情報</h4>' +
                '</div>' +
                '<div class="modal-body" style="padding-top:5px;padding-bottom:5px;">' +
                    '<div class="panel" style="margin-bottom:10px;">' +
                        '<div class="panel-body" style="padding: 0px; height: 100px; overflow: auto;">' +
                            '<div style="height:25px;overflow:hidden;z-index:10;">'+
                            '<label class="lblWidth">入数:</label>'+
                            '<label id="OrderUnitQty'+id+'" >入数:</label>'+
                            '</div>'+
                            '<div style="height:25px;overflow:hidden;z-index:10;">'+
                            '<label class="lblWidth">発注単位:</label>'+
                            '<label id="OrderUnit'+id+'" >入数:</label>'+
                            '</div>'+
                            '<div style="height:25px;overflow:hidden;z-index:10;">'+
                            '<label class="lblWidth">発注単位数:</label>'+
                            '<label id="LotUnit'+id+'" >入数:</label>'+
                            '</div>'+
                            '<div style="height:25px;overflow:hidden;z-index:10;">'+
                            '<label class="lblWidth">条件名:</label>'+
                            '<label id="Terms'+id+'" >入数:</label>'+
                            '</div>'+
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>' +
    '</div>'
    );
    
    var $fun = {
        dialog: function(){
            return $dialog;
        },
        init: function(obj){/*モジュール初期入口*/
            $dialog.appendTo('body');
            $fun.param = obj;
            $fun.dialog().on("hidden.bs.modal",function(){
                $fun.destroy();
            });
            $fun.dialog().on("shown.bs.modal",function(){
            });
            $fun.dialog().on("show.bs.modal",function(){
            	$fun.adjust();

            	if($fun.param.OrderUnitQty) {//入数
                    $("#OrderUnitQty"+id).text($fun.param.OrderUnitQty);
                }
                if($fun.param.OrderUnit) {//発注単位
                    $("#OrderUnit"+id).text($fun.param.OrderUnit);
                }
                if($fun.param.LotUnit) {//発注単位数
                    $("#LotUnit"+id).text($fun.param.LotUnit);
                }
                if($fun.param.Terms) {//条件名
                    $("#Terms"+id).text($fun.param.Terms);
                }
            });
            $fun.dialog().on("hide.bs.modal",function(){
            });
           
            $("#janInfo-dialog-module"+id).scroll(function(){
                $fun.adjust();
            });
            $("#janInfo-dialog-module"+id).resize(function(){
                $fun.adjust();
            });
        },
        show: function(){
            $dialog.modal({backdrop: true, keyboard: true});//{backdrop: 'static', keyboard: false}
        },
        hide: function(){
            $dialog.modal('hide');
        },
        destroy: function(){
            $("#janInfo-dialog-module" + id).remove();
        },
        adjust:function() {
            if( $('#janInfo-modal'+id).size()){
            	if(($fun.param.PosY+200)>$(window).height()){
            		$('#janInfo-modal'+id).css("top",$fun.param.PosY-220);
            	}else{
            		$('#janInfo-modal'+id).css("top",$fun.param.PosY-20);
            	}
            	$('#janInfo-modal'+id).css("left",($(window).width()/2-$fun.param.PosX-80)*(-1));
                
            }
        }
    };
    return $fun;
});