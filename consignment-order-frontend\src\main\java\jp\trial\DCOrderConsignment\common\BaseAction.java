package jp.trial.DCOrderConsignment.common;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.struts2.ServletActionContext;

import com.opensymphony.xwork2.ActionContext;
import com.opensymphony.xwork2.ActionSupport;

/** Description: action の親クラス*/
public class BaseAction extends ActionSupport{
	
	private static final long serialVersionUID = -8512938378400508693L;
	public HttpServletRequest request;
	public HttpServletResponse response;
	public PrintWriter print;
	
	public BaseAction() throws IOException {

		response = ServletActionContext.getResponse();
		response.setContentType("text/plain");
		response.setCharacterEncoding("UTF-8");
		print = response.getWriter();
		request = (HttpServletRequest) ActionContext.getContext().get(
				ServletActionContext.HTTP_REQUEST);
	}
	
	/**JSONデータ InputStreamReader処理 */
	public String getRequestPayload() {  
        StringBuilder sb = new StringBuilder(); 
        try(BufferedReader reader = new BufferedReader(new InputStreamReader(
                (ServletInputStream) request.getInputStream()));) {  
                 char[]buff = new char[1024];  
                 int len;  
                 while((len = reader.read(buff)) != -1) {  
                          sb.append(buff,0, len);  
                 }  
        }catch (IOException e) {  
                 e.printStackTrace();  
        }  
        return sb.toString();  
	}  
}
