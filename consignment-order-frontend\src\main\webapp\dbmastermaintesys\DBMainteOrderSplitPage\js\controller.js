/**
 * @description: DBマスタメンテナンス-単日ロット制限画面
 * @author: 10047496 zhangyunfeng
 * @date: 2018/9/24
 */
var RegistryGlobal = {
	userCD: sessionStorage.getItem("userCD"),
	divMixList:[],//混載
	divCenterList:[],//センター
	divSupplierList:[],
	standardList:[],
	tableData: [],
	tableSourceData: [],
	showList:[],
	init: null
}

$(document).ready(function () {
	$("#loadMask").show();
	$(document).ajaxStart(function(){
		$("#loadMask").show();
    }).ajaxStop(function(){
    	$("#loadMask").hide();
    });
	//INIT
	DivMultiLoadInit();
	//ベンダー用画面Grid表示する
	getInitDataList();
	
	//検索
	$("#btn_search").on("click",function(){
		tableData_search();
	});
	//削除
	$("#btn_Delete").on("click",function(){
		tableData_delete();
	});
	//保存
	$("#btn_save").on("click",function(){
		tableData_Save();
	});
});

var DivMultiLoadInit=function(){
	//混載
	$('#divMixMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全混載',
		nonSelectedText: '全て',
		nSelectedText: ' 混載',
		numberDisplayed:1
	});	
	
	$('#divCenterMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全センター',
		nonSelectedText: '全て',
		nSelectedText: ' センター',
		numberDisplayed:1
	});
	
	$('#divSupplierMulti').multiselect({
		buttonContainer: '<div class="dropdown"/>',
		buttonClass:'btn btn-default btn-block selover',
		maxHeight:200,
		enableFiltering: true,
		enableCaseInsensitiveFiltering: true,
		filterBehavior: 'both',
		filterPlaceholder: '番号、名前',
		includeSelectAllOption: true,
		selectAllText: '全て',
		allSelectedText: '全ベンダー',
		nonSelectedText: '全て',
		nSelectedText: ' ベンダー',
		numberDisplayed:1
	});
}

var getInitDataList=function(){
	
	$.ajax({
		url:SystemManage.address+'OrderSplitController/getInitList',//getCenterList',
		type:'POST',
		data:{userCD:sessionStorage.getItem("employeeCD"),//社員CD
	        venderCD:sessionStorage.getItem("userCD")//ベンダーCD
	    },
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg001,[],function(){});//初期化失敗しました。
			}else{				
				//var result = eval('('+response+')');
				var result = JSON.parse(response);
				RegistryGlobal.divMixList = result[0].MixList;
				RegistryGlobal.divCenterList = result[0].CenterList;
				RegistryGlobal.divSupplierList = result[0].SupplierList;
				RegistryGlobal.standardList = result[0].StandardList;
				//混載
				bindData2(RegistryGlobal.divMixList,"mixCD","mixCD","divMixMulti","");
				//センター
				bindData(RegistryGlobal.divCenterList,"centerCD","centerName","divCenterMulti","");
				//ベンダー
				bindData(RegistryGlobal.divSupplierList,"supplierCD","supplierName","divSupplierMulti","");
				//売数参照基準
				getTemplateMainGrid(RegistryGlobal.standardList,[]);
				
				//ベンダー選択不可用
				if(RegistryGlobal.userCD){
					$('#divSupplierMulti').multiselect("disable");
				}
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg001,[],function(){});//初期化失敗しました。
		}
	});
}

//検索
var tableData_search = function(){
	RegistryGlobal.init = "search";
	chk = "";
	var mixStr;//DIV
	var centerStr;//センター
	var supplierStr;//ベンダー
	mixStr = $("#divMixMulti").val();
	centerStr = $("#divCenterMulti").val();
	if (!$("#divSupplierMulti").val()) {
		supplierStr = sessionStorage.getItem("userCD")//ベンダーCD
	} else {
		supplierStr = $("#divSupplierMulti").val();//gain an array and need to transform to a string
		if (supplierStr) {
			supplierStr = supplierStr.join(",");
		}
	}
	if (mixStr) {
		mixStr = mixStr.join(",");
	}
	if (centerStr) {
		centerStr = centerStr.join(",");
	}
	$.ajax({
		url:SystemManage.address+'OrderSplitController/getSearchInfo',
		type:'POST',
		data:{mixStr : mixStr, centerStr : centerStr, supplierStr : supplierStr},
		success:function(response){
			if("false"==response){
				showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
			}else{				
				var result = JSON.parse(response);
				RegistryGlobal.showList = result;
				getTemplateMainGrid(RegistryGlobal.standardList,RegistryGlobal.showList);
			}
		},
		error:function(response, textStatus){
			showMsgBox(Message.msg002,[],function(){});//検索失敗しました。
		}
	});
}

//削除
var tableData_delete = function(){
	var tableData = TemplateGrid.tbhot.getData();
	var sendData = "";
	chk = "";
	var saveDatas = [];
	for(var i=0;i<tableData.length;i++){
		if("1"==tableData[i][0]){			
			var data = [];
			data.push(tableData[i][1]);
			data.push(tableData[i][2].split(" ")[0]);
			data.push(tableData[i][3].split(" ")[0]);
			data.push("");
			data.push("");
			saveDatas.push(data.join(","));
		}
	}
	if (saveDatas.length == 0){
		showMsgBox(Message.msg004,[],function(){});//チェックされたデータがありません。
	} else {
		sendData = saveDatas.join(";");
		$.ajax({
			url:SystemManage.address+'OrderSplitController/getSave',
			type:'POST',
			data:{
				sendData: sendData,
				userCD:sessionStorage.getItem("employeeCD"),//社員CD
		        venderCD:sessionStorage.getItem("userCD")//ベンダーCD
		    },
			success:function(response){
				if("false"==response){
					showMsgBox(Message.msg037,[],function(){});//削除失敗しました。
				}else{
					tableData_search();
				}
			},
			error:function(response, textStatus){
				showMsgBox(Message.msg037,[],function(){});//削除失敗しました。
			}
		});
	}
}
//保存
var tableData_Save = function(){
	var tableData = TemplateGrid.tbhot.getData();
	var sendData = "";
	chk = "";
	var saveDatas = [];
	for(var i=0;i<tableData.length;i++){
		if("1"==tableData[i][0]){
			var data = [];
			data.push(tableData[i][1]);
			data.push(tableData[i][2].split(" ")[0]);
			data.push(tableData[i][3].split(" ")[0]);
			
			if(!tableData[i][5]||tableData[i][5]=="null"){
				data.push("");
			}else{
				data.push(tableData[i][5]);
			}
			if(!tableData[i][6]||tableData[i][6]=="null"){
				data.push("");
			}else{
				data.push(tableData[i][6]);
			}
			if(!tableData[i][7]||tableData[i][7]=="null"){
				data.push("");
			}else{
				data.push(tableData[i][7]);
			}
			saveDatas.push(data.join(","));
		}
	}
	if (saveDatas.length == 0){
		showMsgBox(Message.msg004,[],function(){});//チェックされたデータがありません。
	} else {
		sendData = saveDatas.join(";");
		$.ajax({
			url:SystemManage.address+'OrderSplitController/getSave',
			type:'POST',
			data:{
				sendData: sendData,
				userCD:sessionStorage.getItem("employeeCD"),//社員CD
		        venderCD:sessionStorage.getItem("userCD")//ベンダーCD
		    },
			success:function(response){
				if("false"==response){
					showMsgBox(Message.msg028,[],function(){});//登録失敗しました。
				}else{
					tableData_search();
				}
			},
			error:function(response, textStatus){
				showMsgBox(Message.msg028,[],function(){});//登録失敗しました。
			}
		});
	}
}
/////////////////////////////////////////////////////////
//bindDataとonchange事件
//data     Json
//valuecol 項目CDString
//labelcol 項目名 String
//id       String
//multiple  "all":全選(複数選択のみ有効) 
//        "single":初期に選択項目
//        Array:選択したデータ  []:すべて選択しない
////////////////////////////////////////////////////////
function bindData(filtdata ,valuecol, labelcol, id, multiple){ 
 var data = filtdata;
 var options=[];
 for(var i=0;i<data.length;i++){
	   options.push({
		   label:data[i][valuecol]+"-"+data[i][labelcol],
		   title:data[i][labelcol],
		   value:data[i][valuecol]
	   });
 }
 $('#'+id).multiselect('dataprovider', options);
 if(multiple=="all"){
	 	//全選  及び　onChange trigger
	 	$('#'+id).multiselect('selectAll',false,true);
	 	$('#'+id).multiselect('updateButtonText');				   
 } else if(multiple=="single"){
	 	//第一の項目選択　及び　onChange trigger
	 	$('#'+id).multiselect('select',options[0].value,true);			     
 } else {
	   	//選択設定   及び　onChange trigger
	 $('#'+id).multiselect('select',multiple,true);
 }
}

function bindData2(filtdata ,valuecol, labelcol, id, multiple){ 
	 var data = filtdata;
	 var options=[];
	 for(var i=0;i<data.length;i++){
		   options.push({
			   label:data[i][valuecol],
			   title:data[i][labelcol],
			   value:data[i][valuecol]
		   });
	 }
	 $('#'+id).multiselect('dataprovider', options);
	 //the follow process have problem if have time need to fix
	 if(multiple=="all"){
		 	//全選  及び　onChange trigger
		 	$('#'+id).multiselect('selectAll',false,true);
		 	$('#'+id).multiselect('updateButtonText');				   
	 } else if(multiple=="single"){
		 	//第一の項目選択　及び　onChange trigger
		 	$('#'+id).multiselect('select',options[0].value,true);			     
	 } else {
		   	//選択設定   及び　onChange trigger
			$('#'+id).multiselect('select',multiple,true);
	 }
}