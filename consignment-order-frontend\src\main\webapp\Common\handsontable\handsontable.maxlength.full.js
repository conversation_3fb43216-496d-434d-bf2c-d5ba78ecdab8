/**
 * Handsontable max length plugin 2.0.1
 * Limit the max length of any cell of the Handsontable plugin.
 *
 * <PERSON><PERSON><PERSON><PERSON>
 * https://github.com/marcioggs
 *
 * Usage:
 * 	new Handsontable(container, {
 * 		(...)
 * 		cells: function (row, col, prop) {
 *   		this.maxLength = row + col;
 *     	}
 * 	});
 *
 */
Handsontable.hooks.add('beforeChange', function(changes, source) {
	for (i = 0; i < changes.length; i++) { //Iteration on all changed cells.
		var row = changes[i][0]; //0 is the changed cell row index.
		var col = changes[i][1]; //1 is the changed cell column index.
		var meta = this.getCellMeta(row, col);
		var data = changes[i][3]?String(changes[i][3].toString().replace(/\D/g, "")):String(changes[i][3]); //3 is the changed data index.

		if(data && typeof meta.maxLength == "number" && data.length > meta.maxLength) {
			changes[i][3] = String(parseInt(data)).substring(0, meta.maxLength); //Change the data with the collapsed text.
		}else if(data && typeof meta.maxLength == "number"){
			changes[i][3] = String(parseInt(data));
		}else if(!data){
			changes[i][3] = "";
		}
	}
});