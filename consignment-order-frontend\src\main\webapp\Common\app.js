/**
 * Model: menu
 * Page: index.html
 * Function: app.js
 */
var show = false;
var userCD = sessionStorage.getItem("userCD");
var userName=sessionStorage.getItem("userName");
var employeeCD = sessionStorage.getItem("employeeCD");
var employeeName=sessionStorage.getItem("employeeName");
(function(){
	var parentPath = ProjectPath.getPageParentDireactPath();
	/**ux style**/
	var arrStyle = function(){
		return[
		];
	}
	/**ux script**/
	var arrScript = function(){
		return[
		];
	}	
	var arrData = arrStyle();
	//ux style
	$.each(arrData,function(index,value){
		var sty = document.createElement('link');
		sty.href = value + "?version="+ ((sysversion=="") ? Math.random().toString():sysversion);
		sty.rel = "stylesheet";
		var s = document.getElementById("cssApp");
		s.parentNode.insertBefore(sty, s);
	});
	
	//ux script
	var arrSData = arrScript();
	$.each(arrSData,function(index,value){
		var bp = document.createElement('script');
	    bp.src = value + "?version="+ ((sysversion=="") ? Math.random().toString():sysversion);
	    var sd = document.getElementById("scpApp");
	    sd.parentNode.insertBefore(bp, sd);
	});
})();

/**
*画面HeadとLeft Menu分作る
*/
var leftmenu = false;
$(document).ready(function () {
	//page root direct path
	var parentPath = ProjectPath.getPageParentDireactPath();
	//page root direct name 
	var parentDireactName = ProjectPath.getPageParentDireactName();

	//add タイトル変更 zhangyunfeng bengin
	if(sessionStorage.getItem("employeeCD") != "" && sessionStorage.getItem("employeeName") != ""){
		$("title").html("center自動発注");
		$(".logo-default").text("center自動発注");
	}
	//add タイトル変更 zhangyunfeng end

	/**header html load*/
	$("#navbarheader").load(parentPath+"navbar.html",function(){
		if(sessionStorage.getItem("userCD") != "" && sessionStorage.getItem("userName") != ""){
			$("#lblLoginUserName").html(sessionStorage.getItem("userName"));
		}else if(sessionStorage.getItem("employeeCD") != "" && sessionStorage.getItem("employeeName") != ""){
				$("#lblLoginUserName").html(sessionStorage.getItem("employeeName"));
		}else{
			window.location.href="https://tralink.trial-net.co.jp/TrialLink/login.aspx";
		}
		//ログアウト
		$("#btnLLogout").on("click",function(){
			sessionStorage.setItem("userCD","");
			sessionStorage.setItem("userName","");
			window.location.href = "https://tralink.trial-net.co.jp/TrialLink/login.aspx";
		});
		$("#manualDown").on("click",function(){
			window.location = "../Page/excel_downloadExcel.action?fileName=manual_autoofer.xls";
		});
	});
	
	/**left menu html load*/
	$("#leftnavlist").load(parentPath+"sidebar.html",function(){
    	$("#subMenu").html("");
		var html = [];
		$.ajax({
			url:'../Page/menu_getSidebarMenuSelList.action',
			type:'POST',
			data:{
				login_id:userCD,
				login_employee:employeeCD
		    },
		    success:function(response){
		    	//$("#loadMask").hide();
		    	$("#subMenu").html("");
		    	if(response=="false"){
		    		showMsgBox(Message.msg022,[],function(){});
		    	}
		    	var result = eval("("+response+")");
		    	if(!result || !result[0] || result[0].result || result[0].result=="false"){
		    	}else{
		    		leftmenu = true;
		    		var html = [];
		    		html.push("<li>"+
	    					  "  <a href=\"../MenuPage/index.html\">"+
	    					  "  <i class=\"icon-home\"></i>"+
							  "  <span class=\"title\">メニュー</span></a>"+
							  "</li>");
		    		
		    		for(var i=0; i<result[0].length; i++){
		    			if(result[0][i].appNo=='1705'){
		    				show = true;
		    			}
	    				html.push("<li>"+
		    					  "  <a href=\"../"+result[0][i].appName+"/index.html\">"+
		    					  "  <i class=\""+result[0][i].className+"\"></i>"+
								  "  <span class=\"title\">"+
								  	   result[0][i].systemName+
								  "  </span></a>"+
								  "</li>");
		    		}
		    		if(!show){
		    			$("#orderCaseChk").hide();
		    		}else{
		    			$("#orderCaseChk").show();
		    		}
		    		
		    		$("#subMenu").append(html.join(" "));
		    	}
		    	var aObj = $("#leftnavlist a[href='"+parentPath+parentDireactName+"/index.html']");
		    	aObj.parent().parent().parent().addClass("open");
		    	aObj.parent().parent().css("display","block");
		    	aObj.parent().addClass("active");
		    	$("#ullihome").removeClass("active");
		    	$("[href='index.html']").parent().removeClass("active");
		    },
		    error:function(){
		    	//$("#loadMask").hide();
		    	showMsgBox(Message.msg022,[],function(){});
		    }
		});
	});
});