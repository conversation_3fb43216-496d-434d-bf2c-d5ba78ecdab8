package jp.trial.DCOrderConsignment.action;

import java.io.IOException;

import jp.trial.DCOrderConsignment.common.BaseAction;
import jp.trial.DCOrderConsignment.service.MenuMainService;
import jp.trial.DCOrderConsignment.service.impl.MenuMainServiceImpl;

public class MenuMainAction extends BaseAction{
	private static final long serialVersionUID = -6482322206006561307L;

	/** Service */
	private MenuMainService menuMainService;
	public String login_id;//ユーザーCD　ベンダーCD
	public String login_employee;//社員CD
	
	// ストラクター
	public MenuMainAction() throws IOException {
		super();
		menuMainService = new MenuMainServiceImpl();
	}
	/**
	 * Description: メニューリスト取得
	 * <AUTHOR>
	 * Date: 2017/01/05
	 */
	public void getSidebarMenuSelList() {
		String res = "";
		String userCD = login_id;
		try {
			res = menuMainService.getSidebarMenuSelList(userCD,login_employee);
			super.print.print(res);
		}catch(Exception e){
			// データをプリンター
			super.print.print("false");
		}finally {
			super.print.flush();
			super.print.close();
		}
	}

	/**
	 * Description: 推奨案情報取得:あるかとか判断
	 * <AUTHOR>
	 * Date: 2017/01/05
	 */
	public void getOrderCaseInfoData() {
		String res = "";
		String userCD = login_id;
		try {
			res=menuMainService.getOrderCaseInfoData(userCD,login_employee);
			super.print.print(res);
		}catch(Exception e){
			// データをプリンター
			super.print.print("false");
		}finally {
			super.print.flush();
			super.print.close();
		}
	}
	
	/**
	 * Description: ユーザー名取得
	 * <AUTHOR>
	 * Date: 2018/10/30
	 */
	public void getLoginInfo() {
		String res = "";
		try {
			res = menuMainService.getLoginInfo(login_id,login_employee);
			super.print.print(res);
		}catch(Exception e){
			// データをプリンター
			super.print.print("false");
		}finally {
			super.print.flush();
			super.print.close();
		}
	}
}
