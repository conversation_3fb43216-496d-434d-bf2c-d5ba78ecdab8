package jp.trial.DCOrderConsignment.common;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import jp.trial.DCOrderConsignment.common.GetHttpServletRequestWrapper;

/** Description : encode UTF-8 Filter*/
public class EncodingFilter implements Filter{
	private String encode;
	
	@Override
	public void doFilter(ServletRequest request, ServletResponse response,
			FilterChain chain) throws IOException, ServletException {
		response.setContentType("text/html;charset=" + encode);
		response.setCharacterEncoding(encode);
		request.setCharacterEncoding(encode);
		HttpServletRequest req = (HttpServletRequest)request;
		
		if (req.getMethod().equalsIgnoreCase("get")){
			req = new GetHttpServletRequestWrapper(req, encode);
		}
		chain.doFilter(req, response);
	}

	@Override
	public void init(FilterConfig config) throws ServletException {
		this.encode = config.getInitParameter("encode");
	}

	@Override
	public void destroy() {
		encode = null;

	}
}
