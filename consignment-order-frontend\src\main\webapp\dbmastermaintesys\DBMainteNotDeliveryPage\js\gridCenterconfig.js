/** DataGrid Creat*/
var TemplateGridCenter = {
		tbhot2:null,
		colWidths:[60,60,180,150,160,120,120,0.001],
		columns: [
            {data: 'addBtn',    		renderer: "html",    readOnly:true},//全
            {data: 'chkFlg',    		type: 'checkbox',    checkedTemplate: '1', uncheckedTemplate: '0'},//全
            {data: 'center',	    	type: 'text',     	 readOnly:true},//センター
            {data: 'division',			type: 'text',     	 readOnly:true},//DIV
            {data: 'notDeliveryWeekDay',
                editor: "chosen",
                chosenOptions: {
                	multiple: true,
                    data: [
                           {id:"日",label:"日"},
                           {id:"月",label:"月"},
                           {id:"火",label:"火"},
                           {id:"水",label:"水"},
                           {id:"木",label:"木"},
                           {id:"金",label:"金"},
                           {id:"土",label:"土"}
                           ]
                }
            },//納品不可曜日
            {data: 'notDeliveryBeginDate',
    	    	type: 'date',
                dateFormat: 'YYYY/MM/DD',
    	        allowEmpty: true,
    	        datePickerConfig: {
    	            firstDay: 1,
    	            showWeekNumber: true,
    	            numberOfMonths: 1,
    	            disableDayFn: function(date) {
    	        	    return new Date(date)<
    		       		    new Date(new Date(RegistryGlobal.CreateDate.substring(0,10)));
    	          },
    	          i18n: {
    			    previousMonth : 'Previous Month',
    			    nextMonth     : 'Next Month',
    			    months        : ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
    			    weekdays      : ['日曜日','月曜日','火曜日','水曜日','木曜日','金曜日','土曜日'],
    			    weekdaysShort : ['日','月','火','水','木','金','土']
    			  }
    	        }
            },//納品不可開始日
            {data: 'notDeliveryEndDate',
    	    	type: 'date',
                dateFormat: 'YYYY/MM/DD',
    	        allowEmpty: true,
    	        datePickerConfig: {
    	            firstDay: 1,
    	            showWeekNumber: true,
    	            numberOfMonths: 1,
    	            disableDayFn: function(date) {
    	        	    return new Date(date)<
    		       		    new Date(new Date(RegistryGlobal.CreateDate.substring(0,10)));
    	          },
    	          i18n: {
    			    previousMonth : 'Previous Month',
    			    nextMonth     : 'Next Month',
    			    months        : ['一月','二月','三月','四月','五月','六月','七月','八月','九月','十月','十一月','十二月'],
    			    weekdays      : ['日曜日','月曜日','火曜日','水曜日','木曜日','金曜日','土曜日'],
    			    weekdaysShort : ['日','月','火','水','木','金','土']
    			  }
    	        }
            },//納品不可終了日
            {data: 'id',	type: 'text'     }//ID
		],
		isChecked: function(){
			if(!this.tbhot2){
				return false;
			}
			if("afterChg2"==chk2){
				RegistryGlobal.tableSourceData = TemplateGridCenter.tbhot2.getSourceData();
				for (var i=0; i<RegistryGlobal.tableSourceData.length; i++) {
					if(RegistryGlobal.tableSourceData[i].chkFlg!="1"){						
						return false;
					}
				}
				return true;
			}else{
				return chk2;
			}
		},
		//create grid
		CreatHotGrid: function(hot2grid,arrData){
			//まず対象内容クリア
			if(this.tbhot2){
				this.tbhot2.destroy();
				chk2=false;
			}
			this.tbhot2 = new Handsontable(document.getElementById(hot2grid),{
				height:$(".page-content").height()-200,
				rowHeaders: false,
				colHeaders: function(col){
								switch(col){
								case 0: return '追加';
								case 1:
									var txt = '<label class="checkbox-inline"><input id="chkAll2" onclick="chkAll2();" type="checkbox"';
									txt += TemplateGridCenter.isChecked() ? 'checked="checked"' : '';
									txt += '/>全</label>';
									return txt;
								case 2: return 'センター';
								case 3: return 'DIV';
								case 4: return '納品不可曜日';
								case 5: return '納品不可開始日';
								case 6: return '納品不可終了日';
								case 7: return 'ID';
								}
							},
				colWidths: this.colWidths,
				columns: this.columns,
				overflow: scroll,
			    fillHandle:false,
			    wordWrap:false,
			    search: true,
			    minSpareRows:0,
			    columnSorting: true,
			    sortIndicator: true,
			    manualColumnResize: true,
				currentRowClassName: 'currentRow',
				cells: function (row, col, prop) {
					var cellProperties = {};
					cellProperties.renderer = styleRender2;
					return cellProperties;					
		    	},
		    	beforeChange : function(changes, source){
		    		if (source === 'loadData'||source ==='external') {
		    	        return; //don't save this change
		    	    }
		    		if (changes && changes[0].length) {
			    		for(var i=0; i<changes.length; i++){
			    			rowidx = changes[i][0];
			    			colName = changes[i][1];
			    			preValue = changes[i][2];
			    			newValue = changes[i][3];
				    		if(colName=="notDeliveryBeginDate"||colName=="notDeliveryEndDate"){
				    			if(newValue!=preValue){
					    		    TemplateGridCenter.tbhot2.setDataAtRowProp(changes[i][0], changes[i][1], dateDeal(newValue));
				    			}
				    		}
			    		}
		    		}
		    	},
		    	afterChange: function(changes, source) {
		    		afterChg(changes, source);
	 			},
	 			afterSelectionByProp: function(r, p, r2, p2) {
	 				afterSelCenter(r, p, r2, p2);
	 			}
			});	
			chk2=false;
			this.tbhot2.loadData(arrData);
		    this.tbhot2.render();
		}		
}
var chk2;
var afterChg = function(changes, source){
	if (source === 'loadData'||source ==='external') {
        return; //don't save this change
    }
	if (changes && changes[0].length) {
		for(var i=0; i<changes.length; i++){
			rowidx = changes[i][0];
			colName = changes[i][1];
			preValue = changes[i][2];
			newValue = changes[i][3];

			RegistryGlobal.tableData = TemplateGridCenter.tbhot2.getData();
			RegistryGlobal.tableSourceData = TemplateGridCenter.tbhot2.getSourceData();
			var realRow = Handsontable.hooks.run(TemplateGridCenter.tbhot2, 'modifyRow', rowidx);
			if("chkFlg"==colName){
				chk2 = "afterChg2";
				if("0"==newValue||"1"==newValue){
					RegistryGlobal.tableSourceData[realRow].chkFlg=newValue;
				}else{
					RegistryGlobal.tableSourceData[realRow].chkFlg=preValue;
				}
			}else if(preValue!=newValue && ("notDeliveryWeekDay"==colName || "notDeliveryBeginDate"==colName|| "notDeliveryEndDate"==colName)){
				RegistryGlobal.tableSourceData[realRow].chkFlg="1";
			}
			TemplateGridCenter.tbhot2.render();
		}
	}
}
var chkAll2 = function(){
	if(chk2==true){
		chk2 = false;
	}else{
		chk2 = true;
	}
	if(TemplateGridCenter.tbhot2.getSourceData().length>0){		
		$.each(RegistryGlobal.showCenterList, function(i, data){ 
				data.chkFlg = chk2?"1":"0";
		});
	}
	TemplateGridCenter.tbhot2.render();
}

var styleRender2=function(instance, td, row, col, prop, value, cellProperties){
	var realRow = Handsontable.hooks.run(TemplateGridCenter.tbhot2, 'modifyRow', row);
	var color;
	if("addBtn"==prop){//html
		td.innerHTML = value;
		td.style.textAlign = "center";
	}else if("chkFlg"==prop){//checkbox
		Handsontable.renderers.CheckboxRenderer.apply(this, arguments);
		td.style.textAlign = "center";
	}else if("notDeliveryWeekDay"==prop){//checkbox
		if(value == null){
			Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
			td.style.textAlign = "left";
			td.style.backgroundColor = "#FFFF99";
		}else{
			var selectedId;
		    var optionsList = cellProperties.chosenOptions.data;
		    if(typeof optionsList === "undefined" || typeof optionsList.length === "undefined" || !optionsList.length) {
		        Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
		        return td;
		    }
		    var values = (value + "").split(",");
		    var value = [];
		    for (var index = 0; index < optionsList.length; index++) {
		        if (values.indexOf(optionsList[index].id + "") > -1) {
		            selectedId = optionsList[index].id;
		            value.push(optionsList[index].label);
		        }
		    }
		    value = value.join(", ");
		    Handsontable.TextCell.renderer(instance, td, row, col, prop, value, cellProperties);
		    td.style.textAlign = "left";
			td.style.backgroundColor = "#FFFF99";
		    if(TemplateGridCenter.tbhot2!=null && value==""){
		    	TemplateGridCenter.tbhot2.setDataAtCell(row, col, null);
			}
		}
	}else if("notDeliveryBeginDate"==prop||"notDeliveryEndDate"==prop){//checkbox
		Handsontable.renderers.TextRenderer.apply(this, arguments);
		if(!value||isDate(convertToCorrectDate(value))){				
			$(arguments[1]).removeClass("htInvalid");
		}
		if(value && !isDate(value)){
			value = "";
		}else if( value && 
				(new Date(new Date(value))<new Date(new Date(RegistryGlobal.CreateDate.substring(0,10))))
		){
			color = "#EEEEEE";
		}
		if(!color){
			color = '#FFFF99';
		}
		td.style.backgroundColor = color;
	}else if("division"==prop){
		if(RegistryGlobal.showCenterList[realRow]&&RegistryGlobal.showCenterList[realRow].division){
			td.title = RegistryGlobal.showCenterList[realRow].division;
		}
		Handsontable.renderers.TextRenderer.apply(this, arguments);
	}else{
		Handsontable.renderers.TextRenderer.apply(this, arguments);
	}
	return td;
}

var afterSelCenter = function(r, p, r2, p2){
	if("addBtn"==p && r==r2 && p==p2){//追加
		var realRow = Handsontable.hooks.run(TemplateGridCenter.tbhot2, 'modifyRow', r);
		var rowData = TemplateGridCenter.tbhot2.getSourceDataAtRow(realRow);
		TemplateGridCenter.tbhot2.alter('insert_row', r+1,1);
		var tableSourceData= TemplateGridCenter.tbhot2.getSourceData();
		tableSourceData[r+1].addBtn=rowData.addBtn;
		tableSourceData[r+1].chkFlg=rowData.chkFlg;
		tableSourceData[r+1].center=rowData.center;
		tableSourceData[r+1].division=rowData.division;
        TemplateGridCenter.tbhot2.render();
	}
	
}